package com.caidaocloud.user.service.domain.entity;

import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.user.service.domain.repository.IUserRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Author: Max
 * @Desc: 用户领域实体类采用充血模型 跟本实例相关的方法都定义在这里，如果需要跨多个实体才能实现的业务逻辑
 * 需要定义在领域层的service目录下
 * @Date: 1/6/2021 11:18 AM
 * 4
 */
@Slf4j
@Service
@Data
public class User {
    public String tenantId;
    private String staffid;
    private Integer userid;
    private Integer corpid;
    private String account;
    private String passwd;
    private String empname;
    private String mobnum;
    private String linktel;
    private String email;
    private Integer empid;
    private Boolean issuperadmin;
    private Integer status;
    private String salt;
    private Integer crtuser;
    private Long crttime;
    private Integer upduser;
    private Long updtime;
    private Integer belongOrgId;
    private String channelId;
    private Integer mobType;
    private String passremind;
    private String deadline;
    private String logintimes;
    private Long locktime;
    private String gesture;
    private String roleids;
    private Integer orgid;
    private Integer postId;
    private String photoUrl;
    private Integer clockType;
    private String globalid;
    /**
     * 第三方平台编码
     */
    private String thirdPart;
    /**
     * 第三方数据ID
     */
    private String thirdId;
    /**
     * 预留扩展字段
     */
    private Map extMap;

    @Autowired
    private IUserRepository userRepository;

    @Autowired
    private ISessionService sessionService;

    public void save(List<User> list) {
        save(list, sessionService.getTenantId());
    }

    public void save(List<User> list, String tenantId) {
        userRepository.save(list, tenantId);
    }

    public User getByAccount(String account) {
        return userRepository.getByAccount(account, sessionService.getTenantId());
    }

    public User getByAccountTenantId(String account, String tenantId) {
        return userRepository.getByAccount(account, tenantId);
    }

    public User getByUserId(Integer userId) {
        return userRepository.getByUserId(userId, sessionService.getTenantId());
    }

    public User getByTenantIdAndStaffid(String tenantId, String staffid) {
        return userRepository.getByTenantIdAndStaffid(tenantId, staffid);
    }

    public User updateClockType(User updateUser) {
        return userRepository.updateClockType(updateUser);
    }

}
