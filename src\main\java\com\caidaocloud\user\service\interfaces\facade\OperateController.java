package com.caidaocloud.user.service.interfaces.facade;


import com.caidaocloud.dto.importdto.ImportExcelDto;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.user.service.application.service.UserExportService;
import com.caidaocloud.user.service.application.service.operate.ImportService;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoQueryDto;
import com.caidaocloud.user.service.interfaces.dto.UserImportExcelDto;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.vo.ImportExcelVo;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022/5/18
 **/
@Api(tags = "操作")
@Slf4j
@RestController
@RequestMapping("/api/user/operate")
public class OperateController {

    @Autowired
    private ImportService importService;
    @Resource
    private UserExportService userExportService;

    @ApiOperation(value = "用户导入")
    @PostMapping("/import")
    @LogRecordAnnotation(menu = "管理中心-用户中心-用户列表-员工账号管理", category = "导入", success = "导入了数据")
    public Result<ImportExcelVo> importUser(UserImportExcelDto importExcelDto) {
        return Result.ok(importService.importUser(importExcelDto.getFile(),importExcelDto.getType()));
    }

    @ApiOperation(value = "根据导入id查询进度信息")
    @GetMapping("/getImportPercentage")
    public Result<ImportExcelProcessVo> getImportPercentage(@RequestParam("processId") String processId, @RequestParam("excelCode") String excelCode) {
        return Result.ok(importService.getImportDataPercentage(processId));
    }

    @ApiOperation(value = "下载错误导入数据")
    @GetMapping("/downloadErrorImportInfo")
    public void downloadErrorExcel(HttpServletResponse response, @RequestParam(value = "processId") String processId,
                                   @RequestParam("excelCode") String excelCode) {
        importService.exportErrorExcel(response, processId);
    }

    @ApiOperation("用户导出")
    @PostMapping("export")
    public void exportUser(HttpServletResponse response, @RequestBody UserBaseInfoQueryDto queryDto) {
        userExportService.export(response, queryDto);
    }

}