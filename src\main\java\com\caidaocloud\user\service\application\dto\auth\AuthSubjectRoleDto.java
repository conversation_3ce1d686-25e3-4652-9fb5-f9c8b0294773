package com.caidaocloud.user.service.application.dto.auth;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Data
@ApiModel(value = "角色dto")
public class AuthSubjectRoleDto {

    @ApiModelProperty("用户id")
    private String subjectId;

    @ApiModelProperty("角色id")
    private List<String> roleIds;

}
