package com.caidaocloud.user.service.infrastructure.config.http;

import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

@Configuration
public class HttpConfig {
    // 秒
    private static final int DEFAULT_MAX_TIMEOUT = 5;

    @Bean
    public OkHttpClient okHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(DEFAULT_MAX_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_MAX_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_MAX_TIMEOUT, TimeUnit.SECONDS)
                .build();
    }

    @Bean
    public RestTemplate restTemplate(OkHttpClient httpClient) {
        OkHttp3ClientHttpRequestFactory httpRequestFactory =
                new OkHttp3ClientHttpRequestFactory(httpClient);
        return new RestTemplate(httpRequestFactory);
    }
}
