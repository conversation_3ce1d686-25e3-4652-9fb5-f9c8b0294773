package com.caidaocloud.user.service.application.service.operate.impl;

import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.dto.SysEmpInfoDto;
import com.caidaocloud.user.service.application.dto.masterdata.EmpInfoDto;
import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.user.service.application.exception.ImportException;
import com.caidaocloud.user.service.application.feign.IBaseInfoClient;
import com.caidaocloud.user.service.application.service.operate.AbstarctImportOperationLink;
import com.caidaocloud.user.service.application.utils.MessgeToCacheUtil;
import com.caidaocloud.user.service.domain.repository.ISysEmpInfoRepository;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Arrays;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.rmi.ServerException;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 检查员工信息
 *
 * <AUTHOR>
 * @date 2022/5/20
 **/
@Slf4j
public class ImportCheckEmpInfo extends AbstarctImportOperationLink {

    private ISysEmpInfoRepository empInfoRepository;

    public ImportCheckEmpInfo(String processId) {
        super(processId);
        this.empInfoRepository = SpringUtil.getBean(ISysEmpInfoRepository.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public void operate(UserImportDto importUser, ImportExcelProcessDto processDto, UserImportErrorDto error) {
        if(StringUtils.isNotEmpty(importUser.getWorkno())){
            try {
                val empResult = empInfoRepository.getEmpInfoByWorkno(importUser.getWorkno());
                List<EmpInfoDto> empInfoList = FastjsonUtil.convertList(empResult, EmpInfoDto.class);
                if(empInfoList.isEmpty()){
                    String msg = LangUtil.getMsg(MsgCodeConstant.IMPORT_ERROR_NOT_FOUND_EMP);
                    error.setErrorMsg(msg);
                    throw new ImportException(msg);
                }else{
                    importUser.setEmpId(empInfoList.get(0).getEmpid());
                    importUser.setCorpid(empInfoList.get(0).getCorpid());
                }
            } catch (Exception e) {
                if(e instanceof ImportException){
                    throw e;
                }else{
                    log.error("导入员工异常"+importUser.getWorkno(), e);
                    String msg = LangUtil.getMsg(MsgCodeConstant.MASTER_DATA_ERROR);
                    error.setErrorMsg(msg);
                    throw new ImportException(msg);
                }
            }
        }
        if (next() != null) {
            next().operate(importUser, processDto, error);
        }
    }

}
