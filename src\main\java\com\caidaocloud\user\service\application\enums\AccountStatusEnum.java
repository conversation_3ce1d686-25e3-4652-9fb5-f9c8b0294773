package com.caidaocloud.user.service.application.enums;

public enum AccountStatusEnum {
    NORMAL(1, "正常"),
    DEACTIVATE(2, "停用"),
    LOCK(3, "锁定");

    private Integer index;
    private String name;

    AccountStatusEnum(Integer index, String name) {
        this.name = name;
        this.index = index;
    }

    public static String getName(int index) {
        for (AccountStatusEnum c : AccountStatusEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
