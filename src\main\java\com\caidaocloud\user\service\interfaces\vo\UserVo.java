package com.caidaocloud.user.service.interfaces.vo;

import lombok.Data;

import java.util.Map;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/6/2021 11:37 AM
 * 4
 */
@Data
public class UserVo {
    public String tenantId;
    private String staffid;
    private Integer userid;
    private Integer corpid;
    private String account;
    private String empname;
    private String mobnum;
    private String linktel;
    private String email;
    private Integer empid;
    private Boolean issuperadmin;
    private Integer status;
    private Integer crtuser;
    private Long crttime;
    private Integer upduser;
    private Long updtime;
    private Integer belongOrgId;
    private String channelId;
    private Integer mobType;
    private String passremind;
    private String deadline;
    private String logintimes;
    private Long locktime;
    private String gesture;
    private String roleids;
    private Integer orgid;
    private Integer postId;
    private String photoUrl;
    private Integer clockType;
    /**
     * 全局唯一 id
     */
    private String globalid;
}
