package com.caidaocloud.user.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.tried.TriedTree;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.util.FastjsonUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class AuthTest {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private PasswordHelper passwordHelper;

    @Test
    public void test() {
        TriedTree tried = new TriedTree();
        tried.insert("api/bcc/dict/common/v1/dict/getEnableDictList".toCharArray());
        String json = FastjsonUtil.toJson(tried.getRoot());
        cacheService.cacheValue("my-test", json);
        String value = cacheService.getValue("my-test");
        TriedTree triedTree = TriedTree.toEntity(value);
        String url = "/api/bcc/dict/common/v1/dict/getEnableDictList";
        if (url.startsWith("/")) {
            url = url.substring(1);
        }
        boolean b = triedTree.find(url.toCharArray());
        System.out.println(b);
    }

    @Test
    public void test2() {
        String salt = passwordHelper.createSalt();
        System.out.println("salt=" + salt);
        String password = passwordHelper.encode("111111", salt);
        System.out.println("password=" + password);
        boolean matches = passwordHelper.matches("111111", salt, password);
        System.out.println(matches);
    }

}
