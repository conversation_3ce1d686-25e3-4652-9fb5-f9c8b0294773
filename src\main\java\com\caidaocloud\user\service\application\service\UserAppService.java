package com.caidaocloud.user.service.application.service;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.LoginPlatform;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.dto.UserAccountBaseInfoDto;
import com.caidaocloud.user.service.application.utils.AESUtils;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.*;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.ChangePasswordDto;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.user.service.interfaces.dto.UserDto;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/5/2021 4:09 PM
 * 4
 */
@Slf4j
@Service
public class UserAppService {
    /*AES秘钥*/
    @Value("${aes.secret_key:QofA168erYjs6Cwr}")
    private String AES_SECRET_KEY;

    @Autowired
    private LoginDomainService loginDomainService;

    @Autowired
    private ISessionService sessionService;

    @Autowired
    private User user;

    @Autowired
    private Tenant tenant;

    @Autowired
    private UserLoginService userLoginService;

    @Autowired
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Autowired
    private ReceiptTokenService receiptTokenService;

    @Autowired
    private PasswordHelper passwordHelper;

    @Autowired
    private TenantBaseInfoService tenantBaseInfoService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private ISmsCodeCacheService smsCodeCacheService;

    public UserAccountBaseInfoDto getUserAccountByUserId(Long userId) {
        UserAccountBaseInfoDto dto = new UserAccountBaseInfoDto();
        Optional<UserBaseInfoDo> userOptional = userBaseInfoDomainService.getByUserId(userId);
        if (!userOptional.isPresent() || null == userOptional.get().getUserId()) {
            return dto;
        }
        UserBaseInfoDo userBaseInfoData = userOptional.get();
        BeanUtil.copyProperties(userBaseInfoData, dto);
        AccountBaseInfoDo accountBaseInfoData = accountBaseInfoDomainService.getAccountByAccountId(userBaseInfoData.getAccountId());
        if (null == accountBaseInfoData || null == accountBaseInfoData.getAccountId()) {
            return dto;
        }
        BeanUtil.copyProperties(accountBaseInfoData, dto);
        return dto;
    }


    public LoginOutDto login(LoginDto loginDto) {
        String account = loginDto.getAccount();
        String[] arr = account.split("-");
        User dbUser = arr.length > 1 ? loginDomainService.getByAccountTenantId(arr[1], arr[0]) : loginDomainService.getUserByAccount(account);
        log.info("mongodb user = {}", JSON.toJSONString(dbUser));
        if (null == dbUser) {
            LoginOutDto loginOutDo = new LoginOutDto();
            loginOutDo.setErrorCode(ErrorCodes.INVALIDED_USER_OR_PASSWORD);
            return loginOutDo;
        }

        Tenant tenantDo = tenant.getTenantByCorpid(dbUser.getCorpid().longValue());
        if (null != tenantDo && StringUtil.isNotEmpty(tenantDo.getLoginConfig())) {
            loginDto.setLoginPlatform(LoginPlatform.THIRD_WEB.getValue());
            return userLoginService.autoLogin(tenantDo.getLoginConfig(), loginDto, dbUser);
        }

        LoginDo loginDo = ObjectConverter.convert(loginDto, LoginDo.class);
        return loginDomainService.login(dbUser, loginDo);
    }

    public UserDto getByUserId(Integer userId) {
        User userObj = user.getByUserId(userId);
        if (userObj != null) {
            userObj.setTenantId(sessionService.getTenantId());
        }
        return ObjectConverter.convert(userObj, UserDto.class);
    }

    public UserDto updateClockType(UserDto userDto) {
        User newUser = ObjectConverter.convert(userDto, User.class);
        User userObj = user.updateClockType(newUser);
        return ObjectConverter.convert(userObj, UserDto.class);
    }

    public UserInfo statusVerify() {
        return sessionService.getUserInfo();
    }

    public void logout() {
        sessionService.doDelete();
        sessionLogout();
    }

    private void sessionLogout() {
        try {
            if (null == WebUtil.getRequest()) {
                return;
            }

            WebUtil.getRequest().logout();
        } catch (Exception e) {
            log.error("logout err,{}", e.getMessage(), e);
        }
    }

    @Transactional
    public void changePassword(ChangePasswordDto dto) {
        PreCheck.preCheckArgument(StringUtil.isEmpty(dto.getPassword()), LangUtil.getMsg(MsgCodeConstant.PLEASE_ENTER_NEW_PASSWORD));
        PreCheck.preCheckArgument(StringUtil.isEmpty(dto.getOldPassword()), LangUtil.getMsg(MsgCodeConstant.PLEASE_ENTER_OLD_PASSWORD));
        // 旧密码校验
        UserInfo userInfo = sessionService.getUserInfo();
        PreCheck.preCheckArgument(null == userInfo || null == userInfo.getUserId(), LangUtil.getMsg(MsgCodeConstant.LOGIN_FAILURE));

        // 用户查询
        Optional<UserBaseInfoDo> optional = userBaseInfoDomainService.getByUserId(userInfo.getUserId());
        PreCheck.preCheckArgument(!optional.isPresent() || null == optional.get().getUserId(),
                LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

        // 账号查询
        UserBaseInfoDo userBaseInfoData = optional.get();
        AccountBaseInfoDo accountBaseInfoData = accountBaseInfoDomainService.getAccountByAccountId(userBaseInfoData.getAccountId());
        PreCheck.preCheckArgument(null == accountBaseInfoData || null == accountBaseInfoData.getAccountId(),
                LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

        // 旧密码校验
        boolean checkFlag = doCheckPassword(dto.isEncrypt(), dto.getOldPassword(), accountBaseInfoData);
        PreCheck.preCheckArgument(!checkFlag, LangUtil.getMsg(MsgCodeConstant.ORIGINAL_PASSWORD_ERROR));

        doChangePassword(dto.getPassword(), userInfo.getUserId(), accountBaseInfoData);
    }

    private void doChangePassword(String password,Long userId, AccountBaseInfoDo accountBaseInfoData) {
        accountBaseInfoDomainService.changePassword(password.trim(), userId, accountBaseInfoData);
        // 重置密码后移除缓存里的登录信息
        sessionService.doDelete();
    }



    public AccountBaseInfoDo doChangePassword(String password) {
        Long userId = SecurityUserUtil.getSecurityUserInfo().getUserId();

        Optional<UserBaseInfoDo> optional = userBaseInfoDomainService.getByUserId(SecurityUserUtil.getSecurityUserInfo().getUserId());
        PreCheck.preCheckArgument(!optional.isPresent() || null == optional.get().getUserId(),
                LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

        // 账号查询
        UserBaseInfoDo userBaseInfoData = optional.get();
        AccountBaseInfoDo accountBaseInfoData = accountBaseInfoDomainService.getAccountByAccountId(userBaseInfoData.getAccountId());
        PreCheck.preCheckArgument(null == accountBaseInfoData || null == accountBaseInfoData.getAccountId(),
                LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

        doChangePassword(password, userId, accountBaseInfoData);
        return accountBaseInfoData;
    }

    /**
     * 密码校验
     *
     * @param password
     * @param accountBaseInfo
     * @return
     */
    private boolean doCheckPassword(boolean encrypt, String password, AccountBaseInfoDo accountBaseInfo) {
        // 密码 AES 解密
        if (encrypt) {
            AESUtils mAESAesUtils = new AESUtils(AES_SECRET_KEY);
            try {
                password = mAESAesUtils.decryptData(password);
            } catch (Exception e) {
                log.error("UserAppService.doCheckPassword.decryptPwdData err,{}", e.getMessage(), e);
            }
        }
        // 2.0 密码校验
        if (passwordHelper.matches(password, accountBaseInfo.getSalt(), accountBaseInfo.getPassword())) {
            return true;
        }
        // 兼容1.0 密码生成规则
        String account = accountBaseInfo.getAccount();
        if (StringUtils.isNotBlank(accountBaseInfo.getAccountLoginPrefix())) {
            int startLength = accountBaseInfo.getAccountLoginPrefix().length();
            account = accountBaseInfo.getAccount().substring(startLength);
        }
        if (passwordHelper.matches(password, account + accountBaseInfo.getSalt(), accountBaseInfo.getPassword())) {
            return true;
        }
        return false;
    }



//    public AccountLoginVo grant(AccountLoginDto loginDto){
//        //根据用户名密码校验并获取租户
//        List<UserBaseInfoDo> userList = Lists.newArrayList();
//        switch (loginDto.getGrantType()){
//            case CAIDAO1_COOKIE:
//                String userId = cacheService.getValue("user-jsession-info-" + loginDto.getAccount());
//                if(StringUtils.isEmpty(userId)){
//                    throw new ServerException("凭据不合法");
//                }
//                val userOptional = userBaseInfoDomainService.getByUserId(Long.valueOf(userId));
//                if(userOptional.isPresent()){
//                    userList.add(userOptional.get());
//                }else{
//                    throw new ServerException("用户不存在");
//                }
//                break;
//            case PASSWORD_TOKEN_GRANT_TYPE:
//                val account = accountBaseInfoDomainService.getAccountInfo(loginDto.getAccount());
//                if(null == account){
//                    throw new ServerException("账号或密码错误");
//                }
//                if (!passwordHelper.matches(loginDto.getPassword(), account.getSalt(), account.getPassword())) {
//                    throw new ServerException("账号或密码错误");
//                }
//                userList = userBaseInfoDomainService.getUserListByAccount(account.getAccount());
//                break;
//            case MOBILE_CODE_TOKEN_GRANT_TYPE:
//                // 获取验证码
//                Object smsCode = smsCodeCacheService.getSmsCode(loginDto.getMobile());
//                PreCheck.preCheckArgument(smsCode == null || !smsCode.toString().equals(loginDto.getVerifyCode()), "验证码错误");
//                // 根据手机号查询账号信息
//                AccountBaseInfoDo mobileAccountInfo = accountBaseInfoDomainService.getAccountInfo(loginDto.getMobile());
//                PreCheck.preCheckArgument(null == mobileAccountInfo, "手机号错误");
//                userList = userBaseInfoDomainService.getUserListByAccount(loginDto.getMobile());
//                break;
//            default:
//                throw new ServerException("not implement");
//        }
//        if(userList.isEmpty()){
//            throw new ServerException("用户未绑定租户");
//        }
//        if(userList.size() == 1){
//            val user = userList.get(0);
//            val token = TokenGenerator.getToken(String.valueOf(user.getUserId()), String.valueOf(user.getTenantId()), loginDto.getLoginPlatform());
//            user.setExtInfo(FastjsonUtil.toJson(loginDto.getExtMap()));
//            loginDomainService.sessionRefresh(loginDto, user);
//            val accountLoginVo = new AccountLoginVo();
//            accountLoginVo.setTenantFocused(true);
//            accountLoginVo.setToken(token);
//            accountLoginVo.setUserId(user.getUserId());
//            accountLoginVo.setUserName(userList.get(0).getUserName());
//            return accountLoginVo;
//        }else{
//            List<Long> tenantIds = userList.stream().map(UserBaseInfoDo::getTenantId).distinct().collect(Collectors.toList());
//            List<TenantBaseInfoDo> tenantList = tenantBaseInfoService.getTenantList(tenantIds);
//            List<UserTenantBaseInfoVo> userTenantList = tenantList.stream().map(it ->
//                    FastjsonUtil.convertObject(it, UserTenantBaseInfoVo.class))
//                    .collect(Collectors.toList());
//            for(UserTenantBaseInfoVo userTenant : userTenantList){
//                val userOption = userList.stream().filter(user->user.getTenantId().equals(userTenant.getTenantId())).findFirst();
//                if(userOption.isPresent()){
//                    userTenant.setUserId(userOption.get().getUserId());
//                    userTenant.setUserName(userOption.get().getUserName());
//                    userTenant.setHeadPortrait(userOption.get().getHeadPortrait());
//                }
//            }
//            val receipt = UUID.randomUUID().toString();
//            val accountLoginVo = new AccountLoginVo();
//            accountLoginVo.setTenantFocused(false);
//            accountLoginVo.setTenantList(userTenantList);
//            accountLoginVo.setReceipt(receipt);
//            receiptTokenService.addReceipt(receipt, userList, loginDto);
//            return accountLoginVo;
//        }
//    }
}
