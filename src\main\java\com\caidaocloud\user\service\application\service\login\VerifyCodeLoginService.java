package com.caidaocloud.user.service.application.service.login;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.enums.SmsCodeType;
import com.caidaocloud.user.service.application.service.ISmsCodeCacheService;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.infrastructure.util.RegexUtil;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 短信验证码登录
 */
@Component
public class VerifyCodeLoginService implements IAccountLoginService {
    @NacosValue("${caidaocloud.sms.fixedVerifyCode:}")
    private String fixedVerifyCode;
    @Autowired
    private ISmsCodeCacheService smsCodeCacheService;

    @Autowired
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Resource
    private CacheService cacheService;
    
    @Override
    public GrantType getGrantType() {
        return GrantType.MOBILE_CODE_TOKEN_GRANT_TYPE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        beforeGrant(loginDto);
        String [] split = loginDto.getAccount().split("\\+");
        loginDto.setAccount(split[0]);
        // 账号查询并检查
        List<AccountBaseInfoDo> accountList = accountBaseInfoDomainService.getAndCheckAccountList(loginDto.getAccount(),
                GrantType.MOBILE_CODE_TOKEN_GRANT_TYPE);
        // 用户查询并检查
        List<Long> accountIds = accountList.stream().map(AccountBaseInfoDo::getAccountId).collect(Collectors.toList());
        List<UserBaseInfoDo> userList = userBaseInfoDomainService.getAndCheckUserList(accountIds);
        // 只匹配到一个用户
        boolean singleUser = userList.size() == 1;
        if (singleUser) {
            // 账号已停用或已锁定判断
            long normalCount = userList.stream().filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).count();
            // 账号已停用或已锁定，请联系管理员
            PreCheck.preCheckArgument(normalCount == 0, LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));
        }

        if(StringUtils.isNotEmpty(fixedVerifyCode) && fixedVerifyCode.equals(loginDto.getPassword())){
            // 如果开启了固定验证码模式，并且校验通过，则直接返回
            return userList;
        }

        // 校验验证码
        String smsCode = smsCodeCacheService.getSmsCode(loginDto.getAccount(), SmsCodeType.LOGIN);
        PreCheck.preCheckArgument(smsCode == null || !smsCode.equals(loginDto.getPassword()),
                LangUtil.getMsg(MsgCodeConstant.VERIFY_CODE_ERROR_INVALID));

        return userList;
    }

    private void beforeGrant(AccountLoginDto loginDto) {
        if (StringUtil.isEmpty(loginDto.getPassword())) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.VERIFICATION_CODE_CANNOT_EMPTY));
        }

        String [] split = loginDto.getAccount().split("\\+");
        String code = split.length > 1 ? "+" + split[1] : "";
        if (("".equals(code) || "+86".equals(code)) && !RegexUtil.isMatchMobile(split[0])) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.MOBILE_PHONE_FORMAT_ERROR));
        }
    }

    @Override
    public boolean checkIfChangePassword(Long tenantId, Long accountId) {
        String key = String.format("change_pwd_account:%s", accountId);
        boolean exist = cacheService.containsKey(key);
        if(exist)
            return true;
        return false;
    }
}
