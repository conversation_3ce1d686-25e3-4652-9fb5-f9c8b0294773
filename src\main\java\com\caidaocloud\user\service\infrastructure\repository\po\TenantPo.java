package com.caidaocloud.user.service.infrastructure.repository.po;

import lombok.Data;
import org.mongodb.morphia.annotations.*;
import org.springframework.stereotype.Component;

/**
 * @Author: Max
 * @Desc: 租户存储对象
 * @Date: 1/7/2021 8:28 PM
 * 4
 */
@Entity(value = "tenant", noClassnameStored = true)
@Indexes(@Index(fields = @Field("id")))
@Component
@Data
public class TenantPo {
    @Id
    private String id;
    private Long corpid;
    private String name;
    private String thirdPart;
    private String loginConfig;
    private String description;
    private long createdTime;
    private long updatedTime;
}
