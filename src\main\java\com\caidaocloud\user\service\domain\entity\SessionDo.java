package com.caidaocloud.user.service.domain.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.LoginPlatform;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;

@Data
public class SessionDo {
    /**
     * session key
     */
    private String sessionKey;

    /**
     * 过期时间
     */
    private long expireTime = -1;

    /**
     * 平台；WEB(0),MOBILE(1),THIRD_WEB(2),THIRD_MOBILE(3);
     */
    private Integer loginPlatform;

    /**
     * 租户
     */
    private String tenantId;

    /**
     * 用户
     */
    private Long userId;

    /**
     * 权限 code
     */
    private List<String> codeList = Lists.newArrayList();

    /**
     * 权限url
     */
    private HashSet<String> authUrlSet;

    public void addCodeList(List<String> menuList) {
        if (null != menuList && !menuList.isEmpty()) {
            codeList.addAll(menuList);
        }
    }

    public void initSession() {
        String key = "";
        long expirationTime = -1;
        if (loginPlatform == LoginPlatform.THIRD_WEB.getValue()) {
            key = String.format("t-user-session-%s:%s", StringUtils.trimToEmpty(tenantId), userId);
            expirationTime = 3600L;
        } else if (loginPlatform == LoginPlatform.THIRD_MOBILE.getValue()) {
            key = String.format("m-t-user-session-%s:%s", StringUtils.trimToEmpty(tenantId), userId);
            expirationTime = -1;
        } else if (loginPlatform == LoginPlatform.WEB.getValue()) {
            key = String.format("user-session-%s:%s", StringUtils.trimToEmpty(tenantId), userId);
            expirationTime = 3600L;
        } else if (loginPlatform == LoginPlatform.MOBILE.getValue()) {
            key = String.format("m-user-session-%s:%s", StringUtils.trimToEmpty(tenantId), userId);
            expirationTime = -1;
        } else {
            throw new ServerException("Unknown LoginPlatform = " + loginPlatform);
        }

        this.sessionKey = key;
        this.expireTime = expirationTime;
    }

}
