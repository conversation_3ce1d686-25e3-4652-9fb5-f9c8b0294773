package com.caidaocloud.user.service.domain.entity;

import com.caidaocloud.user.service.domain.repository.IAuthorizeRepository;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 第三方单点登录表
 */
@Data
@Service
public class UserSsoInfo {
    private Long id;
    // 租户ID
    private Long tenantId;
    // 租户编码
    private String tenantCode;
    // 租户APPID
    private String appid;
    // 租户密钥
    private String appsecret;
    // 租户APPID
    private String corpid;
    // 租户密钥
    private String corpsecret;
    // 租户登录类型：1企业号；2公众号；3第三方app
    private Integer ssoType;
    // 租户认证的前端URL
    private String authPageUrl;
    // 认证获取 accessToken 的 api
    //private String accessTokenApi;
    // 租户认证成功后的URL地址
    private String authSuccessUrl;
    // 租户消息推送的类型
    private Integer pushMsgType;
    // 租户的服务器Host地址
    private String serverHost;
    // 租户的首页地址
    private String homePage;
    // 认证类型：0企业微信认证；1工号认证；2公司邮箱认证；3手机号认证
    private String authType;
    // 认证的扩展配置
    //private String config;

    @Resource
    private IAuthorizeRepository authorizeRepository;

    public UserSsoInfo getSsoInfoById(Long id){
        return authorizeRepository.getById(id);
    }

    public UserSsoInfo getSsoInfoByCode(String tenantCode){
        return authorizeRepository.getByCode(tenantCode);
    }
}
