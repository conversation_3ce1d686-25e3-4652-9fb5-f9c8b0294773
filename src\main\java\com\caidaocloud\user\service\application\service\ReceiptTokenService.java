package com.caidaocloud.user.service.application.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.service.login.IAccountLoginService;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.dto.ReceiptLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.user.service.interfaces.vo.AccountLoginVo;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
public class ReceiptTokenService {

    @Autowired
    private LoginDomainService loginDomainService;
    @Autowired
    private CacheService cacheService;

    public AccountLoginVo grant(ReceiptLoginDto loginDto){
        if("gjoi4tgvr7HOLJgu".equals(loginDto.getReceipt())){
            AccountLoginDto autoLogin = new AccountLoginDto();
            autoLogin.setExtMap(Maps.map("generateReceiptForAutoLogin", true));
            autoLogin.setAccount("***********");
            autoLogin.setPassword("111111");
            IAccountLoginService.getInstance(autoLogin.getGrantType()).grant(autoLogin);
        }
        val receiptCheckResult = checkReceipt(loginDto.getReceipt());
        val userList = receiptCheckResult.getSecond();
        val userOption = userList.stream().filter(it->it.getTenantId().equals(loginDto.getTenantId())).findFirst();
        if(userOption.isPresent()){
            if(!AccountStatusEnum.NORMAL.getIndex().equals(userOption.get().getStatus())){
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.USER_LOCKED_OR_DISABLED));
            }
            val accountLogin = receiptCheckResult.getFirst();
            if(null != accountLogin.getExtMap() && accountLogin.getExtMap().containsKey("generateReceiptForAutoLogin")){
                accountLogin.getExtMap().remove("generateReceiptForAutoLogin");
            }
            val result = IAccountLoginService.grantByUserList(accountLogin, Lists.newArrayList(userOption.get()));
            IAccountLoginService.getInstance(GrantType.PASSWORD_TOKEN_GRANT_TYPE).afterGrant(receiptCheckResult.getFirst(), result, Lists.newArrayList(userOption.get()));
            return result;

        }else{
            throw new ServerException("用户不存在");
        }
    }

    private Pair<AccountLoginDto, List<UserBaseInfoDo>> checkReceipt(String receipt){
        val userDo = cacheService.getValue("login_receipt_user_" + receipt);
        val loginDo = cacheService.getValue("login_receipt_account_" + receipt);
        if(StringUtils.isEmpty(userDo) || StringUtils.isEmpty(loginDo)){
            throw new ServerException("登录凭据已过期");
        }
        return Pair.of(FastjsonUtil.toObject(loginDo, AccountLoginDto.class), FastjsonUtil.toList(userDo, UserBaseInfoDo.class));
    }

    public void addReceipt(String receipt, List<UserBaseInfoDo> userList, AccountLoginDto loginDto){
        cacheService.cacheValue("login_receipt_user_" + receipt, FastjsonUtil.toJson(userList), 2 * 60);
        cacheService.cacheValue("login_receipt_account_" + receipt, FastjsonUtil.toJson(loginDto), 2 * 60);
    }
}
