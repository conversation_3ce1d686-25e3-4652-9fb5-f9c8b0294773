package com.caidaocloud.user.service.dingding;

import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.application.cron.DingUserIdTask;
import com.caidaocloud.user.service.infrastructure.repository.mongo.MongodbDao;
import com.caidaocloud.user.service.infrastructure.repository.po.UserPo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class DingDingTest {


    @Autowired
    private DingUserIdTask dingUserIdTask;


    @Test
    public void saveTest(){

        dingUserIdTask.getUserAndUpdateDingUserId(1L, 0L, 500L);

    }


}
