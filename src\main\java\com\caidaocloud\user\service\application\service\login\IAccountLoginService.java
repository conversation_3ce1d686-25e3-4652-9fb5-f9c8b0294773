package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.user.service.application.service.ReceiptTokenService;
import com.caidaocloud.user.service.application.service.TenantBaseInfoService;
import com.caidaocloud.user.service.application.service.user.UserHandlerManger;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.user.service.interfaces.vo.AccountLoginVo;
import com.caidaocloud.user.service.interfaces.vo.UserTenantBaseInfoVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public interface IAccountLoginService {

    ConcurrentHashMap<GrantType, IAccountLoginService> grantServiceManager = new ConcurrentHashMap();
    ConcurrentHashMap<String, IAccountLoginService> ssoGrantServiceManager = new ConcurrentHashMap();
    Logger log = LoggerFactory.getLogger(IAccountLoginService.class);

    static IAccountLoginService getInstance(GrantType grantType) {
       return getInstance(grantType, null);
    }

    static IAccountLoginService getInstance(GrantType grantType, String serviceKey) {
        if (GrantType.SSO_TOKEN_GRANT_TYPE.equals(grantType)) {
            val loginService = StringUtil.isEmpty(serviceKey) ? SpringUtil.getContext().getEnvironment().getProperty("sso.login.service")
                    : serviceKey;
            val result = ssoGrantServiceManager.get(loginService);
            if (result == null) {
                throw new ServerException("not implement");
            }
            return result;
        }
        val result = grantServiceManager.get(grantType);
        if (result == null) {
            throw new ServerException("not implement");
        }
        return result;
    }

    GrantType getGrantType();

    default AccountLoginVo grant(AccountLoginDto loginDto) {
        List<UserBaseInfoDo> userList = checkAndGetUser(loginDto);
        AccountLoginVo accountLoginVo = grantByUserList(loginDto, userList);
        afterGrant(loginDto, accountLoginVo, userList);
        return accountLoginVo;
    }


    default void afterGrant(AccountLoginDto loginDto, AccountLoginVo loginVo, List<UserBaseInfoDo> userList) {
        if (StringUtil.isEmpty(loginVo.getToken()) || null == userList || userList.isEmpty()) {
            return;
        }
        UserBaseInfoDo user = userList.get(0);
        if(null != user.getTenantId()){
            // 是否强制修改密码
            loginVo.setChangePassword(checkIfChangePassword(user.getTenantId(), user.getAccountId()));
            // 密码是否过期
            loginVo.setPasswordInvalid(checkPasswordIfInvalid(user.getTenantId(), user.getAccountId()));
        }
        try {
            UserHandlerManger.getDefaultUserHandler().handler(loginDto, loginVo, user);
        } catch (Exception e) {
            log.error("权限加载失败", e);
//            throw new ServerException("权限加载失败");
        }
    }

    List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto);

    default String getSsoServiceKey() {
        return null;
    }

    @PostConstruct
    default void register() {
        val grantType = getGrantType();
        if (GrantType.SSO_TOKEN_GRANT_TYPE.equals(grantType)) {
            String serviceKey = getSsoServiceKey();
            if (StringUtils.isEmpty(serviceKey)) {
                throw new ServerException("请实现第三方登录接口");
            }
            ssoGrantServiceManager.put(serviceKey, this);
        } else {
            grantServiceManager.put(grantType, this);
        }
    }

    static AccountLoginVo grantByUserList(AccountLoginDto loginDto, List<UserBaseInfoDo> userList) {
        if (userList.isEmpty()) {
            throw new ServerException("用户未绑定租户");
        }
        AccountLoginVo accountLoginVo = new AccountLoginVo();
        if (userList.size() == 1 && !loginDto.isEnforceSecondLogin() && (null == loginDto.getExtMap() || loginDto.getExtMap().get("generateReceiptForAutoLogin") != Boolean.TRUE)) {
            val user = userList.get(0);
            val token = TokenGenerator.getToken(String.valueOf(user.getUserId()), null==user.getTenantId()?"":String.valueOf(user.getTenantId()), loginDto.getLoginPlatform());
            user.setExtInfo(FastjsonUtil.toJson(loginDto.getExtMap()));
            SpringUtil.getBean(LoginDomainService.class).sessionRefresh(loginDto, user);
            accountLoginVo.setTenantFocused(true);
            accountLoginVo.setToken(token);
            accountLoginVo.setUserId(user.getUserId());
            accountLoginVo.setUserName(user.getUserName());
            accountLoginVo.setHeadPortrait(user.getHeadPortrait());
            accountLoginVo.setEmpId(user.getEmpId());
            LogRecordContext.putVariable("tenantId", user.getTenantId());
        } else {
            loginDto.setEnforceSecondLogin(false);
            List<Long> tenantIds = userList.stream().map(UserBaseInfoDo::getTenantId).distinct().collect(Collectors.toList());
            List<TenantBaseInfoDo> tenantList = SpringUtil.getBean(TenantBaseInfoService.class).getTenantList(tenantIds);
            List<UserTenantBaseInfoVo> userTenantList = tenantList.stream().map(it ->
                            FastjsonUtil.convertObject(it, UserTenantBaseInfoVo.class))
                    .collect(Collectors.toList());
            for (UserTenantBaseInfoVo userTenant : userTenantList) {
                val userOption = userList.stream().filter(user -> user.getTenantId().equals(userTenant.getTenantId())).findFirst();
                if (userOption.isPresent()) {
                    userTenant.setUserId(userOption.get().getUserId());
                    userTenant.setUserName(userOption.get().getUserName());
                    userTenant.setHeadPortrait(userOption.get().getHeadPortrait());
                }
            }
            String receipt = UUID.randomUUID().toString();
            if(null != loginDto.getExtMap() && loginDto.getExtMap().get("generateReceiptForAutoLogin") == Boolean.TRUE){
                receipt = "gjoi4tgvr7HOLJgu";
            }
            accountLoginVo.setTenantFocused(false);
            accountLoginVo.setTenantList(userTenantList);
            accountLoginVo.setReceipt(receipt);
            SpringUtil.getBean(ReceiptTokenService.class).addReceipt(receipt, userList, loginDto);
        }

        accountLoginVo.setAccountId(userList.get(0).getAccountId());
        if("caidao-platform-admin".equals(loginDto.getAccount())){
            return accountLoginVo;
        }
        AccountBaseInfoDo accountBaseInfo = SpringUtil.getBean(AccountBaseInfoDomainService.class).getById(accountLoginVo.getAccountId());
        if (accountBaseInfo == null || accountBaseInfo.getAccountId() == null) {
            throw new ServerException("账号不存在");
        }
        accountLoginVo.setMobNum(accountBaseInfo.getMobNum());
        accountLoginVo.setEmail(accountBaseInfo.getEmail());
        accountLoginVo.setAccount(accountBaseInfo.getAccount());
        accountLoginVo.setStatus(accountBaseInfo.getStatus());
        return accountLoginVo;
    }

    default boolean checkPasswordIfInvalid(Long tenantId, Long accountId) {
        return false;
    }

    default boolean checkIfChangePassword(Long tenantId, Long accountId) {
        return false;
    }
}
