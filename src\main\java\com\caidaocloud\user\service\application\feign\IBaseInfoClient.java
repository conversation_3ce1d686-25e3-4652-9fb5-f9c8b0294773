package com.caidaocloud.user.service.application.feign;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.user.service.application.dto.SysEmpInfoDto;
import com.caidaocloud.user.service.application.feign.fallback.BaseInfoClientFallBack;
import com.caidaocloud.user.service.interfaces.dto.SimpleEmpDto;
import com.caidaocloud.user.service.interfaces.dto.UserDto;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ${caidaocloud.feign.userinfo}
 *
 * <AUTHOR>
 * @date 2021-01-20
 */
@FeignClient(value = "caidaocloud-masterdata-service", fallback = BaseInfoClientFallBack.class, configuration = FeignConfiguration.class)
public interface IBaseInfoClient {
    String API_PREFIX = "/baseinfo";

    /**
     * 用户信息及默认权限保存生成接口
     *
     * @return
     */
    @PostMapping(API_PREFIX + "/info/user/saveByTid/TENCENT")
    Result<Integer> saveBaseInfoUserSave(@RequestBody UserDto userDto);

    /**
     * 根据第三方ID查询员工信息
     *
     * @return
     */
    @GetMapping(API_PREFIX + "/info/simpleEmp/{thirdEmpId}/TENCENT")
    Result<SimpleEmpDto> getBaseInfoSimpleEmp(@PathVariable(value = "thirdEmpId") String thirdEmpId);

    /**
     * MasterData用户信息及默认权限保存生成接口
     *
     * @return
     */
    @PostMapping("/api/masterData/user/v1/saveOrUpdateUser")
    Result<UserInfo> saveMasterDataUser(@RequestBody UserDto userDto);

    /**
     * MasterData根据第三方ID查询员工信息
     *
     * @return
     */
    @GetMapping("/api/masterData/emp/v1/getSimpleEmpByTid")
    Result<SimpleEmpDto> getMasterDataEmp(@RequestParam(value = "thirdId") String thirdId, @RequestParam(value = "thirdPart") String thirdPart);

    /**
     * 获取员工信息
     *
     * @param empIds
     * @return
     */
    @GetMapping(value = "/api/masterData/emp/v1/getEmpInfoByEmpIds")
    Result<List<SysEmpInfoDto>> getEmpInfoByEmpIds(@RequestParam("empIds") String empIds);

    /**
     * 根据工号获取员工信息
     *
     * @param worknoList
     * @return
     */
    @GetMapping(value = "/api/masterData/emp/v1/getEmpInfoByWorkNos")
    Result<List<SysEmpInfoDto>> getEmpInfoByWorkno(@RequestParam("worknos") String workno);

}
