package com.caidaocloud.user.service.domain.repository;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.infrastructure.repository.po.UserBaseInfo;
import com.caidaocloud.util.FastjsonUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR>
 * @date 2023/5/5
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = Application.class)
public class IUserBaseInfoRepositoryPgsqlTests {

	private List<String> userIds = new ArrayList<>();

	@Autowired
	private IUserBaseInfoRepository userBaseInfoRepository;

	@SneakyThrows
	@Before
	public void bf(){
		InputStream stream = new ClassPathResource("userBaseInfoIds.json").getInputStream();
		String userJson = IOUtils.toString(stream, "UTF-8");
		userIds = FastjsonUtil.toList(userJson, String.class);

		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);
	}

	@Test
	public void saveUser(){
		List<UserBaseInfoDo> baseInfos = Sequences.sequence(userIds).map(userId -> {
			UserBaseInfoDo baseInfo = new UserBaseInfoDo();
			baseInfo.setUserId(Long.valueOf(userId));
			baseInfo.setAccountId(Long.valueOf(userId));
			baseInfo.setTenantId(Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()));
			baseInfo.setStatus(1);
			baseInfo.setUpdateBy(0L);
			baseInfo.setUpdateTime(System.currentTimeMillis());
			baseInfo.setCreateBy(baseInfo.getUpdateBy());
			baseInfo.setCreateTime(baseInfo.getUpdateTime());
			baseInfo.setDeleted(0);
			return baseInfo;
		}).toList();

		userBaseInfoRepository.insertBatch(baseInfos);
	}


	@Test
	public void page(){
		userBaseInfoRepository.page(null, 1, 10, true);
	}
}