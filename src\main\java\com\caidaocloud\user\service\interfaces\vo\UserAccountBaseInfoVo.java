package com.caidaocloud.user.service.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户和账号基本信息Vo")
public class UserAccountBaseInfoVo {
    @ApiModelProperty("用户统一注册账号")
    private String account;
    @ApiModelProperty("注册手机号")
    private String mobNum;
    @ApiModelProperty("注册邮箱")
    private String email;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("用户姓名")
    private String userName;
    @ApiModelProperty("员工ID")
    private Long empId;
}
