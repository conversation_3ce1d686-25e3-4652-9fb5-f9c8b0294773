package com.caidaocloud.user.service.application.utils;

import org.apache.commons.compress.utils.Charsets;

import javax.annotation.Nullable;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * created by: FoAng
 * create time: 5/9/2022 5:19 下午
 */
public class SHADigestUtil {

    private static final char[] HEX_DIGITS = "0123456789abcdef".toCharArray();

    public static String md5Hex(final String data) {
        return MD5DigestUtil.md5DigestAsHex(data.getBytes(Charsets.UTF_8));
    }

    public static String md5Hex(final byte[] bytes) {
        return MD5DigestUtil.md5DigestAsHex(bytes);
    }

    public static String sha1(String srcStr) {
        return hash("SHA-1", srcStr);
    }

    public static String sha256(String srcStr) {
        return hash("SHA-256", srcStr);
    }

    public static String sha384(String srcStr) {
        return hash("SHA-384", srcStr);
    }

    public static String sha512(String srcStr) {
        return hash("SHA-512", srcStr);
    }

    public static String hash(String algorithm, String srcStr) {
        try {
            MessageDigest md = MessageDigest.getInstance(algorithm);
            byte[] bytes = md.digest(srcStr.getBytes(Charsets.UTF_8));
            return toHex(bytes);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String toHex(byte[] bytes) {
        StringBuilder ret = new StringBuilder(bytes.length * 2);

        for(int i = 0; i < bytes.length; ++i) {
            ret.append(HEX_DIGITS[bytes[i] >> 4 & 15]);
            ret.append(HEX_DIGITS[bytes[i] & 15]);
        }

        return ret.toString();
    }

    public static boolean slowEquals(@Nullable String a, @Nullable String b) {
        return a != null && b != null && slowEquals(a.getBytes(Charsets.UTF_8), b.getBytes(Charsets.UTF_8));
    }

    public static boolean slowEquals(@Nullable byte[] a, @Nullable byte[] b) {
        if (a != null && b != null) {
            if (a.length != b.length) {
                return false;
            } else {
                int diff = a.length ^ b.length;

                for(int i = 0; i < a.length && i < b.length; ++i) {
                    diff |= a[i] ^ b[i];
                }

                return diff == 0;
            }
        } else {
            return false;
        }
    }

    public static String encrypt(String data) {
        return sha1(md5Hex(data));
    }
}
