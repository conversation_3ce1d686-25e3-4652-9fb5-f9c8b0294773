package com.caidaocloud.user.service.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("候选人信息")
public class SyncOnBoarDingDto {
    // 账号信息
    @ApiModelProperty("账号ID")
    private Long accountId;
    @ApiModelProperty("用户统一注册账号")
    private String account;
    @ApiModelProperty("注册手机号")
    private String mobNum;
    // 用户信息
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("用户姓名")
    private String userName;
    @ApiModelProperty("员工ID")
    private Long empId;
}
