package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.utils.AESUtils;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.AccountPayslipInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.AccountPayslipInfoDomainService;
import com.caidaocloud.user.service.domain.service.PwdRuleDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.user.service.interfaces.vo.AccountLoginVo;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 账号密码登录
 */
@Slf4j
@Component
public class GesturePayslipLoginService implements IAccountLoginService {

    /*AES秘钥*/
    @Value("${aes.secret_key:QofA168erYjs6Cwr}")
    private String AES_SECRET_KEY;

    @Autowired
    private AccountPayslipInfoDomainService accountPayslipInfoDomainService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Autowired
    private PwdRuleDomainService pwdRuleDomainService;

    @Autowired
    private PasswordHelper passwordHelper;

    @Autowired
    private CacheService cacheService;

    @Override
    public GrantType getGrantType() {
        return GrantType.GESTURE_PAYSLIP_GRANT_TYPE;
    }

    @Override
    public AccountLoginVo grant(AccountLoginDto loginDto) {
        AccountLoginVo accountLoginVo = new AccountLoginVo();
        List<UserBaseInfoDo> userList = checkAndGetUser(loginDto);
        if(CollectionUtils.isNotEmpty(userList)){
            accountLoginVo.setAccountId(userList.get(0).getAccountId());
            for (UserBaseInfoDo userBaseInfo : userList) {
                val exist = cacheService.containsKey("user_payslip_verify_code_session_" + userBaseInfo.getUserId());
                if (!exist) {
                    cacheService.cacheValue("user_payslip_verify_code_session_" + userBaseInfo.getUserId(), "1", 10 * 60);
                    break;
                }
            }

        }
        return accountLoginVo;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        beforeGrant(loginDto);

        List<UserBaseInfoDo> userList = null;
        List<AccountPayslipInfoDo> accountList = null;

        if(CollectionUtils.isEmpty(userList)){
            // 账号查询并检查
            accountList = accountPayslipInfoDomainService.getAndCheckAccountList(loginDto.getAccount(), GrantType.PASSWORD_PAYSLIP_GRANT_TYPE);

            // 用户查询并检查
            List<Long> accountIds = accountList.stream().map(AccountPayslipInfoDo::getAccountId).collect(Collectors.toList());
            userList = userBaseInfoDomainService.getAndCheckUserList(accountIds);
        }
        // 只匹配到一个用户
        boolean singleUser = userList.size() == 1;
        if (singleUser) {
            // 账号已停用或已锁定判断
            long normalCount = userList.stream().filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).count();
            // 账号已停用或已锁定，请联系管理员
            PreCheck.preCheckArgument(normalCount == 0, LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));
        }

        // 密码检查，匹配到多个账号时，只要密码和任意账号匹配则认为登录成功
        boolean checkPwdSuccess = false;
        for (AccountPayslipInfoDo account : accountList) {
            if (doCheckGesture(loginDto.getPassword(), account)) {
                checkPwdSuccess = true;
                break;
            }
        }

        if (!checkPwdSuccess) {
            // 密码错误
            // 当账号只绑定一个租户时需校验用户状态，需检查账号是否锁定（使用登录时密码错误超过次数限制将被锁定规则）
            if (singleUser) {
                pwdRuleDomainService.checkUserIsLockAfterLoginFail(userList.get(0));
            }
            // 账号或密码错误
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.WRONG_ACCOUNT_OR_PASSWORD));
        }
        return userList;
    }

    /**
     * 密码校验
     * @param gesture
     * @param accountBaseInfo
     * @return
     */
    private boolean doCheckGesture(String gesture, AccountPayslipInfoDo accountBaseInfo) {
        if (passwordHelper.matches(gesture, accountBaseInfo.getSalt(), accountBaseInfo.getGesture())) {
            pwdRuleDomainService.cleanErrorCountCache(accountBaseInfo.getAccountId());
            return true;
        }
        return false;
    }

    private void beforeGrant(AccountLoginDto loginDto) {
        if (StringUtils.isEmpty(loginDto.getAccount()) || StringUtils.isEmpty(loginDto.getPassword())) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.USERNAME_OR_PWD_EMPTY));
        }

        // 账号、密码 AES 解密
        if (loginDto.isEncrypt()) {
            String account = loginDto.getAccount();
            String password = loginDto.getPassword();
            AESUtils mAESAesUtils = new AESUtils(AES_SECRET_KEY);
            try {
                account = mAESAesUtils.decryptData(account);
                password = mAESAesUtils.decryptData(password);

                loginDto.setAccount(account);
                loginDto.setPassword(password);
            } catch (Exception e) {
                log.error("GesturePayslipLoginService.beforeGrant err,{}", e.getMessage(), e);
            }
        }
    }

    @Override
    public boolean checkPasswordIfInvalid(Long tenantId, Long accountId) {
        return false;
    }

    @Override
    public boolean checkIfChangePassword(Long tenantId, Long accountId) {
        return false;
    }
}
