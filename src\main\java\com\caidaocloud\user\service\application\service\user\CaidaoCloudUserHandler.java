package com.caidaocloud.user.service.application.service.user;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.feign.IAuthFeign;
import com.caidaocloud.user.service.application.service.user.annotation.UserHandlerType;
import com.caidaocloud.user.service.domain.entity.SessionDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.vo.AccountLoginVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;

/**
 * 2.0 权限处理器
 *
 * <AUTHOR>
 * @date 2022/5/16
 **/
@Slf4j
@UserHandlerType(PermissionType.CAIDAOCLOUD)
@Service
public class CaidaoCloudUserHandler implements IUserHandler {

    @Resource
    private LoginDomainService loginDomainService;

    @Autowired
    private IAuthFeign authFeign;

    @Override
    public void handler(AccountLoginDto loginDto, AccountLoginVo loginVo, UserBaseInfoDo user) {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(null==user.getTenantId()?null:user.getTenantId().toString());
        userInfo.setUserId(user.getUserId());
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try{
            Result<List<String>> result = authFeign.getResourceUrlListBySubjectId(loginVo.getUserId(), loginVo.getToken());
            if (result == null || !result.isSuccess()) {
                throw new ServerException("request auth server fail");
            }
            if(log.isDebugEnabled()) {
                var logStr = String.format("%s refresh resource url list: %s", loginVo.getUserId(), FastjsonUtil.toJson(result.getData()));
                log.debug(logStr);
            }
            List<String> urlList = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(result.getData())) {
                urlList = result.getData();
            }
            HashSet<String> urlSet = Sets.newHashSet();
            for (String url : urlList) {
                if (StringUtils.isBlank(url)) {
                    continue;
                }
                urlSet.add(url);
            }

            SessionDo sessionData = new SessionDo();
            sessionData.setLoginPlatform(loginDto.getLoginPlatform());
            sessionData.setUserId(loginVo.getUserId());
            String tenantId = null==user.getTenantId()?null:user.getTenantId().toString();
            sessionData.setTenantId(tenantId);
            sessionData.setAuthUrlSet(urlSet);
            loginDomainService.sessionRefresh(sessionData);
        }finally{
            SecurityUserUtil.removeSecurityUserInfo();
        }

    }

}
