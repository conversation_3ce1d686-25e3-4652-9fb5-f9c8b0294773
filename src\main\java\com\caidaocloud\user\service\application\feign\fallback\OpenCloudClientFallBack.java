package com.caidaocloud.user.service.application.feign.fallback;

import com.caidaocloud.user.service.application.feign.IOpenCloudClient;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OpenCloudClientFallBack implements IOpenCloudClient {

    @Override
    public Result<List<String>> getAllMenu(String accessToken) {
        return Result.ok(Lists.newArrayList());
    }
}
