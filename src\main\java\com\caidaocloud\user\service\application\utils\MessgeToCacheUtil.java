package com.caidaocloud.user.service.application.utils;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date  2022/5/19
 **/
public class MessgeToCacheUtil {

    private static CacheService cacheService;

    private static CacheService getCacheService() {
        if (cacheService == null) {
            synchronized (MessgeToCacheUtil.class) {
                if (cacheService == null) {
                    cacheService= SpringUtil.getBean(CacheService.class);
                }
            }
        }
        return cacheService;
    }

    public static void setMessageToCache(String key, String value, long time) {
        setMessageToCache(key, Lists.newArrayList(value), time);
    }

    public static void setMessageToCache(String key, List<String> valueList, long time) {
        getCacheService().cacheList(key, valueList, time);
    }

}
