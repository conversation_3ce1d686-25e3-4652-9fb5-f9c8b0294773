package com.caidaocloud.user.service.domain.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.dto.PwdCreateDto;
import com.caidaocloud.user.service.application.dto.PwdUpdateDto;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.utils.CacheKeyDefine;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.*;
import com.caidaocloud.user.service.domain.util.PassGenerateHelp;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.util.DateUtil;
import com.caidaocloud.util.WebUtil;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

@Slf4j
@Service
public class PwdRuleDomainService {
    @Autowired
    private PwdRuleDo pwdRuleDo;
    @Autowired
    private AccountBaseInfoDo accountBaseInfoDo;
    @Autowired
    private HisUserPwdRecDo hisUserPwdRecDo;
    @Autowired
    private PasswordHelper passwordHelper;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private UserBaseInfoDo userBaseInfoDo;

    public void syncSave(List<PwdRuleDo> dataList) throws Exception {
        pwdRuleDo.syncSave(dataList);
    }

    /**
     * 根据初始、重置密码规则设定，创建密码
     *
     * @param dto
     * @return
     */
    public String createPwd(PwdCreateDto dto) {
        // 查询密码规则
        PwdRuleDo mRule = pwdRuleDo.getByTenantId(dto.getTenantId());

        // 默认生成规则
        if (mRule == null || !mRule.getIsFirstRule() || StringUtil.isNullOrEmpty(mRule.getFirstRule())) {
            // 创建默认密码
            return PassGenerateHelp.generatePasswordByRule(dto.getDefaultFirstRule(), dto.getDefaultFirstLength(), dto.getMobile(), dto.getCardNo());
        }

        // 根据规则创建密码
        int ruleType = 0;
        String[] firstRuleList = mRule.getFirstRule().split(",");
        for (String item : firstRuleList) {
            int value = Integer.parseInt(item);
            if (value == 1) {
                ruleType |= PassGenerateHelp.TYPE_NUMBER;
            } else if (value == 2) {
                ruleType |= PassGenerateHelp.TYPE_UPPER;
            } else if (value == 3) {
                ruleType |= PassGenerateHelp.TYPE_LOWER;
            } else if (value == 4) {
                ruleType |= PassGenerateHelp.TYPE_OTHER;
            } else if (value == 5) {
                ruleType |= PassGenerateHelp.TYPE_MOBILE;
            } else if (value == 6) {
                ruleType |= PassGenerateHelp.TYPE_CARD;
            }
        }
        ruleType = ruleType == 0 ? dto.getDefaultFirstRule() : ruleType;
        int ruleLength = mRule.getFirstLength() != null ? mRule.getFirstLength() : dto.getDefaultFirstLength();
        return PassGenerateHelp.generatePasswordByRule(ruleType, ruleLength, dto.getMobile(), dto.getCardNo());
    }

    /**
     * 根据修改密码规则设定，进行新密码校验
     *
     * @param dto
     */
    public void checkPwdBeforeUpdatePwd(PwdUpdateDto dto) {
        // 查询密码规则
        PwdRuleDo pwdrule = pwdRuleDo.getByTenantId(dto.getTenantId());
        if (pwdrule == null || !pwdrule.getIsValid()) {
            // 未启用修改密码规则
            return;
        }
        String newPasswd = dto.getNewPasswd();
        // 判断密码长度
        if (pwdrule.getPwdLen1() != null && pwdrule.getPwdLen2() != null) {
            if (!(newPasswd.length() >= pwdrule.getPwdLen1() && newPasswd.length() <= pwdrule.getPwdLen2())) {
                throw new ServerException(MessageFormat.format("密码长度应为{0}~{1}个字符", pwdrule.getPwdLen1(),
                        pwdrule.getPwdLen2()));
            }
        }
        // 判断密码复杂度 1.包含数字、2.大写字母、3.小写字母 4.特殊字符
        if (StringUtils.isNotBlank(pwdrule.getPwdComplexity())) {
            StringBuffer sub = new StringBuffer();
            if (pwdrule.getPwdComplexity().contains("1")) {
                if (!Pattern.compile("\\d+").matcher(newPasswd).find()) {
                    sub.append("数字");
                }
            }
            if (pwdrule.getPwdComplexity().contains("2")) {
                if (!Pattern.compile("[A-Z]+").matcher(newPasswd).find()) {
                    if (sub.toString().length() > 0) sub.append(",");
                    sub.append("大写字母");
                }
            }
            if (pwdrule.getPwdComplexity().contains("3")) {
                if (!Pattern.compile("[a-z]+").matcher(newPasswd).find()) {
                    if (sub.toString().length() > 0) sub.append(",");
                    sub.append("小写字母");
                }
            }
            if (pwdrule.getPwdComplexity().contains("4")) {
                if (!Pattern.compile("\\W+").matcher(newPasswd).find()) {
                    if (sub.toString().length() > 0) sub.append(",");
                    sub.append("特殊字符");
                }
            }
            if (sub.toString().length() > 0) {
                throw new ServerException(MessageFormat.format("新密码中必须包含:{0}", sub.toString()));
            }
        }
        // 判断不可与前多少次历史密码相同
        if (pwdrule.getNotPwdSameNum() != null) {
            List<HisUserPwdRecDo> list = hisUserPwdRecDo.getPageListByAccountId(dto.getTenantId(), dto.getAccountId(), 1, pwdrule.getNotPwdSameNum());
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            // 密码验证
            for (HisUserPwdRecDo hisPwd : list) {
                PreCheck.preCheckArgument(passwordHelper.matches(newPasswd, hisPwd.getSalt(), hisPwd.getPassword()),
                        MessageFormat.format("新密码不能和近{0}次密码相同", pwdrule.getNotPwdSameNum()));
                // 兼容1.0 密码生成规则
                String account = dto.getAccount();
                if (StringUtils.isNotBlank(dto.getAccountLoginPrefix())) {
                    int startLength = dto.getAccountLoginPrefix().length();
                    account = dto.getAccount().substring(startLength);
                }
                PreCheck.preCheckArgument(passwordHelper.matches(newPasswd, account + hisPwd.getSalt(), hisPwd.getPassword()),
                        MessageFormat.format("新密码不能和近{0}次密码相同", pwdrule.getNotPwdSameNum()));
            }
        }
    }

    /**
     * 登录时，检查账号是否锁定（使用登录时密码错误超过次数限制将被锁定规则）
     *
     * @param user
     * @return
     */
    public void checkUserIsLockAfterLoginFail(UserBaseInfoDo user) {
        if (AccountStatusEnum.LOCK.getIndex().equals(user.getStatus())) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));
        }
        // 查询密码规则
        PwdRuleDo pwdrule = pwdRuleDo.getByTenantId(user.getTenantId());
        if (pwdrule == null || pwdrule.getLockAccountNum() == null || pwdrule.getLockAccountNum() <= 0) {
            // 未启用登录时密码错误超过次数限制将被锁定规则
            return;
        }
        int count = 1;
        String key = CacheKeyDefine.LOGIN_PWD_ERROR_ATTEMPT_COUNT + user.getAccountId();
        if (cacheService.containsKey(key) && cacheService.getValue(key) != null && StringUtils.isNotEmpty(cacheService.getValue(key))) {
            count = Integer.parseInt(cacheService.getValue(key));
            count += 1;
        }
        val lockAccountNum = Objects.isNull(pwdrule.getLockAccountNum()) ? 5 : pwdrule.getLockAccountNum().intValue();
        if (count >= lockAccountNum) {
            // 用户锁定
            user.setStatus(AccountStatusEnum.LOCK.getIndex());
            userBaseInfoDo.updateById(user);
            throw new ServerException("账号已被锁定，请联系管理员处理～");
        }
        int lockAccountTime = (Objects.isNull(pwdrule.getLockAccountTime()) ? 10 : pwdrule.getLockAccountTime()) * 60;
        cacheService.cacheValue(key, String.valueOf(count), Long.valueOf(lockAccountTime));
        if (count <= 2) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.password.initial.tip"));
        } else if (count > 2 && count < lockAccountNum) {
            int remainingAttempts = lockAccountNum - count;
            String format = MessageHandler.getMessage("caidao.exception.password.secondary.tip", WebUtil.getRequest());
            throw new ServerException(String.format(format, count, remainingAttempts));
        } else {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.password.lastest.tip"));
        }
    }

    /**
     * 清除本地错误次数限制
     *
     * @param accountId
     */
    public void cleanErrorCountCache(Long accountId) {
        cacheService.remove(CacheKeyDefine.LOGIN_PWD_ERROR_ATTEMPT_COUNT + accountId);
    }

    /**
     * 登录时，检查账号密码是否过期
     *
     * @param tenantId
     * @param accountId
     */
    public boolean checkPasswordIfInvalid(Long tenantId, Long accountId) {
        // 查询密码规则
        PwdRuleDo pwdrule = pwdRuleDo.getByTenantId(tenantId);
        if (pwdrule == null || !pwdrule.getIsValid() || pwdrule.getPwdValidTime() == null || pwdrule.getPwdValidTime() <= 0) {
            return false;
        }
        List<HisUserPwdRecDo> hisList = hisUserPwdRecDo.getListByAccountId(tenantId, accountId);
        // 检查密码是否过期
        if (CollectionUtils.isEmpty(hisList)) {
            hisList.sort(Comparator.comparing(AbstractBaseEntity::getCreateTime).reversed());
            long second = DateUtil.getCurrentTimestamp() - hisList.get(0).getCreateTime();
            int day = (int) second / (24 * 60 * 60);
            // 密码已过期，请重置密码！
            return day > pwdrule.getPwdValidTime();
        } else {
            AccountBaseInfoDo accountBaseInfo = accountBaseInfoDo.getById(accountId);
            PreCheck.preCheckArgument(null == accountBaseInfo, "账号或密码错误");
            long second = DateUtil.getCurrentTimestamp() - accountBaseInfo.getCreateTime();
            int day = (int) second / (24 * 60 * 60);
            // 密码已过期，请重置密码！
            return day > pwdrule.getPwdValidTime();
        }
    }

    /**
     * 登录时，检查是否强制修改密码
     *
     * @param tenantId
     * @param accountId
     */
    public boolean checkIfChangePassword(Long tenantId, Long accountId) {
        // 查询密码规则
        PwdRuleDo pwdrule = pwdRuleDo.getByTenantId(tenantId);
        if (pwdrule == null || !pwdrule.getIsValid() || pwdrule.getIsPwdChange() == null || !pwdrule.getIsPwdChange()) {
            return false;
        }
        List<HisUserPwdRecDo> hisList = hisUserPwdRecDo.getListByAccountId(tenantId, accountId);
        // 检查是否强制修改密码
        if (CollectionUtils.isEmpty(hisList)) {
            // 初始密码登录，请重置密码！
            return true;
        }
        return false;
    }
}
