package com.caidaocloud.user.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.user.service.domain.entity.PwdRuleDo;
import com.caidaocloud.user.service.domain.repository.IPwdRuleRepository;
import com.caidaocloud.user.service.infrastructure.repository.mapper.PwdRuleMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.PwdRule;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class PwdRuleRepositoryImpl implements IPwdRuleRepository {
    @Autowired
    private PwdRuleMapper pwdRuleMapper;

    @Override
    public void insertBatch(List<PwdRuleDo> dataList) {
        pwdRuleMapper.insertBatch(ObjectConverter.convertList(dataList, PwdRule.class));
    }

    @Override
    public void insert(PwdRuleDo data) {
        pwdRuleMapper.insert(ObjectConverter.convert(data, PwdRule.class));
    }

    @Override
    public void update(PwdRuleDo data) {
        pwdRuleMapper.updateById(ObjectConverter.convert(data, PwdRule.class));
    }

    @Override
    public int insertSelective(PwdRuleDo record) {
        return pwdRuleMapper.insertSelective(ObjectConverter.convert(record, PwdRule.class));
    }

    @Override
    public int updateByPrimaryKeySelective(PwdRuleDo record) {
        return pwdRuleMapper.updateByPrimaryKeySelective(ObjectConverter.convert(record, PwdRule.class));
    }

    @Override
    public void delete(List<Long> pwdRuleIds) {
        pwdRuleMapper.deleteBatchIds(pwdRuleIds);
    }

    @Override
    public void deleteByTenantIds(List<Long> tenantIds) {
        LambdaQueryWrapper<PwdRule> queryWrapper = new QueryWrapper<PwdRule>().lambda();
        queryWrapper.in(PwdRule::getTenantId, tenantIds);
        pwdRuleMapper.delete(queryWrapper);
    }

    @Override
    public List<PwdRuleDo> getPwdRuleListByTenantIds(List<Long> tenantIds) {
        LambdaQueryWrapper<PwdRule> mQueryInfo = new QueryWrapper<PwdRule>().lambda();
        mQueryInfo.in(PwdRule::getTenantId, tenantIds);
        mQueryInfo.ne(PwdRule::getDeleted, 1);
        List<PwdRule> list = pwdRuleMapper.selectList(mQueryInfo);
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : ObjectConverter.convertList(list, PwdRuleDo.class);
    }
}
