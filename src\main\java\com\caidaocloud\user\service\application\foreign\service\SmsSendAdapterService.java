package com.caidaocloud.user.service.application.foreign.service;

import com.caidaocloud.user.service.application.enums.SmsCodeType;
import com.caidaocloud.user.service.application.event.dto.SmsSendMessageDto;
import com.caidaocloud.user.service.application.event.publish.SmsSendPublish;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 短信发送-适配服务
 *
 * <AUTHOR>
 * @Date 2022/9/5
 */
@Slf4j
@Service
public class SmsSendAdapterService implements IForeignSmsService {
    @Override
    public String getSmsType() {
        return "adapter";
    }

    @Resource
    private SmsSendPublish smsSendPublish;

    @Value("${caidaocloud.smsTemplate:您的登录验证码为 #code ，该验证码5分钟内有效，请勿泄露于他人。}")
    private String smsTemplate;

    @Override
    public boolean sendMessageCode(String mobile, String smsCode, SmsCodeType smsCodeType) {
        SmsSendMessageDto messageDto = new SmsSendMessageDto();
        messageDto.setSmsCodeType(null != smsCodeType ? smsCodeType.toString() : "");
        messageDto.setSmsCode(smsCode);
        messageDto.setPhoneNumbers(Lists.newArrayList(mobile));
        messageDto.setContent(smsTemplate.replaceAll("#code", smsCode));
        smsSendPublish.publishMsg(messageDto);
        return true;
    }

    @Override
    public boolean sendMessageCode(String mobile, String code, String smsCode, SmsCodeType smsCodeType) {
        return sendMessageCode(mobile, smsCode, smsCodeType);
    }
}
