package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.util.WebUtil;
import com.google.common.collect.Lists;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

@Component
public class Caidao1LoginService implements IAccountLoginService {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Override
    public GrantType getGrantType() {
        return GrantType.CAIDAO1_COOKIE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        Arrays.stream(WebUtil.getRequest().getCookies())
                .filter(it -> "JSESSIONID".equals(it.getName())).findFirst().ifPresent(it ->
                        loginDto.setAccount(it.getValue())
                );
        beforeGrant(loginDto);
        val jsessionId = loginDto.getAccount();
        val redisKey = "user-jsession-info-" + jsessionId;
        if (!cacheService.containsKey(redisKey)) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ILLEGAL_CREDENTIALS));
        }
        String sessionInfo = new String(cacheService.getByte(redisKey), StandardCharsets.UTF_8);
        if (StringUtils.isEmpty(sessionInfo)) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ILLEGAL_CREDENTIALS));
        }
        val userId = sessionInfo.split("-")[1];
        val userOptional = userBaseInfoDomainService.getByUserId(Long.valueOf(userId));
        if (userOptional.isPresent()) {
            return Lists.newArrayList(userOptional.get());
        } else {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));
        }
    }

    private void beforeGrant(AccountLoginDto loginDto) {
        if (StringUtils.isEmpty(loginDto.getAccount())) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.LOGIN_CREDENTIALS_EMPTY));
        }
    }
}
