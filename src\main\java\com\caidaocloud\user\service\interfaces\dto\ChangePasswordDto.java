package com.caidaocloud.user.service.interfaces.dto;

import com.caidaocloud.user.service.interfaces.granter.GrantType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("密码修改Dto")
public class ChangePasswordDto {
    @ApiModelProperty("原密码")
    private String oldPassword;

    @ApiModelProperty("新密码")
    private String password;
    
    @ApiModelProperty(value = "是否AES 解密，默认 false")
    private boolean encrypt;

    @ApiModelProperty(value = "操作token")
    private String token;

    @ApiModelProperty("手势密码")
    private String gesture;
}
