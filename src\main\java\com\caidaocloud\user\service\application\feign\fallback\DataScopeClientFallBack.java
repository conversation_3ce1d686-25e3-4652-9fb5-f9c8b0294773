package com.caidaocloud.user.service.application.feign.fallback;

import com.caidaocloud.user.service.application.feign.IDataScopeClient;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class DataScopeClientFallBack implements IDataScopeClient {

    @Override
    public Result datascopeLoad(String token) {
        return Result.fail();
    }

    @Override
    public Result getCodeList(String token) {
        return Result.fail();
    }
}
