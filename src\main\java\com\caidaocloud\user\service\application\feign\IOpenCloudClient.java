package com.caidaocloud.user.service.application.feign;

import com.caidaocloud.security.session.SessionUtil;
import com.caidaocloud.user.service.application.feign.fallback.OpenCloudClientFallBack;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(value = "OpenCloud", url = "${caidaocloud.user.menu.url:}", fallback = OpenCloudClientFallBack.class)
public interface IOpenCloudClient {
    @GetMapping("/caidao2/menu/v1/all")
    Result<List<String>> getAllMenu(@RequestHeader(SessionUtil.ACCESS_TOKEN) String accessToken);
}
