package com.caidaocloud.user.service;

import com.caidaocloud.security.dto.TokenDto;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.session.SessionUtil;
import com.caidaocloud.security.token.TokenVerify;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class TokenTest {

    @Autowired
    private ISessionService sessionRepository;

    @Test
    public void test() {
        String accessToken = "eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnRJZCI6IjMzIiwidGltZSI6MTY1MzMwNjIzOTEwNSwidHlwZSI6MCwidXNlcklkIjoiMTUyODY4Mjk1NzE4NTc1NzE4NiJ9.nlPUgfcriDCfB0R97V4qJdbPtWOuNMYEY1DnjXCZH1X5tQp_cIB91KAP5lqrE-c_HI3Yroq9HMVBL_1GpyXPOQ";
        TokenDto tokenDataModel = TokenVerify.getTokenDataModel(accessToken);
        boolean valid = isValid(tokenDataModel);
        System.out.println(valid);
    }

    private boolean isValid(TokenDto tokenDto) {
        String sessionKey = SessionUtil.getSessionKeyByTokenDataModel(tokenDto);
        if (StringUtil.isEmpty(sessionKey) || null == sessionRepository.getUserInfo()) {
            log.error("sessionKey=[{}],tokenDto=[{}]", sessionKey, FastjsonUtil.toJson(tokenDto));
            return false;
        }

        sessionRepository.doUpdate();
        return true;
    }


}
