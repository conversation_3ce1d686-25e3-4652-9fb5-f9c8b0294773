package com.caidaocloud.user.service.application.service.operate.impl;

import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.user.service.application.exception.ImportException;
import com.caidaocloud.user.service.application.service.AccountBaseInfoService;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.application.service.operate.AbstarctImportOperationLink;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.infrastructure.util.UserContext;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequences;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.rmi.ServerException;
import java.util.List;

/**
 * 检查导入用户是否存在
 *
 * <AUTHOR>
 * @date 2022/5/20
 **/
@Slf4j
public class ImportCheckAccountIsExist extends AbstarctImportOperationLink {

    private AccountBaseInfoService accountBaseInfoService;
    private UserBaseInfoService userBaseInfoService;

    public ImportCheckAccountIsExist(String processId) {
        super(processId);
        this.accountBaseInfoService = SpringUtil.getBean(AccountBaseInfoService.class);
        this.userBaseInfoService = SpringUtil.getBean(UserBaseInfoService.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public void operate(UserImportDto importUser, ImportExcelProcessDto processDto, UserImportErrorDto error) {
        try {
            List<AccountBaseInfoDo> existedList = accountBaseInfoService.getAccount(importUser.getMobile(), importUser.getEmail());
            if(!existedList.isEmpty()){
                if(existedList.size() > 1 || !StringUtils.equals(importUser.getMobile(), existedList.get(0).getMobNum())){
                    log.info("existedList:{}", FastjsonUtil.toJson(existedList));
                    String msg = LangUtil.getMsg(MsgCodeConstant.IMPORT_ERROR_EMAIL_MOBILE);
                    error.setErrorMsg(msg);
                    throw new ImportException(msg);
                }
                val existed = existedList.get(0);
                if(StringUtils.isNotEmpty(importUser.getEmail()) && StringUtils.isEmpty(existed.getEmail())){
                    existed.setEmail(importUser.getEmail());
                    accountBaseInfoService.saveOrUpdateAccount(existed);
                }
                importUser.setAccountId(existed.getAccountId());

                // 存在候选人用户
                List<UserBaseInfoDo> existUser = userBaseInfoService.getUserByAccountId(existed.getAccountId());
                Option<UserBaseInfoDo> option = Sequences.sequence(existUser)
                        .find(user -> UserContext.preCheckUser().getTenantId()
                                .equals(String.valueOf(user.getTenantId())) && user.getOnboarding());
                if (option.isDefined()) {
                    String msg = LangUtil.getMsg(MsgCodeConstant.USER_EXISTED);
                    error.setErrorMsg(msg);
                    throw new ImportException(msg);
                }
            }
        } catch (Exception e) {
            if(e instanceof ImportException){
                throw e;
            }else{
                log.error("导入账号异常", e);
                String msg = LangUtil.getMsg(MsgCodeConstant.UNKNOWN_EXCEPTION);
                error.setErrorMsg(msg);
                throw new ImportException(msg);
            }
        }
        if (next() != null) {
            next().operate(importUser, processDto, error);
        }
    }

}
