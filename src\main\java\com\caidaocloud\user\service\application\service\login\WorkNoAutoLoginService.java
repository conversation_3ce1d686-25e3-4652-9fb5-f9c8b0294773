package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.dto.masterdata.EmpInfoDto;
import com.caidaocloud.user.service.application.feign.IBaseInfoClient;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.repository.ISysEmpInfoRepository;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.util.FastjsonUtil;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class WorkNoAutoLoginService implements IAccountLoginService{

    @Value("${sso.workNo.login.default.tenant:}")
    private String workNoDefaultTenant;

    @Autowired
    private UserBaseInfoService userBaseInfoService;

    @Autowired
    private ISysEmpInfoRepository empInfoRepository;

    public String getSsoServiceKey(){
        return "saml.byWorkNo";
    }

    @Override
    public GrantType getGrantType() {
        return GrantType.SSO_TOKEN_GRANT_TYPE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        Map<String, List<String>> samlProps = loginDto.getSamlProps();
        PreCheck.preCheckArgument(!samlProps.containsKey("PERNER"), "未获取到PERNER参数，请检查");
        val workNo = samlProps.get("PERNER").stream().findFirst().orElse(null);
        log.info("saml login workNo: {}", workNo);
        val userInfo = new SecurityUserInfo();
        userInfo.setTenantId(workNoDefaultTenant);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try{
            val empResult = empInfoRepository.getEmpInfoByWorkno(workNo);
            List<EmpInfoDto> empInfoList = FastjsonUtil.convertList(empResult, EmpInfoDto.class);
            if(empInfoList.isEmpty()){
                throw new ServerException("login failed");
            }else{
                val empId = empInfoList.get(0).getEmpid();
                List<UserBaseInfoDo> list = userBaseInfoService.listByEmpId(empId);
                return list;
            }
        }finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}
