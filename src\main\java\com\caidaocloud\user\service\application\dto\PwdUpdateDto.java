package com.caidaocloud.user.service.application.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PwdUpdateDto {
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("账号ID")
    private Long accountId;
    @ApiModelProperty("使用用户统一注册账号登录时的前缀，例如：caidao-，登录时输入：前缀-账号进行登录")
    private String accountLoginPrefix;
    @ApiModelProperty("用户统一注册账号")
    private String account;
    @ApiModelProperty("新密码")
    private String newPasswd;
}
