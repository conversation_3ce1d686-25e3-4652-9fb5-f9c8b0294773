package com.caidaocloud.user.service.domain.entity;

import com.caidaocloud.user.service.domain.enums.AccountType;
import com.caidaocloud.user.service.domain.repository.IAccountBaseInfoRepository;
import com.caidaocloud.user.service.domain.util.ListUtil;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
@Data
public class AccountBaseInfoDo extends BaseEntity {
    /**
     * 账号ID
     */
    private Long accountId;
    /**
     * 使用用户统一注册账号登录时的前缀，例如：caidao-，登录时输入：前缀-账号进行登录
     */
    private String accountLoginPrefix;
    /**
     * 用户统一注册账号
     */
    private String account;
    /**
     * 注册手机号
     */
    private String mobNum;
    /**
     * 注册邮箱
     */
    private String email;
    /**
     * 密码
     */
    private String password;
    /**
     * 盐值
     */
    private String salt;
    /**
     * 手势密码
     */
    private String gesture;
    /**
     * 账号状态：1 正常 2 停用 3 锁定
     */
    private Integer status;
    /**
     * 注册方式
     */
    private String regType;

    @Autowired
    private IAccountBaseInfoRepository accountBaseInfoRepository;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public List<AccountBaseInfoDo> getList(String account, AccountType accountType) {
        return accountBaseInfoRepository.selectList(account, accountType);
    }

    public void deleteByIds(List<Long> accountIds) {
        accountBaseInfoRepository.deleteByIds(accountIds);
    }

    public void softDeleteByIds(List<Long> accountIds) {
        accountBaseInfoRepository.softDeleteByIds(accountIds);
    }

    public Long syncSave(AccountBaseInfoDo data) {
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(data);
        if (data.getAccountId() == null) {
            data.setAccountId(snowflakeUtil.createId());
            accountBaseInfoRepository.insert(data);
        } else {
            accountBaseInfoRepository.update(data);
        }
        return data.getAccountId();
    }

    @Transactional
    public void syncSave(List<AccountBaseInfoDo> dataList) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        for (AccountBaseInfoDo data : dataList) {
            if (data.getAccountId() == null) {
                data.setAccountId(snowflakeUtil.createId());
            }
        }
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(dataList);
        // 数据保存
        List<List<AccountBaseInfoDo>> lists = ListUtil.split(dataList, 500);
        for (List<AccountBaseInfoDo> list : lists) {
            accountBaseInfoRepository.insertBatch(list);
        }
    }

    @Transactional
    public void syncUpdate(List<AccountBaseInfoDo> dataList) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(dataList);
        // 数据保存
        List<List<AccountBaseInfoDo>> lists = ListUtil.split(dataList, 500);
        for (List<AccountBaseInfoDo> list : lists) {
            accountBaseInfoRepository.updateBatch(list);
        }
    }

    public List<AccountBaseInfoDo> getListByIds(List<Long> accountIds) {
        return accountBaseInfoRepository.getListByIds(accountIds);
    }

    public AccountBaseInfoDo getById(Long accountId) {
        return accountBaseInfoRepository.getById(accountId);
    }

    public void updateById(AccountBaseInfoDo data) {
        accountBaseInfoRepository.update(data);
    }

    public void updateByPrimaryKey(AccountBaseInfoDo data) {
        accountBaseInfoRepository.updateByPrimaryKeySelective(data);
    }
}
