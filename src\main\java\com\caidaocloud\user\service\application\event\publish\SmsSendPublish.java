package com.caidaocloud.user.service.application.event.publish;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.user.service.application.event.dto.SmsSendMessageDto;
import com.caidaocloud.user.service.application.event.publish.message.SmsSendMessage;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class SmsSendPublish {
    private final static String EXCHANGE = "user.message.fac.direct.exchange";
    private final static String ROUTING_KEY = "routingKey.user.message.sms.send";

    @Resource
    private MqMessageProducer<SmsSendMessage> producer;

    public void publishMsg(SmsSendMessageDto message) {
        SmsSendMessage msg = new SmsSendMessage();
        msg.setBody(FastjsonUtil.toJson(message));
        msg.setExchange(EXCHANGE);
        msg.setRoutingKey(ROUTING_KEY);
        log.info("SmsMsgPublish={}", msg.getBody());
        producer.publish(msg);
    }
}
