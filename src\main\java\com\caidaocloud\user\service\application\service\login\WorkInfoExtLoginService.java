package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.dto.masterdata.EmpInfoDto;
import com.caidaocloud.user.service.application.feign.IHrFeign;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * created by: FoAng
 * create time: 9/12/2022 2:44 下午
 */
@Slf4j
@Service
public class WorkInfoExtLoginService implements IAccountLoginService{

    @Value("${saml.login.samlPopKey:User Name}")
    private String samlPopKey;

    @Value("${saml.login.defaultTenant:33}")
    private String defaultTenant;

    @Resource
    private IHrFeign hrFeign;

    @Resource
    private UserBaseInfoService userBaseInfoService;

    @Override
    public String getSsoServiceKey(){
        return "saml.workExt";
    }

    @Override
    public GrantType getGrantType() {
        return GrantType.SSO_TOKEN_GRANT_TYPE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        Map<String, List<String>> samlProps = loginDto.getSamlProps();
        PreCheck.preCheckArgument(StringUtil.isEmpty(samlPopKey) || !samlProps.containsKey(samlPopKey), "未获取到samlPopKey参数，请检查");
        val extValue = samlProps.get(samlPopKey).stream().findFirst().orElse(null);
        log.info("saml login workExtValue: {}", extValue);
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId(defaultTenant);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try{
            val workInfoResult = hrFeign.getEmpIdByUserName(extValue);
            PreCheck.preCheckArgument(workInfoResult == null || !workInfoResult.isSuccess(), "sso登录失败");
            EmpInfoDto empInfo = FastjsonUtil.convertObject(workInfoResult.getData(), EmpInfoDto.class);
            PreCheck.preCheckArgument(empInfo == null || empInfo.getEmpid() == null, "sso获取用户信息失败");
            return userBaseInfoService.listByEmpId(empInfo.getEmpid());
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}
