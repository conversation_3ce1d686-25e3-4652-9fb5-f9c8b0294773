package com.caidaocloud.user.service.domain.repository;

import com.caidaocloud.user.service.domain.entity.PwdRuleDo;

import java.util.List;

public interface IPwdRuleRepository {
    void insertBatch(List<PwdRuleDo> dataList);

    void insert(PwdRuleDo data);

    void update(PwdRuleDo data);

    int insertSelective(PwdRuleDo record);

    int updateByPrimaryKeySelective(PwdRuleDo record);

    void delete(List<Long> pwdRuleIds);

    void deleteByTenantIds(List<Long> tenantIds);

    List<PwdRuleDo> getPwdRuleListByTenantIds(List<Long> tenantIds);
}
