-- 用户登录日志表
create table if not exists user_login_log
(
    id             varchar(50)  not null,
    user_id        bigint,
    tenant_id      varchar(50),
    account        varchar(100),
    user_name      varchar(100),
    login_time     timestamp    not null,
    login_platform integer,
    login_type     integer,
    device_type    integer,
    ip_address     varchar(50),
    user_agent     varchar(500),
    login_status   integer      not null default 1,
    fail_reason    varchar(200),
    session_id     varchar(100),
    ext_info       varchar(500),
    create_time    timestamp    not null,
    constraint pk_user_login_log_id primary key (id)
);

comment on table user_login_log is '用户登录日志表';
comment on column user_login_log.id is '日志ID';
comment on column user_login_log.user_id is '用户ID';
comment on column user_login_log.tenant_id is '租户ID';
comment on column user_login_log.account is '用户账号';
comment on column user_login_log.user_name is '用户名';
comment on column user_login_log.login_time is '登录时间';
comment on column user_login_log.login_platform is '登录平台：0为web端，1为移动端, 2 第三方为web端，3 为第三方移动端';
comment on column user_login_log.login_type is '登录方式：0为账号密码登录，1:为手机验证码登录,2为微信扫描登录，3为支付宝扫码登录，4为腾讯sso登录';
comment on column user_login_log.device_type is '登录设备类型：web:0 android:1,ios:2';
comment on column user_login_log.ip_address is '登录IP地址';
comment on column user_login_log.user_agent is '用户代理信息（浏览器信息）';
comment on column user_login_log.login_status is '登录状态：1成功，0失败';
comment on column user_login_log.fail_reason is '登录失败原因';
comment on column user_login_log.session_id is '会话ID';
comment on column user_login_log.ext_info is '扩展信息';
comment on column user_login_log.create_time is '创建时间';

-- 创建索引
create index if not exists idx_user_login_log_user_id on user_login_log (user_id);
create index if not exists idx_user_login_log_tenant_id on user_login_log (tenant_id);
create index if not exists idx_user_login_log_login_time on user_login_log (login_time);
create index if not exists idx_user_login_log_ip_address on user_login_log (ip_address);
create index if not exists idx_user_login_log_account on user_login_log (account);
