package com.caidaocloud.user.service;

import com.caidaocloud.user.service.application.service.user.IUserHandler;
import com.caidaocloud.user.service.application.service.user.UserHandlerManger;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class UserHandlerTest {

    @Test
    public void test() {
        IUserHandler defaultUserHandler = UserHandlerManger.getDefaultUserHandler();
        System.out.println(defaultUserHandler);
    }

}
