package com.caidaocloud.user.service;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.dto.preemp.PreEmpUserDto;
import com.caidaocloud.user.service.application.enums.DataOpEnum;
import com.caidaocloud.user.service.application.service.PreEmpUserService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * created by: FoAng
 * create time: 6/9/2022 10:04 上午
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class TestUserImport {
    @Resource
    private PreEmpUserService preEmpUserService;

    @Test
    public void testImportUser() {
        PreEmpUserDto dto = new PreEmpUserDto();
        dto.setEmpId(1571804789159943L);
        dto.setEmail("test");
        dto.setMobileNo("123");
        dto.setUserName("管理员2");
        preEmpUserService.updatePreEmpUser(dto);
    }

    @Before
    public void setUser() {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("8");
        userInfo.setEmpId(0L);
        userInfo.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
    }

    @Test
    public void testEnum() {
        DataOpEnum.valueOf("INSERT");
    }
}
