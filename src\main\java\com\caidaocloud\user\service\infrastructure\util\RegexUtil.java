package com.caidaocloud.user.service.infrastructure.util;

import java.util.regex.Pattern;

public class RegexUtil {

    private static final String MOBILE_PATTERN = "^(1[3|8|4|7|5|6|9][0-9]{9})$";

    private static final String EMAIL_PATTERN = "^([a-zA-Z0-9_\\-\\.]+)@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([a-zA-Z0-9\\-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$";

    public static boolean isMatchMobile(String input) {
        Pattern pattern = Pattern.compile(MOBILE_PATTERN);
        return pattern.matcher(input).matches();
    }

    public static boolean isMatchEmail(String input) {
        Pattern pattern = Pattern.compile(EMAIL_PATTERN);
        return pattern.matcher(input).matches();
    }

}
