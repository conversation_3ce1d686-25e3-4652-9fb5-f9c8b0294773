package com.caidaocloud.user.service.application.service.user;

import com.caidaocloud.security.service.ISessionService;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class MenuService {
    @Resource
    private ISessionService sessionService;

    public List<String> getMenuCodeList(){
        List<String> permissions = sessionService.getPermissions();
        return null == permissions ? Lists.newArrayList() : permissions;
    }
}
