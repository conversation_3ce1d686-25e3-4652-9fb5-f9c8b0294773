package com.caidaocloud.user.service.application.event.publish.enums;

import lombok.Getter;

/**
 * created by: FoAng
 * create time: 13/12/2022 2:16 下午
 */
@Getter
public enum UserEventType {

    ADD("add", "0"),
    UPDATE("update", "1"),
    DELETE("delete", "2");

    private final String code;

    private final String value;

    UserEventType(String code, String value) {
        this.code = code;
        this.value = value;
    }
}
