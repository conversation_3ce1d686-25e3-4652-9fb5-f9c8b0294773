package com.caidaocloud.user.service.feign;

import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.application.dto.SysEmpInfoDto;
import com.caidaocloud.user.service.application.feign.IBaseInfoClient;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class IBaseClientTest {

    @Autowired
    private IBaseInfoClient baseInfoClient;

    @Test
    public void getEmpInfoByWorknoTest() {
        ArrayList<String> worknoList = Lists.newArrayList("ts0127");
//        Result<List<SysEmpInfoDto>> result = baseInfoClient.getEmpInfoByWorkno(worknoList);
//        List<SysEmpInfoDto> data = result.getData();
//        for (SysEmpInfoDto str : data) {
//            System.out.println(str);
//        }
    }

    @Test
    public void getEmpInfoByEmpIdsTest() {
//        ArrayList<Long> worknoList = Lists.newArrayList(0L);
//        Result<List<SysEmpInfoDto>> result = baseInfoClient.getEmpInfoByEmpIds(worknoList);
//        List<SysEmpInfoDto> data = result.getData();
    }

}
