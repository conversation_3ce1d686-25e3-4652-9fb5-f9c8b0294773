package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.service.IEmailService;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.infrastructure.util.RegexUtil;
import com.caidaocloud.user.service.interfaces.dto.EmailCodeDto;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/user/email/v2")
@Api(value = "/api/user/email/v2", tags = "邮件服务接口")
public class EmailController {
    @Autowired
    private IEmailService emailService;
    @Autowired
    private UserBaseInfoService userBaseInfoService;
    @Autowired
    private CacheService cacheService;

    @GetMapping("/code")
    @ApiOperation("发送验证码")
    public Result<Boolean> sendMessageCode(@RequestParam("email") String email) {
        if (StringUtil.isEmpty(email) || !RegexUtil.isMatchEmail(email)) {
            return Result.fail(LangUtil.getMsg(MsgCodeConstant.EMAIL_FORMAT_ERROR));
        }

        boolean tooMuch = true;
        for (int i = 0; i < 5; i++) {
            val exist = cacheService.containsKey("user_verify_code_count_" + email + "_" + i);
            if (!exist) {
                cacheService.cacheValue("user_verify_code_count_" + email + "_" + i, "1", 10 * 60);
                tooMuch = false;
                break;
            }
        }
        if (tooMuch) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_" + MsgCodeConstant.VERIFY_CODE_TOO_FREQUENTLY));
        }

        boolean sendResult = emailService.sendMessageCode(email);
        return sendResult ? Result.status(true, 0, LangUtil.getMsg(MsgCodeConstant.SEND_SUCCESS))
                : Result.status(false, -1, LangUtil.getMsg(MsgCodeConstant.SEND_FAIL));
    }

    @PostMapping("/verify")
    @ApiOperation("验证短信验证码")
    public Result<Boolean> verify(@RequestBody EmailCodeDto dto) {
        if (StringUtil.isEmpty(dto.getEmail()) || !RegexUtil.isMatchEmail(dto.getEmail())) {
            return Result.fail(LangUtil.getMsg(MsgCodeConstant.EMAIL_FORMAT_ERROR));
        }
        if (StringUtil.isEmpty(dto.getVerifyCode())) {
            return Result.fail(LangUtil.getMsg(MsgCodeConstant.VERIFICATION_CODE_CANNOT_EMPTY));
        }

        return Result.ok(emailService.verify(dto));
    }
}
