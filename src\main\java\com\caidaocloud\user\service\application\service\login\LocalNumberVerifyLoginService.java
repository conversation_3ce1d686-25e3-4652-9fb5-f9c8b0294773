package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.foreign.service.IForeignLoginService;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.web.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 本机号码校验认证
 */
@Component
public class LocalNumberVerifyLoginService implements IAccountLoginService {
    @Autowired
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Autowired
    private IForeignLoginService foreignLoginService;

    @Override
    public GrantType getGrantType() {
        return GrantType.LOCAL_NUMBER_VERIFICATION_TYPE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        // 调用阿里云sdk完成本机号码校验认证
        Result<Boolean> result = foreignLoginService.verifyMobile(loginDto.getLoginAccessCode(), loginDto.getAccount());
        PreCheck.preCheckArgument(!result.isSuccess() || !result.getData(), LangUtil.getMsg(MsgCodeConstant.LOCAL_NUMBER_VERIFY_LOGIN_FAIL));
        String mobile = loginDto.getAccount();
        // 账号查询并检查
        List<AccountBaseInfoDo> accountList = accountBaseInfoDomainService.getAndCheckAccountList(mobile, GrantType.LOCAL_NUMBER_VERIFICATION_TYPE);
        // 用户查询并检查
        List<Long> accountIds = accountList.stream().map(AccountBaseInfoDo::getAccountId).collect(Collectors.toList());
        List<UserBaseInfoDo> userList = userBaseInfoDomainService.getAndCheckUserList(accountIds);
        // 只匹配到一个用户
        boolean singleUser = userList.size() == 1;
        if (singleUser) {
            // 账号已停用或已锁定判断
            long normalCount = userList.stream().filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).count();
            // 账号已停用或已锁定，请联系管理员
            PreCheck.preCheckArgument(normalCount == 0, LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));
        }
        return userList;
    }
}
