package com.caidaocloud.user.service.application.service.operate;

import com.caidaocloud.user.service.application.constant.KeyConstant;
import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;

import java.util.List;

/**
 * 导入责任链抽象类
 *
 * <AUTHOR>
 * @date 2022/5/20
 **/
public abstract class AbstarctImportOperationLink {

    private AbstarctImportOperationLink next;

    private String processId;

    public AbstarctImportOperationLink(String processId) {
        this.processId = processId;
    }

    public AbstarctImportOperationLink appendNext(AbstarctImportOperationLink ops) {
        this.next = ops;
        return this;
    }

    public AbstarctImportOperationLink next() {
        return next;
    }

    /**
     * 操作
     *
     * @param
     */
    public abstract void operate(UserImportDto importUser, ImportExcelProcessDto processDto, UserImportErrorDto error);

    public String getErrorKey() {
        return KeyConstant.getImportErrorKey(processId);
    }

    public String getProcessKey() {
        return KeyConstant.getImportProcessKey(processId);
    }

}
