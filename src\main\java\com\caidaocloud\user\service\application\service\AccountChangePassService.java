package com.caidaocloud.user.service.application.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.enums.SmsCodeType;
import com.caidaocloud.user.service.application.service.impl.SmsService;
import com.caidaocloud.user.service.application.service.login.PasswordLoginService;
import com.caidaocloud.user.service.application.utils.CacheKeyDefine;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.ChangePasswordDto;
import com.caidaocloud.user.service.interfaces.dto.FindPasswordDto;
import com.caidaocloud.user.service.interfaces.dto.IdentityVerifyDto;
import com.caidaocloud.user.service.interfaces.dto.ResetPasswordDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.user.service.interfaces.vo.IdentityVerifyResultVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.caidaocloud.user.service.application.constant.Constant.CHANGE_PASS_KEY;
import static com.caidaocloud.user.service.application.constant.Constant.FIND_PASS_KEY;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/6/11
 */
@Service
public class AccountChangePassService {
	@Resource
	private CacheService cacheService;
	@Resource
	private PasswordLoginService passwordLoginService;
	@Resource
	private UserAppService userAppService;
	@Resource
	private AccountBaseInfoDomainService accountBaseInfoDomainService;
	@Autowired
	private UserBaseInfoDomainService userBaseInfoDomainService;
	@Autowired
	private MsgNoticeService msgNoticeService;
	@Autowired
	private SmsService smsService;

	public IdentityVerifyResultVo verify(IdentityVerifyDto verify) {
		Long accountId = doVerify(verify);
		return genOptToken(accountId);
	}

	private IdentityVerifyResultVo genOptToken(Long accountId) {
		String uuid = UUID.randomUUID().toString().replace("-", "");
		if (accountId != null) {
			cacheService.cacheValue(FIND_PASS_KEY + uuid, Objects.toString(accountId), 60 * 5);
			return new IdentityVerifyResultVo(uuid, multiTenantStatus(accountId));
		} else {
			cacheService.cacheValue(CHANGE_PASS_KEY + SecurityUserUtil.getSecurityUserInfo()
					.getUserId() + uuid, uuid, 60 * 5);
			return new IdentityVerifyResultVo(uuid, false);
		}
	}

	private Long doVerify(IdentityVerifyDto verifyDto) {
		GrantType type = verifyDto.getType();
		switch (type) {
		case PASSWORD_TOKEN_GRANT_TYPE:
			return passwordLoginService.verify(verifyDto.getPass());
		case MOBILE_CODE_TOKEN_GRANT_TYPE:
			// 短信验证码
			return verifySmsCode(verifyDto.getAccount(), verifyDto.getPass());
		default:
			throw new ServerException("unsupported grant type: " + type);
		}
	}

	public void findPasswordReset(FindPasswordDto passwordDto) {
		PreCheck.preCheckArgument(StringUtils.isEmpty(passwordDto.getAccount()) || StringUtils.isEmpty(passwordDto.getToken())
				|| StringUtils.isEmpty(passwordDto.getToken()),
				LangUtil.getMsg(MsgCodeConstant.ILLEGAL_CREDENTIALS));
		String accessToken = passwordDto.getToken();
		String cacheAccountId = cacheService.getValue(FIND_PASS_KEY + accessToken);
		if (StringUtils.isNotEmpty(cacheAccountId)) {
			boolean validPassword = PasswordHelper.PasswordGenerator.validateSimplePassword(passwordDto.getPwd());
			PreCheck.preCheckArgument(!validPassword, "密码不符合规则");
			AccountBaseInfoDo accountBaseInfoDo = accountBaseInfoDomainService.getAccountByAccountId(Long.valueOf(cacheAccountId));
			if (accountBaseInfoDo != null) {
				accountBaseInfoDomainService.changePassword(passwordDto.getPwd().trim(), 0L, accountBaseInfoDo);
			}
		}
	}



	/**
	 * 发送短信验证码
	 * @param mobile
	 * @param code
	 */
	public void sendVerifySmsCode(String mobile, String code) {
		List<UserBaseInfoDo> userList = userBaseInfoDomainService.getUserListByAccount(mobile);
		if (CollectionUtils.isNotEmpty(userList)) {
			// 检查用户是否存在
			boolean tooFrequency = false;
			for (int i = 0; i < 5; i++) {
				val exist = cacheService.containsKey("user_verify_code_count_" + mobile + "_" + i);
				if (!exist) {
					cacheService.cacheValue("user_verify_code_count_" + mobile + "_" + i, "1", 10 * 60);
					break;
				}
				tooFrequency = true;
			}
			if (tooFrequency) {
				throw ServerException.globalException(LangUtil.getMsg(MsgCodeConstant.VERIFY_CODE_TOO_FREQUENTLY));
			}
			boolean sendResult = smsService.sendMessageCode(mobile, code, SmsCodeType.RETRIEVE_PASSWORD);
			if (!sendResult) {
				throw new ServerException(LangUtil.getMsg(MsgCodeConstant.SEND_FAIL));
			}
		}
		throw new ServerException(LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));
	}

	/**
	 * 验证短信验证码
	 * @param smsCode
	 */
	public Long verifySmsCode(String mobile, String smsCode) {
		List<AccountBaseInfoDo> accountBaseInfoDos = accountBaseInfoDomainService.getAccount(mobile, null);
		AccountBaseInfoDo accountBaseInfoDo = null;
		String cacheReceiptKey = CacheKeyDefine.SMS_RETRIEVE_PASSWORD_KEY_ + mobile;
		boolean match = false;
		if (CollectionUtils.isNotEmpty(accountBaseInfoDos)) {
			accountBaseInfoDo = accountBaseInfoDos.get(0);
			String cacheValue = cacheService.getValue(cacheReceiptKey);
			match = StringUtils.isNotEmpty(cacheValue) && cacheValue.equals(smsCode);
		}
		PreCheck.preCheckArgument(!match || accountBaseInfoDo == null, LangUtil.getMsg(MsgCodeConstant.VERIFY_CODE_ERROR_INVALID));
		List<UserBaseInfoDo> userBaseInfoDos = userBaseInfoDomainService.getUserListByAccountId(accountBaseInfoDo.getAccountId());
		// 检查用户是否存在
		if (CollectionUtils.isEmpty(userBaseInfoDos)) {
			throw new ServerException(LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));
		}
		boolean singleUser = userBaseInfoDos.size() == 1;
		if (singleUser) {
			// 检查账号是否已停用或已锁定
			long normalCount = userBaseInfoDos.stream().filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).count();
			if (normalCount == 0) {
				// 账号已停用或已锁定，请联系管理员
				throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));
			}
		}
		// 移除短信验证码
		cacheService.remove(cacheReceiptKey);
		return accountBaseInfoDo.getAccountId();
	}

	private boolean multiTenantStatus(Long accountId) {
		AccountBaseInfoDo accountBaseInfoDo = accountBaseInfoDomainService.getAccountByAccountId(accountId);
		List<UserBaseInfoDo> userBaseInfoDos = userBaseInfoDomainService.getUserListByAccount(String.valueOf(accountBaseInfoDo.getAccountId()));
		return CollectionUtils.isNotEmpty(userBaseInfoDos) && userBaseInfoDos.size() > 1;
	}


	public void changePass(ChangePasswordDto changePasswordDto) {
		checkOptToken(changePasswordDto.getToken());
		userAppService.doChangePassword(changePasswordDto.getPassword());
	}

	private void checkOptToken(String token) {
		String key=CHANGE_PASS_KEY + SecurityUserUtil.getSecurityUserInfo()
				.getUserId() + token;
		if (!cacheService.containsKey(key)) {
			throw new ServerException("opt token is invalid");
		}
	}


	/**
	 * 重置密码
	 * @param passwordDto
	 */
	public void resetPasswordCommon(ResetPasswordDto passwordDto) {
		int resetType = passwordDto.getResetType();
		String password;
		if (resetType == 0) {
			password = PasswordHelper.PasswordGenerator.generatePassword(6);
		} else {
			password = passwordDto.getNewPwd();
			boolean validPassword = PasswordHelper.PasswordGenerator.validateSimplePassword(password);
			PreCheck.preCheckArgument(!validPassword, "密码不符合规则");
		}
		Optional<UserBaseInfoDo> infoDoOptional = userBaseInfoDomainService.getByUserId(passwordDto.getUserId());
		Long userId = SecurityUserUtil.getSecurityUserInfo().getUserId();
		infoDoOptional.ifPresent(it -> {
			AccountBaseInfoDo accountInfo = accountBaseInfoDomainService.getById(it.getAccountId());
			accountBaseInfoDomainService.changePassword(password.trim(), userId, accountInfo);
			if (passwordDto.isNotify()) {
				int userType = passwordDto.getUserType();
				Map<String, String> extMap = Maps.newHashMap();
				List<String> subjects = Lists.newArrayList(String.valueOf(it.getEmpId()));
				extMap.put("empId", String.valueOf(it.getEmpId()));
				extMap.put("onboarding.account.password", password);
				extMap.put("onboarding.account", it.getAccount());
				msgNoticeService.sendMsgNoticeEvent(userType == 0 ? NoticeType.EMPLOYEE_ACCOUNT_CREATE :
						NoticeType.ON_BOARDING_ACCOUNT_CREATE, subjects, extMap, "user", userType);
			}
		});
	}

	public String resetPass(Long userId, Long accountId) {
		String newPwd = PasswordHelper.PasswordGenerator.generatePassword(6);
		AccountBaseInfoDo accountInfo = accountBaseInfoDomainService.getById(accountId);
		accountBaseInfoDomainService.changePassword(newPwd.trim(), userId, accountInfo);
		return newPwd;
	}

	public String changePassForFirstLogin(ChangePasswordDto changePasswordDto) {
		Long userId = SecurityUserUtil.getSecurityUserInfo().getUserId();

		Optional<UserBaseInfoDo> optional = userBaseInfoDomainService.getByUserId(SecurityUserUtil.getSecurityUserInfo().getUserId());
		PreCheck.preCheckArgument(!optional.isPresent() || null == optional.get().getUserId(),
				LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

		// 账号查询
		UserBaseInfoDo userBaseInfoData = optional.get();
		AccountBaseInfoDo accountBaseInfoData = accountBaseInfoDomainService.getAccountByAccountId(userBaseInfoData.getAccountId());
		PreCheck.preCheckArgument(null == accountBaseInfoData || null == accountBaseInfoData.getAccountId(),
				LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));
		String key = String.format("change_pwd_account:%s", accountBaseInfoData.getAccountId());
		if (cacheService.containsKey(key)) {
			accountBaseInfoDomainService.changePassword(changePasswordDto.getPassword().trim(), userId, accountBaseInfoData);
			cacheService.remove(key);

			List<UserBaseInfoDo> list = userBaseInfoDomainService.getUserListByAccountId(userBaseInfoData.getAccountId());
			//多租户单独提示
			if(list != null && list.size()>1)
				return "同一账号下的多个租户，其登录密码将会同步被修改";
		}
		return "";
	}
}	
