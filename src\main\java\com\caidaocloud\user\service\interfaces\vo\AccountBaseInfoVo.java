package com.caidaocloud.user.service.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("账号信息")
public class AccountBaseInfoVo {
    @ApiModelProperty("账号ID")
    private Long accountId;
    @ApiModelProperty("使用用户统一注册账号登录时的前缀，例如：caidao-，登录时输入：前缀-账号进行登录")
    private String accountLoginPrefix;
    @ApiModelProperty("用户统一注册账号")
    private String account;
    @ApiModelProperty("注册手机号")
    private String mobNum;
    @ApiModelProperty("注册邮箱")
    private String email;
    @ApiModelProperty("密码")
    private String password;
    @ApiModelProperty("盐值")
    private String salt;
    @ApiModelProperty("手势密码")
    private String gesture;
    @ApiModelProperty("账号状态：1 正常 2 停用 3 锁定")
    private Integer status;
    @ApiModelProperty("注册方式")
    private String regType;
}
