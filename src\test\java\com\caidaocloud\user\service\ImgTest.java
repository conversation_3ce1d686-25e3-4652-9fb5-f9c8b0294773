package com.caidaocloud.user.service;

import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ImgTest {
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private boolean validImg(String fileName){

        int idx = fileName.lastIndexOf(".");
        if(idx < 0){
            return false;
        }

        String str = fileName.substring(idx, fileName.length());
        String reg = "[Gg][Ii][Ff]|[Jj][Pp][Gg]|[Bb][Mm][Pp]|[Jj][Pp][Ee][Gg]";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(str.toLowerCase());
        return matcher.find();
    }

    public static void main(String[] args) {
        ImgTest test = new ImgTest();
        test.setName("test-hhh");

        StandardEvaluationContext context = new StandardEvaluationContext();
        // 为了让表达式可以访问该对象, 先把对象放到上下文中
        context.setVariable("user", null);
        String str = "#user?.name";
        ExpressionParser parser = new SpelExpressionParser();
        String aaa = parser.parseExpression(str).getValue(context, String.class);


        System.out.println(aaa);

       // System.out.println(new ImgTest().validImg("abc.jPg"));
    }
}
