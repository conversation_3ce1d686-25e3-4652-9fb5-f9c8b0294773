package com.caidaocloud.user.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("mxy_sso_config")
public class MxySsoConfigPo {
    @TableId
    private String tenantId;
    private String tenantName;

    private String corpId;
    private String corpSecret;
    private String token;
    private String mxyKey;

    private Long createBy;
    private Long createTime;
    private Long updateBy;
    private Long updateTime;
}
