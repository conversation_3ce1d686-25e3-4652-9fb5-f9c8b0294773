package com.caidaocloud.user.service.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.user.service.domain.repository.IMxySsoConfigRepository;
import com.caidaocloud.user.service.infrastructure.repository.mapper.MxySsoConfigMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.MxySsoConfigPo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Repository
public class MxySsoConfigRepositoryImpl implements IMxySsoConfigRepository {
    @Resource
    private MxySsoConfigMapper mxySsoConfigMapper;

    @Override
    public MxySsoConfigPo getById(String tenantId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("tenant_id", tenantId);
        return mxySsoConfigMapper.selectOne(queryWrapper);
    }
}
