create table if not exists his_user_pwd_rec
(
    rec_id      bigint not null,
    account_id  bigint not null,
    user_id     bigint,
    tenant_id   bigint not null,
    password    varchar(100),
    salt        varchar(50),
    create_by   bigint not null,
    create_time bigint not null,
    update_by   bigint,
    update_time bigint,
    constraint pk_his_user_pwd_rec_rec_id primary key (rec_id)
    );
comment on table his_user_pwd_rec is '用户密码历史表';
comment on column his_user_pwd_rec.rec_id is '修改记录ID';
comment on column his_user_pwd_rec.account_id is '账号ID';
comment on column his_user_pwd_rec.user_id is '用户ID';
comment on column his_user_pwd_rec.tenant_id is '租户ID';
comment on column his_user_pwd_rec.password is '密码';
comment on column his_user_pwd_rec.salt is '盐值';