package com.caidaocloud.user.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @date 2024/6/11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IdentityVerifyResultVo {

	@ApiModelProperty("验证receiptToken")
	private String token;

	@ApiModelProperty("匹配多个租户")
	private boolean multiTenant;

}
