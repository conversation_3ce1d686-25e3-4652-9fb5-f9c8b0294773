package com.caidaocloud.user.service;

import com.caidaocloud.user.service.application.foreign.service.SmsSendFactory;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.User;
import com.caidaocloud.user.service.domain.repository.IUserRepository;
import com.caidaocloud.user.service.interfaces.dto.SyncOnBoarDingDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.PropUtil;
import com.caidaocloud.util.SignUtil;
import com.caidaocloud.util.SpringUtil;
import lombok.SneakyThrows;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/7/2021 3:55 PM
 * 4
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class UserTest {

    @Autowired
    private IUserRepository userRepository;

    @Value("${caidaocloud.smsType}")
    private String smsTemplate;
    @Resource
    private PasswordHelper passwordHelper;
    @Test
    public void testPwd(){
        String password = "123456";
        if (passwordHelper.matches(password, "57914041b705fe6fb9d3a94516ecd52b", "57914041b705fe6fb9d3a94516ecd52b")) {
            System.out.println("12323");
        }
    }


    @Test
    public void user_save_test() {
        List<User> list = new ArrayList<>();
        User userDo = new User();
        userDo.setUserid(1);
        userDo.setEmpname("max");
        userDo.setAccount("max");
        userDo.setEmail("<EMAIL>");
        userDo.setPasswd(SignUtil.md5("123456"));
        userDo.setMobnum("***********");
        userDo.setCrttime(System.currentTimeMillis());
        list.add(userDo);
        userRepository.save(list, "5ff7fd9d83a5837cc0f30c38");
    }

    @Test
    public void test() {
        System.out.println(PropUtil.getProp("caidaocloud.smsType"));
        System.out.println(smsTemplate);
    }

    @SneakyThrows
    @Test
    public void testSendSms() {
        String json = "[{\n"
                + "  \"mobNum\": \"***********\",\n"
                + "  \"tenantId\": 11,\n"
                + "  \"userName\": \"测试\",\n"
                + "  \"empId\": ****************\n"
                + "}]";
        List<SyncOnBoarDingDto> list = FastjsonUtil.toArrayList(json, SyncOnBoarDingDto.class);
        SpringUtil.getBean(UserBaseInfoService.class).syncOnBoarDingSave(list);
    }
}
