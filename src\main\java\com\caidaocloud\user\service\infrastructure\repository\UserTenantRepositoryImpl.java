package com.caidaocloud.user.service.infrastructure.repository;

import com.caidaocloud.user.service.infrastructure.repository.mongo.MongodbDao;
import com.caidaocloud.user.service.infrastructure.repository.po.UserTenantPo;
import com.caidaocloud.user.service.domain.entity.UserTenant;
import com.caidaocloud.user.service.domain.repository.IUserTenantRepository;
import com.caidaocloud.util.ObjectConverter;
import com.mongodb.BasicDBObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/8/2021 1:39 PM
 * 4
 */
@Repository
public class UserTenantRepositoryImpl implements IUserTenantRepository {
    private static final String collectionName = "basic_r_user_tenant";
    @Autowired
    private MongodbDao<UserTenantPo> mongodbDao;

    @Override
    public void save(List<UserTenant> list) {
        List<UserTenantPo> poList = ObjectConverter.convertList(list, UserTenantPo.class);
        mongodbDao.save(poList, collectionName);
    }

    @Override
    public UserTenant getByUserIdOrAccount(long userId, String userAccount) {
        BasicDBObject query = new BasicDBObject();
        if (userId != 0) {
            query.put("userId", userId);
        }

        if (StringUtils.isNotEmpty(userAccount)) {
            query.put("userAccount", userAccount);
        }

        List<UserTenantPo> entities = this.query(query);
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }
        return ObjectConverter.convert(entities.get(0), UserTenant.class);
    }

    public List<UserTenantPo> query(BasicDBObject query) {
        return mongodbDao.query(query, collectionName, UserTenantPo.class);
    }
}
