package com.caidaocloud.user.service.interfaces.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class UserDetailInfoVo {


    @ApiModelProperty
    private String accountId;
    @ApiModelProperty("用户统一注册账号")
    @Excel(name = "账号")
    private String account;
    @ApiModelProperty("userId")
    private Long userId;
    @ApiModelProperty("用户姓名")
    @Excel(name = "姓名")
    private String userName;
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty("工号")
    @Excel(name = "工号")
    private String workno;
    @ApiModelProperty("所属组织")
    @Excel(name = "所属部门")
    private String organizeTxt;
    @ApiModelProperty("注册手机号")
    @Excel(name = "手机号")
    private String mobile;
    @ApiModelProperty("注册邮箱")
    @Excel(name = "邮箱")
    private String email;
    @ApiModelProperty("账号状态：1 正常 2 停用 3 锁定")
    @Excel(name = "用户状态")
    private Integer status;
    @ApiModelProperty("角色ID")
    private List<String> roleIds;
    @ApiModelProperty("角色")
    @Excel(name = "所属角色")
    private String roleNames;

}
