package com.caidaocloud.user.service.application.cron;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.extension.exceptions.ApiException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.dto.SysEmpInfoDto;
import com.caidaocloud.user.service.application.dto.masterdata.EmpInfoDto;
import com.caidaocloud.user.service.application.feign.DingTalkFeignClient;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.domain.repository.ISysEmpInfoRepository;
import com.caidaocloud.util.FastjsonUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2DepartmentListsubidRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiV2DepartmentListsubidResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 钉钉userId同步任务
 */
@Component
@Slf4j
public class DingUserIdTask {

    @NacosValue("${caidaocloud.msg.dingtalk.agentId:}")
    private String agentId;
    @NacosValue("${caidaocloud.msg.dingtalk.appKey:}")
    private String appKey;
    @NacosValue("${caidaocloud.msg.dingtalk.appSecret:}")
    private String appSecret;
    // 官方sdk限制为不能超过100
    @NacosValue("${caidaocloud.msg.dingtalk.pageSize:100}")
    private Long pageSize = 100L;

    @Resource
    private DingTalkFeignClient dingTalkFeignClient;
    @Autowired
    private ISysEmpInfoRepository empInfoRepository;
    @Resource
    private UserBaseInfoService userBaseInfoService;

    @XxlJob("synDingUserIdJobHandler")
    public ReturnT<String> synDingUserIdJobHandler() {
        XxlJobHelper.log("XxlJob synDingUserIdJobHandler start");
        log.info("cronTask[dingUserId]------------------------start execution,time {}", System.currentTimeMillis());

        try {
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId("8");
            userInfo.setUserId(0L);
            userInfo.setEmpId(0L);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            Long deptId = 1L;
            Long cursor = 0L;
            //先查人
            getUserAndUpdateDingUserId(deptId, cursor, pageSize);
            //再查部门
            getDepartmentAndUpdateDingUserId(deptId, cursor, pageSize);
        }finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }

        log.info("cronTask[dingUserId]------------------------end execution,time {}", System.currentTimeMillis());
        XxlJobHelper.log("XxlJob synDingUserIdJobHandler end");
        return ReturnT.SUCCESS;
    }

    public void getDepartmentAndUpdateDingUserId(Long deptId, Long cursor, Long size) {
        OapiV2DepartmentListsubidResponse.DeptListSubIdResponse deptListSubIdResponse = departmentListSubId(deptId);
        if(deptListSubIdResponse == null){
            // 修复华强部分场景下空异常问题
            return;
        }
        List<Long> deptIdList = deptListSubIdResponse.getDeptIdList();
        log.info("user getDepartmentAndUpdateDingUserId deptIdList = {}", FastjsonUtil.toJson(deptIdList));
        if (CollectionUtils.isEmpty(deptIdList)) {
            return;
        }

        for (Long dept : deptIdList) {
            getUserAndUpdateDingUserId(dept, cursor, size);
        }
        for (Long dept : deptIdList) {
            getDepartmentAndUpdateDingUserId(dept, cursor, size);
        }
    }

    public void getUserAndUpdateDingUserId(Long deptId, Long cursor, Long size) {
        OapiV2UserListResponse.PageResult pageResult = departmentUserInfo(deptId, cursor, size);
        log.info("user getUserAndUpdateDingUserId pageResult = {}", FastjsonUtil.toJson(pageResult));
        if (pageResult == null) {
            return;
        }
        List<OapiV2UserListResponse.ListUserResponse> list = pageResult.getList();
        for (OapiV2UserListResponse.ListUserResponse user : list) {
            String workNo = user.getJobNumber();
            if (StringUtils.isEmpty(workNo)) {
                log.error("user jobNumber is empty, user = {}", FastjsonUtil.toJson(user));
                continue;
            }
            //根据工号找empId
            log.info("user getUserAndUpdateDingUserId, workNo = {}", workNo);
            List<SysEmpInfoDto> empResult = empInfoRepository.getEmpInfoByWorkno(workNo);
            List<EmpInfoDto> empInfoList = FastjsonUtil.convertList(empResult, EmpInfoDto.class);
            if (empInfoList.isEmpty()) {
                log.error("user empInfoList is empty, workNo = {}", workNo);
            } else {
                val empId = empInfoList.get(0).getEmpid();
                userBaseInfoService.updateDingUserIdByEmpId(empId, user.getUserid());
            }
        }
        if (pageResult.getHasMore()) {
            getUserAndUpdateDingUserId(deptId, pageResult.getNextCursor(), size);
        }
    }

    /**
     * 查部门下的子部门
     */
    public OapiV2DepartmentListsubidResponse.DeptListSubIdResponse departmentListSubId(Long deptId) throws ApiException {
        log.info("user departmentListSubId deptId = {}", deptId);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsubid");
        OapiV2DepartmentListsubidRequest req = new OapiV2DepartmentListsubidRequest();
        req.setDeptId(deptId);
        Map<String, String> ssoToken = dingTalkFeignClient.getSsoToken(appKey, appSecret);

        String accessToken = ssoToken.get("access_token");
        OapiV2DepartmentListsubidResponse response = null;
        try {
            response = client.execute(req, accessToken);
            if (response != null && response.isSuccess()) {
                return response.getResult();
            }
        } catch (com.taobao.api.ApiException e) {
            log.error(e.getErrMsg(), e);

        }
        return null;
    }

    /**
     * 查部门下的所有人
     *
     * @throws ApiException
     */
    public OapiV2UserListResponse.PageResult departmentUserInfo(Long deptId, Long cursor, Long size) throws ApiException {
        log.info("user departmentUserInfo deptId = {}, cursor = {}, size = {}", deptId, cursor, size);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(deptId);
        req.setCursor(cursor);
        req.setSize(size);
        req.setOrderField("modify_desc");
        req.setContainAccessLimit(false);
        req.setLanguage("zh_CN");
        Map<String, String> ssoToken = dingTalkFeignClient.getSsoToken(appKey, appSecret);

        String accessToken = ssoToken.get("access_token");
        OapiV2UserListResponse response = null;
        try {
            response = client.execute(req, accessToken);
            log.info("user departmentUserInfo client.execute response = {}", FastjsonUtil.toJson(response));
            if(null == response){
                return null;
            }
            if (response.isSuccess()) {
                log.info("user departmentUserInfo response = {}", FastjsonUtil.toJson(response.getResult()));
                return response.getResult();
            } else if(!response.isSuccess() && null != response.getErrorCode()
                    && "88".equals(response.getErrorCode()) && null != response.getSubCode() && "90002".equals(response.getSubCode())){
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {}
                response = client.execute(req, accessToken);
                if (null != response && response.isSuccess()) {
                    log.info("--- find user departmentUserInfo response = {}", FastjsonUtil.toJson(response.getResult()));
                    return response.getResult();
                }
            }
        } catch (com.taobao.api.ApiException e) {
            log.error(e.getErrMsg(), e);
        }
        return null;
    }

}
