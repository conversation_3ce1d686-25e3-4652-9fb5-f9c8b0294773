package com.caidaocloud.user.service.infrastructure.repository;

import com.caidaocloud.user.service.infrastructure.repository.mongo.MongodbDao;
import com.caidaocloud.user.service.infrastructure.repository.po.UserPo;
import com.caidaocloud.user.service.domain.entity.User;
import com.caidaocloud.user.service.domain.repository.IUserRepository;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/4/2021 6:39 PM
 * 4
 */
@Repository
public class UserRepositoryImpl implements IUserRepository {
    private static final String collectionName = "basic_e_user_";

    @Autowired
    private MongodbDao<UserPo> mongodbDao;

    private String getCollectionName(String tenantId) {
        return collectionName + tenantId;
    }

    @Override
    public void save(List<User> list, String tenantId) {
        List<UserPo> poList = ObjectConverter.convertList(list, UserPo.class);
        mongodbDao.save(poList, this.getCollectionName(tenantId));
    }

    @Override
    public User getByAccount(String account, String tenantId) {
        BasicDBObject query = new BasicDBObject();
        query.put("account", account);
        List<UserPo> entities = this.query(query, tenantId);
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }

        return userPo2User(entities);
    }

    @Override
    public User getByUserId(long userId, String tenantId) {
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userId);
        List<UserPo> entities = this.query(query, tenantId);
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }

        return userPo2User(entities);
    }

    @Override
    public User getByTenantIdAndStaffid(String tenantId, String staffid) {
        if(StringUtil.isEmpty(tenantId) && StringUtil.isEmpty(staffid)){
            return null;
        }

        BasicDBObject query = new BasicDBObject();
        if (StringUtils.isNotEmpty(tenantId)) {
            query.put("tenantId", tenantId);
        }

        if(StringUtils.isNotEmpty(staffid)){
            query.put("staffid", staffid);
        }

        List<UserPo> entities = mongodbDao.query(query, collectionName + tenantId, UserPo.class);
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }

        return userPo2User(entities);
    }

    @Override
    public User updateClockType(User newUser) {
        BasicDBObject query = new BasicDBObject();
        query.put("tenantId", newUser.getTenantId());
        query.put("userid", newUser.getUserid());
        query.put("corpid", newUser.getCorpid());
        query.put("belongOrgId", newUser.getBelongOrgId());


        DBObject update= new BasicDBObject(
            "$set", new BasicDBObject("clockType", newUser.getClockType())
        );

        mongodbDao.update(query, update, false,false, collectionName + newUser.getTenantId());

        List<UserPo> entities = query(query, newUser.getTenantId());
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }

        return userPo2User(entities);
    }

    public List<UserPo> query(BasicDBObject query, String tenantId) {
        return mongodbDao.query(query, this.getCollectionName(tenantId), UserPo.class);
    }

    /**
     * UserPo 转 User
     * @param entities
     * @return
     */
    private User userPo2User(List<UserPo> entities){
        return ObjectConverter.convert(entities.get(0), User.class);
    }
}
