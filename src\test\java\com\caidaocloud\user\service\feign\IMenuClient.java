package com.caidaocloud.user.service.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @Security 注解针对Feign调用测试
 * <AUTHOR>
 * @date 2021-03-01
 */
@FeignClient(value = "caidaocloud-attendance-service", fallback = MenuClientFallBack.class, configuration = FeignConfiguration.class)
public interface IMenuClient {
    @PostMapping("/api/attendance/bluetooth/v1/list")
    Result getMenuList(@RequestBody KeywordBasePageDto search);
}
