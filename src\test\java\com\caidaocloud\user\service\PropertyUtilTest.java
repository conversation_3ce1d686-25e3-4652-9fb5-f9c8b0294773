package com.caidaocloud.user.service;

import com.caidaocloud.user.service.application.utils.PropertyUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class PropertyUtilTest {

    @Test
    public void test() {
        String defaultImportPassword = PropertyUtil.getProperty("caidaocloud.import.password:111111");
        System.out.println(defaultImportPassword);
    }

}
