package com.caidaocloud.user.service;

import com.caidaocloud.user.service.application.event.publish.UserEventPublish;
import com.caidaocloud.user.service.application.event.publish.enums.UserEventType;
import com.caidaocloud.user.service.application.event.publish.message.UserEventMsg;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * created by: FoAng
 * create time: 13/12/2022 2:21 下午
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class UserEventPublishTest {

    @Resource
    private UserEventPublish userEventPublish;

    @Test
    public void sendDeleteMsg() {
        UserEventMsg msg = new UserEventMsg();
        msg.setType(UserEventType.DELETE.getValue());
        UserBaseInfoDo userBaseInfoDo = new UserBaseInfoDo();
        userBaseInfoDo.setUserId(111111L);
        userBaseInfoDo.setEmpId(22222L);
        msg.setUserBaseInfoDo(userBaseInfoDo);
        userEventPublish.publishUserEventFanout(msg);
    }
}
