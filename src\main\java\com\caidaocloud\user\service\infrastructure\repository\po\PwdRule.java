package com.caidaocloud.user.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.user.service.domain.entity.AbstractBaseEntity;
import lombok.Data;

@Data
@TableName("pwd_rule")
public class PwdRule extends AbstractBaseEntity {
    /**
     * 密码规则ID
     */
    @TableId
    private Long pwdRuleId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 是否启用初始、重置密码生成规则
     */
    private Boolean isFirstRule;
    /**
     * 初始密码生成规则的复杂度:1、数字 2、大写字母 3、小写字母 4 特殊字符 5、手机号码后六位 6、身份证后六位
     */
    private String firstRule;
    /**
     * 初始密码生成规则的密码长度
     */
    private Integer firstLength;
    /**
     * 是否启用修改密码规则
     */
    private Boolean isValid;
    /**
     * 使用初始密码登陆后是否强制修改密码
     */
    private Boolean isPwdChange;
    /**
     * 密码长度（最小长度）
     */
    private Integer pwdLen1;
    /**
     * 密码长度（最大长度）
     */
    private Integer pwdLen2;
    /**
     * 密码复杂度：1.包含数字、2.大写字母、3.小写字母 4.特殊字符
     */
    private String pwdComplexity;
    /**
     * 不可与前多少次历史密码相同：自定义次数
     */
    private Integer notPwdSameNum;
    /**
     * 密码期限：自定义密码有效时长，单位为天
     */
    private Integer pwdValidTime;
    /**
     * 密码到期前提醒天数
     */
    private Integer pwdExpiresDay;
    /**
     * 登录时密码错误超过次数限制将被锁定：默认5次
     */
    private Integer lockAccountNum;
    /**
     * 超过锁定次数后的锁定时间：自定义密码锁定时间，单位为分钟
     */
    private Integer lockAccountTime;
    /**
     * 自动解锁时间，锁定用户时更新锁定时间到用户表上，在根据 锁定时间判断是否可解锁
     */
    private Integer autoUnlockTime;
    /**
     * 集团公司ID
     */
    private Long corpId;
}
