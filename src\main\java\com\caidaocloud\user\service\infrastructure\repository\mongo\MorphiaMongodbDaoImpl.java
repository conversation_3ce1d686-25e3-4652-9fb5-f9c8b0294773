package com.caidaocloud.user.service.infrastructure.repository.mongo;

import com.caidaocloud.user.service.infrastructure.config.mongo.MorphiaContext;
import com.mongodb.*;
import lombok.extern.slf4j.Slf4j;
import org.mongodb.morphia.Datastore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Repository
public class MorphiaMongodbDaoImpl<T> implements MongodbDao<T> {

    @Autowired
    private MorphiaContext morphiaContext;

    @Override
    public List<T> query(DBObject query, String collectionName, Class<T> t) {
        Datastore datastore = morphiaContext.getDatastore();
        DBCursor cursor = datastore.getDB().getCollection(collectionName).find(query);
        List<T> list = new ArrayList<T>();
        T item = null;
        while (cursor.hasNext()) {
            item = morphiaContext.getMorphia().fromDBObject(datastore, t, cursor.next());
            list.add(item);
        }
        return list;
    }

    @Override
    public List<T> queryAggregate(List<? extends DBObject> pipeline, String collectionName, Class<T> t) {
        Datastore datastore = morphiaContext.getDatastore();
        Cursor cursor = datastore.getDB().getCollection(collectionName).aggregate(pipeline, AggregationOptions.builder().build());
        List<T> list = new ArrayList<T>();
        T item = null;
        while (cursor.hasNext()) {
            item = morphiaContext.getMorphia().fromDBObject(datastore, t, cursor.next());
            list.add(item);
        }
        return list;
    }

    @Override
    public <E extends Object> List<E> query(DBObject query, DBObject projection, String collectionName, Class<E> t) {
        Datastore datastore = morphiaContext.getDatastore();
        DBCursor cursor = datastore.getDB().getCollection(collectionName).find(query, projection);
        List<E> list = new ArrayList<>();
        E item = null;
        while (cursor.hasNext()) {
            item = morphiaContext.getMorphia().fromDBObject(datastore, t, cursor.next());
            list.add(item);
        }
        return list;
    }

    @Override
    public void delete(DBObject query, String collectionName) {
        Datastore datastore = morphiaContext.getDatastore();
        datastore.getDB().getCollection(collectionName).remove(query);
    }

    @Override
    public void save(List<T> obj, String collectionName) {
        Datastore datastore = morphiaContext.getDatastore();
        List<DBObject> list = new ArrayList<DBObject>();
        log.info("开始保存数据至{}, 总共数量： {}", collectionName, obj.size());
        int batchSize = 10000;
        for (int i = 0, j = 0; i < obj.size(); i++, j++) {
            if (j == batchSize) {
                BatchPersistent(collectionName, datastore, list);
                list = new ArrayList<DBObject>();
                j = 0;
                log.info("开始保存数据至{}进行中, 已保存： {}", i * batchSize);
            }
            list.add(morphiaContext.getMorphia().toDBObject(obj.get(i)));
        }
        BatchPersistent(collectionName, datastore, list);
        log.info("结束保存数据至{},", collectionName);
    }

    @Override
    public void save(T obj, String collectionName) {
        Datastore datastore = morphiaContext.getDatastore();
        DBObject object = morphiaContext.getMorphia().toDBObject(obj);
        datastore.getDB().getCollection(collectionName).insert(object);
    }

    private void BatchPersistent(String collectionName, Datastore datastore, List<DBObject> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        StopWatch sw = new StopWatch();
        sw.start();
        datastore.getDB().getCollection(collectionName).insert(list);
        sw.stop();
        System.out.println(sw.getTotalTimeMillis());
    }

    /**
     * 分页查询
     *
     * @param query          查询条件
     * @param projection     字段筛选条件
     * @param collectionName 文档名称
     * @param pageNum        页码
     * @param pageSize       每页大小
     * @return
     */
    @Override
    public List<T> queryWithPaging(DBObject query, DBObject projection, String collectionName, Class<T> t, int pageNum, int pageSize) {
        List<T> list = new ArrayList<>();
        DBCursor dbcursor = null;
        try {
            Datastore datastore = morphiaContext.getDatastore();
            DBCollection dbCollection = datastore.getDB().getCollection(collectionName);
            if (projection == null) {
                dbcursor = dbCollection.find(query).skip((pageNum - 1) * pageSize).limit(pageSize);
            } else {
                dbcursor = dbCollection.find(query, projection).skip((pageNum - 1) * pageSize).limit(pageSize);
            }
            while (dbcursor.hasNext()) {
                list.add(morphiaContext.getMorphia().fromDBObject(datastore, t, dbcursor.next()));
            }
        } catch (Exception ex) {
            log.error(ex.toString());
        } finally {
            if (dbcursor != null) {
                dbcursor.close();
            }
        }
        return list;
    }


    @Override
    public void update(DBObject query, DBObject update, boolean upsert, boolean multi, String collectionName) {
        Datastore datastore = morphiaContext.getDatastore();
        datastore.getDB().getCollection(collectionName).update(query, update, upsert, multi);
    }

    @Override
    public void createIndex(DBObject keys, String collectionName) {
        Datastore datastore = morphiaContext.getDatastore();
        datastore.getDB().getCollection(collectionName).createIndex(keys);
    }

    @Override
    public void dropCollection(String collectionName) {
        Datastore datastore = morphiaContext.getDatastore();
        datastore.getDB().getCollection(collectionName).drop();
    }

    @Override
    public void createCollection(String collectionName) {
        Datastore datastore = morphiaContext.getDatastore();
        datastore.getDB().createCollection(collectionName, new BasicDBObject());
    }

    @Override
    public int queryCount(DBObject query, String collectionName) {
        Datastore datastore = morphiaContext.getDatastore();
        DBCursor cursor = datastore.getDB().getCollection(collectionName).find(query);
        return cursor.count();
    }

    @Override
    public boolean collectionExisting(String collectionName) {
        Datastore datastore = morphiaContext.getDatastore();
        long count = datastore.getDB().getCollection(collectionName).count();
        return count > 0;
    }
}
