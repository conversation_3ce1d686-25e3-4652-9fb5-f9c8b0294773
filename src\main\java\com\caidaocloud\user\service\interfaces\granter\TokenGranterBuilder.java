package com.caidaocloud.user.service.interfaces.granter;

import com.caidaocloud.util.SpringUtil;
import lombok.AllArgsConstructor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@AllArgsConstructor
public class TokenGranterBuilder {
    /**
     * TokenGranter缓存池
     */
    private static Map<GrantType, ITokenGranter> granterPool = new ConcurrentHashMap<>();

    static {
        granterPool.put(GrantType.PASSWORD_TOKEN_GRANT_TYPE, SpringUtil.getBean(PasswordTokenGranter.class));
        granterPool.put(GrantType.MOBILE_CODE_TOKEN_GRANT_TYPE, SpringUtil.getBean(MobileCodeTokenGranter.class));
        granterPool.put(GrantType.WX_SCAN_CODE_TOKEN_GRANT_TYPE, SpringUtil.getBean(WxScanCodeTokenGranter.class));
        granterPool.put(GrantType.ALIPAY_SCAN_CODE_TOKEN_GRANT_TYPE, SpringUtil.getBean(AlipayScanCodeTokenGranter.class));
        granterPool.put(GrantType.SSO_TOKEN_GRANT_TYPE, SpringUtil.getBean(SsoTokenGranter.class));
        granterPool.put(GrantType.OPEN_CLOUD_GRANT_TYPE, SpringUtil.getBean(OpenCloudTokenGranter.class));
    }

    /**
     * 获取TokenGranter
     * @param loginType 授权类型
     * @return ITokenGranter
     */
    public static ITokenGranter getGranter(Integer loginType) {
        GrantType grantType = GrantType.valueByType(loginType);
        ITokenGranter tokenGranter = granterPool.get(grantType);
        if(null == tokenGranter){
            // 默认密码模式
            tokenGranter = granterPool.get(GrantType.PASSWORD_TOKEN_GRANT_TYPE);
        }
        return tokenGranter;
    }

    public static ITokenGranter getGranter(GrantType grantType) {
        ITokenGranter tokenGranter = granterPool.get(grantType);
        if(null == tokenGranter){
            // 默认密码模式
            tokenGranter = granterPool.get(GrantType.PASSWORD_TOKEN_GRANT_TYPE);
        }
        return tokenGranter;
    }
}
