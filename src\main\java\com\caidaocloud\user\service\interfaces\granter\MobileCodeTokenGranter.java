package com.caidaocloud.user.service.interfaces.granter;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.service.UserAppService;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.vo.AccountLoginVo;
import com.caidaocloud.web.Result;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 1:为手机验证码登录
 *
 * <AUTHOR>
 * @date 2020-01-15
 */
@Component
public class MobileCodeTokenGranter extends AbstractTokenGranter {


    @Override
    public Result grant(LoginDto loginDto) {
        return null;
    }



}
