package com.caidaocloud.user.service.domain.entity;

import com.caidaocloud.user.service.domain.enums.AccountType;
import com.caidaocloud.user.service.domain.repository.IAccountPayslipInfoRepository;
import com.caidaocloud.user.service.domain.util.ListUtil;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
@Data
public class AccountPayslipInfoDo extends BaseEntity {
    /**
     * 账号ID
     */
    private Long accountId;
    /**
     * 使用用户统一注册账号登录时的前缀，例如：caidao-，登录时输入：前缀-账号进行登录
     */
    private String accountLoginPrefix;
    /**
     * 用户统一注册账号
     */
    private String account;
    /**
     * 注册手机号
     */
    private String mobNum;
    /**
     * 注册邮箱
     */
    private String email;
    /**
     * 密码
     */
    private String password;
    /**
     * 盐值
     */
    private String salt;
    /**
     * 手势密码
     */
    private String gesture;
    /**
     * 账号状态：1 正常 2 停用 3 锁定
     */
    private Integer status;
    /**
     * 注册方式
     */
    private String regType;

    @Autowired
    private IAccountPayslipInfoRepository iAccountPayslipInfoRepository;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public List<AccountPayslipInfoDo> getList(String account, AccountType accountType) {
        return iAccountPayslipInfoRepository.selectList(account, accountType);
    }

    public void deleteByIds(List<Long> accountIds) {
        iAccountPayslipInfoRepository.deleteByIds(accountIds);
    }

    public void softDeleteByIds(List<Long> accountIds) {
        iAccountPayslipInfoRepository.softDeleteByIds(accountIds);
    }

    public Long syncSave(AccountPayslipInfoDo data) {
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(data);
        if (data.getAccountId() == null) {
            data.setAccountId(snowflakeUtil.createId());
            iAccountPayslipInfoRepository.insert(data);
        } else {
            iAccountPayslipInfoRepository.update(data);
        }
        return data.getAccountId();
    }

    public Long syncInsert(AccountPayslipInfoDo data) {
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(data);
        iAccountPayslipInfoRepository.insert(data);
        return data.getAccountId();
    }

    @Transactional
    public void syncSave(List<AccountPayslipInfoDo> dataList) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        for (AccountPayslipInfoDo data : dataList) {
            if (data.getAccountId() == null) {
                data.setAccountId(snowflakeUtil.createId());
            }
        }
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(dataList);
        // 数据保存
        List<List<AccountPayslipInfoDo>> lists = ListUtil.split(dataList, 500);
        for (List<AccountPayslipInfoDo> list : lists) {
            iAccountPayslipInfoRepository.insertBatch(list);
        }
    }

    @Transactional
    public void syncUpdate(List<AccountPayslipInfoDo> dataList) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(dataList);
        // 数据保存
        List<List<AccountPayslipInfoDo>> lists = ListUtil.split(dataList, 500);
        for (List<AccountPayslipInfoDo> list : lists) {
            iAccountPayslipInfoRepository.updateBatch(list);
        }
    }

    public List<AccountPayslipInfoDo> getListByIds(List<Long> accountIds) {
        return iAccountPayslipInfoRepository.getListByIds(accountIds);
    }

    public AccountPayslipInfoDo getById(Long accountId) {
        return iAccountPayslipInfoRepository.getById(accountId);
    }

    public void updateById(AccountPayslipInfoDo data) {
        iAccountPayslipInfoRepository.update(data);
    }

    public void updateByPrimaryKey(AccountPayslipInfoDo data) {
        iAccountPayslipInfoRepository.updateByPrimaryKeySelective(data);
    }
}
