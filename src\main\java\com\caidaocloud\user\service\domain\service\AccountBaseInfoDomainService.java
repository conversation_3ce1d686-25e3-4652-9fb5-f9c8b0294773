package com.caidaocloud.user.service.domain.service;

import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.enums.AccountType;
import com.caidaocloud.user.service.domain.repository.IAccountBaseInfoRepository;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.infrastructure.util.RegexUtil;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AccountBaseInfoDomainService {
    @Autowired
    private AccountBaseInfoDo accountBaseInfoDo;

    @Resource
    private IAccountBaseInfoRepository accountBaseInfoRepository;

    @Resource
    private PasswordHelper passwordHelper;

    public List<AccountBaseInfoDo> getAndCheckAccountList(String account, GrantType grantType) {
        List<AccountBaseInfoDo> accountList = getAccountInfoList(account);
        if (CollectionUtils.isEmpty(accountList)) {
            if (grantType == GrantType.MOBILE_CODE_TOKEN_GRANT_TYPE) {
                // 手机号+验证码登录
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.MOBILE_NUMBER_NOT_REGISTERED));
            } else if (grantType == GrantType.ONE_CLICK_LOGIN_TYPE || grantType == GrantType.LOCAL_NUMBER_VERIFICATION_TYPE) {
                // 一键登录、本机号码验证
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.MOBILE_NUMBER_NOT_REGISTERED));
            } else {
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ACCOUNT_NOT_EXIST));
            }
        }

        return accountList;
    }

    public List<AccountBaseInfoDo> getAndCheckNormalAccountList(String account, GrantType grantType) {
        List<AccountBaseInfoDo> accountList = getAccountInfoList(account);
        if (CollectionUtils.isEmpty(accountList)) {
            if (grantType == GrantType.MOBILE_CODE_TOKEN_GRANT_TYPE) {
                // 手机号+验证码登录
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.MOBILE_NUMBER_NOT_REGISTERED));
            } else if (grantType == GrantType.ONE_CLICK_LOGIN_TYPE || grantType == GrantType.LOCAL_NUMBER_VERIFICATION_TYPE) {
                // 一键登录、本机号码验证
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.MOBILE_NUMBER_NOT_REGISTERED));
            } else {
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ACCOUNT_NOT_EXIST));
            }
        }
        // 过滤掉停用、锁定的账号
        List<AccountBaseInfoDo> normalAccountList = accountList.stream().
                filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).collect(Collectors.toList());
        PreCheck.preCheckArgument(CollectionUtils.isEmpty(normalAccountList), LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));

        return normalAccountList;
    }

    public List<AccountBaseInfoDo> getAccountInfoList(String account) {
        if (RegexUtil.isMatchEmail(account)) {
            return accountBaseInfoDo.getList(account, AccountType.EMAIL);
        }

        String [] split = account.split("\\+");
        String code = split.length > 1 ? "+" + split[1] : "";
        if (!"".equals(code) || RegexUtil.isMatchMobile(split[0])) {
            return accountBaseInfoDo.getList(split[0], AccountType.MOBILE);
        } else {
            return accountBaseInfoDo.getList(account, AccountType.ACCOUNT);
        }
    }

    public AccountBaseInfoDo getAccountInfo(String account) {
        List<AccountBaseInfoDo> list = getAccountInfoList(account);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    public Long syncSave(AccountBaseInfoDo data) {
        return accountBaseInfoDo.syncSave(data);
    }

    public List<AccountBaseInfoDo> getListByIds(List<Long> accountIds) {
        return accountBaseInfoDo.getListByIds(accountIds);
    }

    public AccountBaseInfoDo getById(Long accountId) {
        return accountBaseInfoDo.getById(accountId);
    }

    public void softDeleteByIds(List<Long> accountIds) {
        accountBaseInfoDo.softDeleteByIds(accountIds);
    }

    public List<AccountBaseInfoDo> getAccount(String mobNum, String email) {
        return accountBaseInfoRepository.selectAccountByMobNumAndEmail(mobNum, email);
    }

    public AccountBaseInfoDo getAccountByAccountId(Long accountId) {
        return accountBaseInfoRepository.getAccountByAccountId(accountId);
    }

    public void updateById(AccountBaseInfoDo data) {
        accountBaseInfoDo.updateById(data);
    }

    public void updateByPrimaryKey(AccountBaseInfoDo data) {
        accountBaseInfoDo.updateByPrimaryKey(data);
    }

    public void syncSave(List<AccountBaseInfoDo> dataList) throws Exception {
        accountBaseInfoDo.syncSave(dataList);
    }

    public void syncUpdate(List<AccountBaseInfoDo> dataList) throws Exception {
        accountBaseInfoDo.syncUpdate(dataList);
    }

    public List<AccountBaseInfoDo> getAccountByMobNumOrEmail(List<String> mobNums,List<String> emails) {
        return accountBaseInfoRepository.getAccountByMobNumOrEmail(mobNums,emails);
    }

    public List<AccountBaseInfoDo> getAccountByMobNums(List<String> mobNums) {
        return accountBaseInfoRepository.getAccountByMobNums(mobNums);
    }

    public void changePassword(String password, Long userId, AccountBaseInfoDo accountBaseInfoData) {
        checkPassword(password);
        // 修改密码
        accountBaseInfoData.setSalt(passwordHelper.createSalt());
        accountBaseInfoData.setPassword(passwordHelper.encode(password, accountBaseInfoData.getSalt()));
        accountBaseInfoData.setUpdateBy(userId);
        accountBaseInfoData.setUpdateTime(System.currentTimeMillis());
        updateById(accountBaseInfoData);
    }

    private void checkPassword(String password) {
        if (password.length()<6) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.password.length.short"));
        }
        if (password.length()>12){
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.password.length.long"));
        }
        if (!PasswordHelper.PasswordGenerator.validateSimplePassword(password)) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.password.valid"));
        }
    }
}
