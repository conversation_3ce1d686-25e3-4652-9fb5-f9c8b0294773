package com.caidaocloud.user.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 重置密码
 * created by: FoAng
 * create time: 3/3/2025 4:30 下午
 */
@Data
public class ResetPasswordDto {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("新密码")
    private String newPwd;

    @ApiModelProperty("密码设置类型，0：自动生成，1：手动输入")
    private int resetType;

    @ApiModelProperty("是否发送消息通知")
    private boolean notify;

    @ApiModelProperty("用户类型， 0:员工，1:入职")
    private int userType;

}
