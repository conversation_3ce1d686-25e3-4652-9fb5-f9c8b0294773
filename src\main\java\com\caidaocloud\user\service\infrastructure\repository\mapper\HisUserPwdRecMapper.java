package com.caidaocloud.user.service.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.HisUserPwdRec;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HisUserPwdRecMapper extends BaseMapper<HisUserPwdRec> {
    int insertSelective(HisUserPwdRec record);

    int updateByPrimaryKeySelective(HisUserPwdRec record);

    int insertBatch(@Param("records") List<HisUserPwdRec> records);
}
