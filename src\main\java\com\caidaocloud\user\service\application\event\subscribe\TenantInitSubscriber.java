package com.caidaocloud.user.service.application.event.subscribe;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.service.TenantBaseInfoService;
import com.caidaocloud.user.service.infrastructure.util.UserContext;
import com.caidaocloud.user.service.interfaces.dto.TenantInitDto;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2022/12/16
 */
@Slf4j
@Component
public class TenantInitSubscriber {

	@Autowired
	private TenantBaseInfoService tenantBaseInfoService;

	@RabbitHandler
	@RabbitListener(
			bindings = @QueueBinding(
					value = @Queue(value = "user.tenant.init.queue", durable = "true"),
					exchange = @Exchange(value = "maintenance.tenant.init.fanout.exchange", type = ExchangeTypes.FANOUT),
					key = {"routingKey.maintenance.tenant.init"}
			)
	)
	public void process(String message) {
		log.info("Tenant init message={}", message);
		try {
			TenantInitDto dto = FastjsonUtil.toObject(message, TenantInitDto.class);
			SecurityUserInfo userInfo = new SecurityUserInfo();
			userInfo.setTenantId(dto.getTenantId());
			userInfo.setEmpId(0L);
			userInfo.setUserId(0L);
			SecurityUserUtil.setSecurityUserInfo(userInfo);
			UserInfo user = new UserInfo();
			user.setTenantId(dto.getTenantId());
			user.setStaffId(0L);
			user.setUserid(0);
			user.setEmpid(0);
			UserContext.setCurrentUser(user);

			tenantBaseInfoService.initTenant(dto.getTenantId(), dto.getTenantName(), dto.getCode());
		} catch (Exception ex) {
			log.error("process tenantInitMessage err,{}", ex.getMessage(), ex);
		} finally {
			SecurityUserUtil.removeSecurityUserInfo();
			UserContext.remove();
		}
	}

}
