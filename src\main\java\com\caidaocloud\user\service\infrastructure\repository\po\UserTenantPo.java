package com.caidaocloud.user.service.infrastructure.repository.po;

import lombok.Data;
import org.mongodb.morphia.annotations.*;
import org.springframework.stereotype.Component;

/**
 * @Author: Max
 * @Desc: 用户与租户关联集合
 * @Date: 1/8/2021 1:29 PM
 * 4
 */
@Entity(value = "user_tenant", noClassnameStored = true)
@Indexes(@Index(fields = @Field("id")))
@Component
@Data
public class UserTenantPo {
    @Id
    private String id;
    private long userId;
    private String userAccount;
    private String tenantId;
    private long createdTime;
    private long updatedTime;
}
