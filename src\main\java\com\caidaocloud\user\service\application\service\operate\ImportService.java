package com.caidaocloud.user.service.application.service.operate;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.constant.KeyConstant;
import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.user.service.application.enums.ImportOperation;
import com.caidaocloud.user.service.application.service.operate.impl.*;
import com.caidaocloud.user.service.application.utils.MessgeToCacheUtil;
import com.caidaocloud.user.service.infrastructure.config.thread.ThreadPoolExector;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.caidaocloud.vo.ImportExcelVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/18
 **/
@Service
@Slf4j
public class ImportService {

    @Autowired
    private ThreadPoolExector threadPoolExector;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private ISessionService userService;

    public ImportExcelVo importUser(MultipartFile file, ImportOperation type) {
        try {
            checkFile(file);
            ImportExcelVo importExcelVo = new ImportExcelVo();
            var importParams = new ImportParams();
            importParams.setHeadRows(1);
            importParams.setTitleRows(1);
            ExcelImportResult<UserImportDto> importResult = ExcelImportUtil.importExcelMore(file.getInputStream(), UserImportDto.class, importParams);
            var list = importResult.getList();
            String proceInstId = importUser(list,type);
            importExcelVo.setProcessUUid(proceInstId);
            return importExcelVo;
        } catch (Exception e) {
            log.error("import excel fail, {}", e);
            throw new ServerException("import excel fail");
        }
    }

    /**
     * 检查是否是xls格式的文件
     *
     * @param
     * @return
     */
    private void checkFile(MultipartFile file) {
        if (file == null) {
            throw new ServerException("illegal parameter, parameter is null");
        }
        var fileName = file.getOriginalFilename();
        var isLegalName = StringUtils.isNotBlank(fileName) &&
                fileName.lastIndexOf(".") != -1 &&
                !".xls".equalsIgnoreCase(fileName.substring(fileName.lastIndexOf("."))) &&
                !".xlsx".equalsIgnoreCase(fileName.substring(fileName.lastIndexOf(".")));
        if (isLegalName) {
            throw new ServerException("formats other than xls and xlsx no supported");
        }
    }

    /**
     * 导出导入失败的错误信息
     *
     * @param response
     * @param processId
     */
    public void exportErrorExcel(HttpServletResponse response, String processId) {
        String errorKey = KeyConstant.getImportErrorKey(processId);
        List<Object> list = cacheService.getList(errorKey, 0, -1);
        if (!CollectionUtils.isEmpty(list)) {
            String json = FastjsonUtil.toJson(list);
            json = json.replaceAll("\"\\{", "{").replaceAll("}\"", "}").replaceAll("\\\\", "");
            List<UserImportErrorDto> errorList = FastjsonUtil.toArrayList(json, UserImportErrorDto.class);
            ExportParams exportParams = new ExportParams();
            Workbook exportExcel = ExcelExportUtil.exportExcel(exportParams, UserImportErrorDto.class, errorList);
            try {
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("用户导入错误信息.xlsx", "UTF-8"));
                exportExcel.write(response.getOutputStream());
            } catch (Exception e) {
                log.error("export excel occur error, {}", e);
            }
        }
    }

    private String importUser(List<UserImportDto> userImportDtoList, ImportOperation type) {
        if (CollectionUtils.isEmpty(userImportDtoList)) {
            throw new ServerException("data is empty");
        }
        Long currentTimeMillis = System.currentTimeMillis();
        String processId = currentTimeMillis.toString();
        UserInfo userInfo = userService.getUserInfo();
        if (userInfo == null) {
            throw new ServerException("Login invalid");
        }
        Long userId = userInfo.getUserId();
        String tenantId = userInfo.getTenantId();
        String role = SecurityUserUtil.getSecurityUserInfo().getRole();
        ImportExcelProcessDto processDto = new ImportExcelProcessDto();
        processDto.setProcessUUid(processId);
        processDto.setTotal(userImportDtoList.size());
        processDto.setCompleted(0);
        String processKey = KeyConstant.getImportProcessKey(processId);
        cacheService.cacheValue(processKey, FastjsonUtil.toJson(processDto), 300);
        threadPoolExector.getThreadPool()
                .execute(new ImportHandler(userImportDtoList, processId, tenantId, userId, role,type));
        return processId;
    }

    public ImportExcelProcessVo getImportDataPercentage(String processId) {
        String processKey = KeyConstant.getImportProcessKey(processId);
        String value = cacheService.getValue(processKey);
        ImportExcelProcessVo vo = FastjsonUtil.toObject(value, ImportExcelProcessVo.class);
        return vo;
    }

    protected class ImportHandler implements Runnable {

        private final List<UserImportDto> userList;

        private final String REDIS_ERROR_MSG_KEY;

        private final String processId;

        private final Integer totalSize;

        private final String tenantId;

        private final Long userId;

        private final String role;

        private final  AbstarctImportOperationLink authHandler;

        public ImportHandler(List<UserImportDto> userImportDtoList, String processId, String tenantId, Long userId, String role, ImportOperation type) {
            this.totalSize = userImportDtoList.size();
            this.userList = userImportDtoList;
            this.REDIS_ERROR_MSG_KEY = String.format("%s%s", processId, KeyConstant.IMPORT_USER_ERROR_KEY);
            this.processId = processId;
            this.tenantId = tenantId;
            this.userId = userId;
            this.role = role;
            authHandler = type == ImportOperation.UPDATE ? new UpdateAuthorizationHandler(processId) : new ImportAuthorization(processId);
        }

        @Override
        public void run() {
            try {
                SecurityUserInfo userInfo = new SecurityUserInfo();
                userInfo.setTenantId(tenantId);
                userInfo.setUserId(userId);
                userInfo.setRole(role);
                SecurityUserUtil.setSecurityUserInfo(userInfo);
                ImportExcelProcessDto processDto = new ImportExcelProcessDto();
                processDto.setProcessUUid(processId);
                processDto.setTotal(totalSize);
                int failed = 0;
                int success = 0;
                int completedCount = 0;
                List<UserImportErrorDto> errorList = Lists.newArrayList();
                for(UserImportDto importUser : userList){
                    val importOperationLink = new ImportCheckMustFiled(processId)
                            .appendNext(new ImportCheckAccountIsExist(processId)
                                    .appendNext(new ImportCheckEmpInfo(processId)
                                            .appendNext(new ImportCreateAccount(processId)
                                                    .appendNext(authHandler))));

                    val error = FastjsonUtil.convertObject(importUser, UserImportErrorDto.class);
                    try {
                        SpringUtil.getBean(ImportService.class)
                                .importOne(importOperationLink, importUser, processDto, error);
                        success++;
                    } catch (Exception e) {
                        log.error("import account occur error, [{}]", e);
                        errorList.add(error);
                        failed++;
                    } finally {
                        completedCount++;
                        processDto.setSuccessCount(success);
                        processDto.setFailCount(failed);
                        processDto.setCompleted(completedCount);
                        String processKey = KeyConstant.getImportProcessKey(processId);
                        cacheService.cacheValue(processKey, FastjsonUtil.toJson(processDto), 300);
                    }
                }
                MessgeToCacheUtil.setMessageToCache(KeyConstant.getImportErrorKey(processId), FastjsonUtil.convertList(errorList, String.class), 1800);
            } finally {
                SecurityUserUtil.removeSecurityUserInfo();
            }
        }
    }

    @Transactional
    public void importOne(AbstarctImportOperationLink importOperationLink, UserImportDto importUser,ImportExcelProcessDto processDto, UserImportErrorDto error){
        importOperationLink.operate(importUser, processDto, error);
    }

}
