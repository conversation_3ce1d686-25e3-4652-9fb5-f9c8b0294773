package com.caidaocloud.user.service.application.event.publish;

import com.caidaocloud.mq.rabbitmq.MqMessageProducer;
import com.caidaocloud.user.service.application.event.publish.message.UserEventMsg;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * created by: FoAng
 * create time: 13/12/2022 1:45 下午
 */
@Slf4j
@Component
public class UserEventPublish {

    public static final String USER_EVENT_EXCHANGE = "message.user.fanout.exchange";

    @Resource
    private MqMessageProducer<UserEventMsg> producer;

    /**
     * 发送更新消息
     * @param msg
     */
    public void publishUserEventFanout(UserEventMsg msg) {
        msg.setBody(FastjsonUtil.toJson(msg));
        msg.setExchange(USER_EVENT_EXCHANGE);
        log.info("user info update send msg:{}", FastjsonUtil.toJson(msg));
        producer.publish(msg);
    }
}
