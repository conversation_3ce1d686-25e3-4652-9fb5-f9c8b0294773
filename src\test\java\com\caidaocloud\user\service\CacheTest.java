package com.caidaocloud.user.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.user.service.application.constant.KeyConstant;
import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.user.service.application.utils.MessgeToCacheUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class CacheTest {

    @Autowired
    private CacheService cacheService;

    @Test
    public void test() {
        UserImportErrorDto errorDto = new UserImportErrorDto();
        errorDto.setName("test1");
        errorDto.setWorkno("test1");
        errorDto.setRoles("test1");
        errorDto.setMobile("test1");
        errorDto.setEmail("test1");
        errorDto.setErrorMsg("test1");

        UserImportErrorDto errorDto2 = new UserImportErrorDto();
        errorDto2.setName("test2");
        errorDto2.setWorkno("test2");
        errorDto2.setRoles("test2");
        errorDto2.setMobile("test2");
        errorDto2.setEmail("test2");
        errorDto2.setErrorMsg("test2");

        ArrayList<UserImportErrorDto> list = Lists.newArrayList(errorDto, errorDto2);

        String key = String.format("%s%s", KeyConstant.IMPORT_USER_ERROR_KEY, 1);
        List<String> errors = FastjsonUtil.convertList(list, String.class);

        MessgeToCacheUtil.setMessageToCache(key, errors, 300);
    }

    @Test
    public void getCacheList() {
        String key = String.format("%s%s", KeyConstant.IMPORT_USER_ERROR_KEY, 1);
        List<Object> list = cacheService.getList(key, 0, -1);
        if (!CollectionUtils.isEmpty(list)) {
            String json = FastjsonUtil.toJson(list);
            json = json.replaceAll("\"\\{", "{").replaceAll("}\"", "}").replaceAll("\\\\", "");
            List<UserImportErrorDto> errorList = FastjsonUtil.toArrayList(json, UserImportErrorDto.class);
            for (UserImportErrorDto errorDto : errorList) {
                System.out.println(errorDto);
            }
        }
    }

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Test
    public void test3() throws InterruptedException {
        new Thread(() -> {
            redisTemplate.execute(new SessionCallback<Object>() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                String key = "zoro";
                operations.watch(key);
                operations.multi();
                ImportExcelProcessDto dto = new ImportExcelProcessDto();
                dto.setSuccessCount(2);
                dto.setTotal(2);
                dto.setCompleted(2);
                dto.setProcessUUid("1653565791191");
                dto.setNotDone(0);
                String json = FastjsonUtil.toJson(dto);
                ValueOperations operate = operations.opsForValue();
                System.out.println(json);
                operate.set(key, json, 300 , TimeUnit.SECONDS);
                return operations.exec();
            }
        });}).start();
        Thread.sleep(10000L);

    }

}
