package com.caidaocloud.user.service.infrastructure.config.mybatis;


import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.caidaocloud.user.service.infrastructure.config.mybatis.handler.BooleanTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.TypeHandlerRegistry;

import org.springframework.context.annotation.Configuration;

/**
 * mybatis配置
 *
 * <AUTHOR>
 * @date 2022/5/7
 **/
@Configuration
@Slf4j
public class MybatisConfig implements ConfigurationCustomizer {

	@Override
	public void customize(org.apache.ibatis.session.Configuration configuration) {
		TypeHandlerRegistry registry = configuration.getTypeHandlerRegistry();
		registry.register(Boolean.class, BooleanTypeHandler.class);
	}
}
