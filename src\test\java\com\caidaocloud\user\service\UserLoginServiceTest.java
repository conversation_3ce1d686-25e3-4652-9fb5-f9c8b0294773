package com.caidaocloud.user.service;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.user.service.application.dto.LoginConfigDto;
import com.caidaocloud.user.service.application.service.UserLoginService;
import com.caidaocloud.user.service.domain.entity.User;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;

import java.util.HashMap;
import java.util.Map;

public class UserLoginServiceTest {
    public static void main(String[] args) {
        String str = "{\"msg\":\"success\",\"code\":0,\"success\":true,\"serverTime\":1622439917934}";
        System.out.println(str.indexOf("\"success\":true") < 0);


        LoginConfigDto loginConfigDto = new LoginConfigDto();
        loginConfigDto.setLoginUrl("http://dev.caidaocloud.com/api/career/user/v1/login");
        loginConfigDto.setLoginType("http");

        // 请求头部参数
        Map headerMap = new HashMap<>();
        headerMap.put("key", "aaaaa");
        headerMap.put("secret", "bbb");
        headerMap.put("Content-Type", "application/json");

        loginConfigDto.setHeaders(headerMap);

        // 请求参数
        Map params = new HashMap<>();
        params.put("account", "#login.account");
        params.put("password", "#login.password");
        Map extMap = new HashMap();
        extMap.put("userId", "#user.userid");
        params.put("extMap", extMap);
        //params.put("extMap", "{\"userId\": #user.userid}");
        params.put("terminal", "0");

        loginConfigDto.setParams(params);

        loginConfigDto.setSuccessMark("\"success\":true");

        System.out.println(JSON.toJSONString(loginConfigDto));

        System.out.println("---------------------");
        UserLoginService httpLoginService = new UserLoginService();

        LoginDto loginDto = new LoginDto();
        loginDto.setAccount("CI17872");
        loginDto.setPassword("password");

        User dbUser = new User();
        dbUser.setUserid(35113);
        String logConfig = JSON.toJSONString(loginConfigDto);
        //httpLoginService.autoLogin(logConfig, loginDto, dbUser);
    }
}
