package com.caidaocloud.user.service.interfaces.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/5/2021 3:10 PM
 */
@Data
public class LoginDto {
    @ApiModelProperty(value = "用户账号", required = true)
    private String account;

    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "登录平台，0为web端，1为移动端, 2 第三方为web端，3 为第三方移动端")
    private Integer loginPlatform = 0;

    @ApiModelProperty(value = "登录方式，0为账号密码登录，1:为手机验证码登录,2为微信扫描登录，3为支付宝扫码登录，4为腾讯sso登录")
    private Integer loginType = 0;

    @ApiModelProperty(value = "登录设备类型web:0 android:1,ios:2")
    private Integer deviceType = 0;

    /**
     * 第三方平台编码
     */
    @ApiModelProperty(value = "第三方平台编码")
    private String thirdPart;

    /**
     * 第三方数据ID
     */
    @ApiModelProperty(value = "第三方数据ID")
    private String thirdId;

    /**
     * 扩展备用字段map
     */
    @ApiModelProperty(value = "扩展备用字段")
    private Map extMap;

}
