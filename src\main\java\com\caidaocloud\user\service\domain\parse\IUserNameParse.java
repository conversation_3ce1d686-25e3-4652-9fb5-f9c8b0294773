package com.caidaocloud.user.service.domain.parse;

import com.caidaocloud.record.core.service.IParseFunction;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * created by: FoAng
 * create time: 25/7/2024 10:23 上午
 */
@Slf4j
@Service
@AllArgsConstructor
public class IUserNameParse implements IParseFunction {

    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Override
    public String functionName() {
        return "name";
    }

    @Override
    public boolean executeBefore() {
        return true;
    }

    @Override
    public String apply(String accountId) {
        return Optional.ofNullable(accountId).map(it -> {
            String userId = it.contains(",") ? StringUtils.split(",")[0] : it;
            List<UserBaseInfoDo> userList = userBaseInfoDomainService.getUserListByAccountId(Long.valueOf(userId));
            return CollectionUtils.isNotEmpty(userList) ? userList.get(0).getUserName() : null;
        }).orElse("");
    }
}
