package com.caidaocloud.user.service.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.TenantBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TenantBaseInfoMapper extends BaseMapper<TenantBaseInfo> {
    int insertSelective(TenantBaseInfo record);

    int updateByPrimaryKeySelective(TenantBaseInfo record);

    int insertBatch(@Param("records") List<TenantBaseInfo> records);
}
