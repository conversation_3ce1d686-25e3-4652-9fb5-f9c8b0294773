package com.caidaocloud.user.service.application.service.operate.impl;

import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.dto.operate.*;
import com.caidaocloud.user.service.application.exception.ImportException;
import com.caidaocloud.user.service.application.feign.IAuthFeign;
import com.caidaocloud.user.service.application.service.operate.AbstarctImportOperationLink;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * 导入账户授权
 *
 * <AUTHOR>
 * @date 2022/5/20
 **/
@Slf4j
public class ImportAuthorization extends AbstarctImportOperationLink {

    private IAuthFeign authFeign;

    private RedisTemplate<String, Object> redisTemplate;

    public ImportAuthorization(String processId) {
        super(processId);
        this.authFeign = SpringUtil.getBean(IAuthFeign.class);
        this.redisTemplate = SpringUtil.getBean("redisTemplate", RedisTemplate.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public void operate(UserImportDto importUser, ImportExcelProcessDto processDto, UserImportErrorDto error) {
        if(StringUtils.isNotEmpty(importUser.getRoles())){
            var authImportDto = new AuthImportDto();
            authImportDto.setSubjectId(importUser.getUserId());
            authImportDto.setRoleName(importUser.getRoles());
            Result<List<AuthImportMsgDto>> result = null;
            try {
                result = authFeign.authorizationToUser(Lists.list(authImportDto));
                if (result.isSuccess() && !CollectionUtils.isEmpty(result.getData())) {
                    Optional<AuthImportMsgDto> errorMsgDto = result.getData().stream()
                            .filter(it->it.getSubjectId().equals(importUser.getUserId())).findFirst();
                    if(errorMsgDto.isPresent()){
                        String msg = errorMsgDto.get().getErrorMsg();
                        error.setErrorMsg(msg);
                        throw new ImportException(msg);
                    }
                }else if (!result.isSuccess()) {
                    log.error("request auth server fail");
                    var msg = "";
                    if (StringUtils.isNotBlank(result.getMsg())) {
                        msg = result.getMsg();
                    } else {
                        msg = LangUtil.getMsg(MsgCodeConstant.REQUEST_AUTH_ERROR);
                    }
                    error.setErrorMsg(msg);
                    throw new ImportException(msg);
                }
            } catch (Exception e) {
                if(e instanceof ImportException){
                    throw e;
                }else{
                    log.error("导入权限异常", e);
                    var msg = "";
                    if (result != null && !result.isSuccess()) {
                        msg = result.getMsg();
                    } else {
                        msg = LangUtil.getMsg(MsgCodeConstant.REQUEST_AUTH_ERROR);
                    }
                    error.setErrorMsg(msg);
                    throw new ImportException(msg);
                }
            }
        }



    }

}
