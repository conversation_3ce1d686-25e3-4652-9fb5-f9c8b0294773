package com.caidaocloud.user.service.application.service;

import com.caidaocloud.user.service.domain.entity.PwdRuleDo;
import com.caidaocloud.user.service.domain.service.PwdRuleDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class PwdRuleService {
    @Autowired
    private PwdRuleDomainService pwdRuleDomainService;

    @Transactional
    public void syncSave(List<PwdRuleDo> dataList) throws Exception {
        pwdRuleDomainService.syncSave(dataList);
    }
}
