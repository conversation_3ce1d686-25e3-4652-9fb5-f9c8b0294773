package com.caidaocloud.user.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.em.DeleteStatusEnum;
import com.caidaocloud.user.service.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.user.service.domain.repository.ITenantBaseInfoRepository;
import com.caidaocloud.user.service.infrastructure.repository.mapper.TenantBaseInfoMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.TenantBaseInfo;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class TenantBaseInfoRepositoryImpl implements ITenantBaseInfoRepository {
    @Autowired
    private TenantBaseInfoMapper tenantBaseInfoMapper;

    @Override
    public void insertBatch(List<TenantBaseInfoDo> dataList) {
        tenantBaseInfoMapper.insertBatch(ObjectConverter.convertList(dataList, TenantBaseInfo.class));
    }

    @Override
    public void insert(TenantBaseInfoDo data) {
        tenantBaseInfoMapper.insert(ObjectConverter.convert(data, TenantBaseInfo.class));
    }

    @Override
    public void update(TenantBaseInfoDo data) {
        tenantBaseInfoMapper.updateById(ObjectConverter.convert(data, TenantBaseInfo.class));
    }

    @Override
    public int insertSelective(TenantBaseInfoDo record) {
        return tenantBaseInfoMapper.insertSelective(ObjectConverter.convert(record, TenantBaseInfo.class));
    }

    @Override
    public int updateByPrimaryKeySelective(TenantBaseInfoDo record) {
        return tenantBaseInfoMapper.updateByPrimaryKeySelective(ObjectConverter.convert(record, TenantBaseInfo.class));
    }

    @Override
    public void delete(List<Long> tenantIds) {
        tenantBaseInfoMapper.deleteBatchIds(tenantIds);
    }

    @Override
    public void softDelete(List<Long> tenantIds) {
        TenantBaseInfo tenantBaseInfo = new TenantBaseInfo();
        tenantBaseInfo.setDeleted(DeleteStatusEnum.DELETED.getIndex());
        tenantBaseInfo.setUpdateTime(System.currentTimeMillis());
        LambdaQueryWrapper<TenantBaseInfo> mQueryInfo = new QueryWrapper<TenantBaseInfo>().lambda();
        mQueryInfo.in(TenantBaseInfo::getTenantId, tenantIds);
        tenantBaseInfoMapper.update(tenantBaseInfo, mQueryInfo);
    }

    @Override
    public List<TenantBaseInfoDo> getTenantList(List<Long> tenantIds) {
        LambdaQueryWrapper<TenantBaseInfo> mQueryInfo = new QueryWrapper<TenantBaseInfo>().lambda();
        mQueryInfo.in(TenantBaseInfo::getTenantId, tenantIds).eq(TenantBaseInfo::getDeleted, 0);
        List<TenantBaseInfo> list = tenantBaseInfoMapper.selectList(mQueryInfo);
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : ObjectConverter.convertList(list, TenantBaseInfoDo.class);
    }

    @Override
    public TenantBaseInfoDo getTenantByCode(String tenantCode) {
        LambdaQueryWrapper<TenantBaseInfo> queryWrapper = new QueryWrapper<TenantBaseInfo>().lambda();
        queryWrapper.eq(TenantBaseInfo::getTenantCode, tenantCode).eq(TenantBaseInfo::getDeleted, 0);
        List<TenantBaseInfo> tenantBaseInfoList = tenantBaseInfoMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(tenantBaseInfoList) ? null : ObjectConverter.convertList(tenantBaseInfoList, TenantBaseInfoDo.class)
                .get(0);
    }

    @Override
    public List<TenantBaseInfoDo> getTenantListByCorpCode(String corpCode) {
        LambdaQueryWrapper<TenantBaseInfo> queryWrapper = new QueryWrapper<TenantBaseInfo>().lambda();
        queryWrapper.eq(TenantBaseInfo::getCorpCode, corpCode).eq(TenantBaseInfo::getDeleted, 0);
        List<TenantBaseInfo> tenantBaseInfoList = tenantBaseInfoMapper.selectList(queryWrapper);
        return CollectionUtils.isEmpty(tenantBaseInfoList) ? null : ObjectConverter.convertList(tenantBaseInfoList, TenantBaseInfoDo.class);
    }

    @Override
    public List<TenantBaseInfoDo> getAllTenantList() {
        LambdaQueryWrapper<TenantBaseInfo> mQueryInfo = new QueryWrapper<TenantBaseInfo>().lambda();
        mQueryInfo.eq(TenantBaseInfo::getDeleted, 0);
        List<TenantBaseInfo> list = tenantBaseInfoMapper.selectList(mQueryInfo);
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : ObjectConverter.convertList(list, TenantBaseInfoDo.class);
    }
}
