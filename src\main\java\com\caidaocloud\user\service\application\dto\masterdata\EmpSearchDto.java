package com.caidaocloud.user.service.application.dto.masterdata;

import java.util.List;

import com.caidaocloud.dto.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2023/5/18
 */
@Data
@ApiModel("员工搜索dto")
public class EmpSearchDto extends BasePage {
	@ApiModelProperty("时间轴")
	private Long datetime;
	@ApiModelProperty("关键字")
	private String keyword;
	@ApiModelProperty("员工id")
	private List<String> empIds;
	@ApiModelProperty("邮箱")
	private String companyEmail;

	@ApiModelProperty("工号")
	private String workNo;

}
