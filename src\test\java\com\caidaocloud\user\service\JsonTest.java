package com.caidaocloud.user.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Strings;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class JsonTest {

    public static void main(String[] args) {
        try {
            List<Long> list = Strings.split(",").call("").filter(e -> StringUtils.isNotBlank(e)).map(e -> Long.parseLong(e)).toList();
            System.out.println(list);
        } catch (Exception e) {
            e.printStackTrace();
        }


        UserImportErrorDto errorDto = new UserImportErrorDto();
        errorDto.setName("test1");
        errorDto.setWorkno("test1");
        errorDto.setRoles("test1");
        errorDto.setMobile("test1");
        errorDto.setEmail("test1");
        errorDto.setErrorMsg("test1");

        UserImportErrorDto errorDto2 = new UserImportErrorDto();
        errorDto2.setName("test2");
        errorDto2.setWorkno("test2");
        errorDto2.setRoles("test2");
        errorDto2.setMobile("test2");
        errorDto2.setEmail("test2");
        errorDto2.setErrorMsg("test2");

        ArrayList<UserImportErrorDto> list = Lists.newArrayList(errorDto, errorDto2);

        List<String> errors = FastjsonUtil.convertList(list, String.class);

        ArrayList<UserImportErrorDto> list1 = Lists.newArrayList();
        for (String error : errors) {
            UserImportErrorDto errorDto1 = FastjsonUtil.convertObject(error, UserImportErrorDto.class);
            list1.add(errorDto1);
        }

        String json = FastjsonUtil.toJson(list1);
        System.out.println(json);
        List<UserImportErrorDto> userImportErrorDtos1 = FastjsonUtil.toList(json, UserImportErrorDto.class);
        
        String str = "[\"{\"workno\":\"test1\",\"roles\":\"test1\",\"mobile\":\"test1\",\"name\":\"test1\",\"email\":\"test1\",\"errorMsg\":\"test1\"}\",\"{\"workno\":\"test2\",\"roles\":\"test2\",\"mobile\":\"test2\",\"name\":\"test2\",\"email\":\"test2\",\"errorMsg\":\"test2\"}\"]";
        str = str.replaceAll("\"\\{", "{").replaceAll("}\"", "}");
        System.out.println(str);
        List<UserImportErrorDto> userImportErrorDtos2 = FastjsonUtil.toList(str, UserImportErrorDto.class);

//        List<UserImportErrorDto> userImportErrorDtos = FastjsonUtil.convertList(errors, UserImportErrorDto.class);

        for (UserImportErrorDto userImportErrorDto : list1) {
            System.out.println(userImportErrorDto);
        }
    }

}
