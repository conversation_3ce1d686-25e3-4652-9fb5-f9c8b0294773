package com.caidaocloud.user.service.application.utils;

import java.security.SecureRandom;
import java.util.regex.Pattern;

import com.caidaocloud.util.StringUtil;
import lombok.Data;
import org.apache.shiro.crypto.RandomNumberGenerator;
import org.apache.shiro.crypto.SecureRandomNumberGenerator;
import org.apache.shiro.crypto.hash.SimpleHash;
import org.apache.shiro.util.ByteSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class PasswordHelper {
    /**
     * 加密算法名称
     */
    @Value("${system.auth.algorithmName:md5}")
    private String algorithmName;

    /**
     * 哈希迭代次数
     */
    @Value("${system.auth.hashIterations:2}")
    private int hashIterations;

    /**
     * 随机数生成器
     */
    private final RandomNumberGenerator randomNumberGenerator = new SecureRandomNumberGenerator();

    /**
     * 加密（编码）
     *
     * @param charSequence
     * @return
     */
    public String encode(CharSequence charSequence) {
        return new SimpleHash(
                algorithmName,
                charSequence,
                ByteSource.Util.bytes(randomNumberGenerator.nextBytes().toHex()),
                hashIterations).toHex();
    }

    /**
     * 自定义盐值加密（编码）
     *
     * @param CharSequence
     * @param salt
     * @return
     */
    public String encode(CharSequence CharSequence, String salt) {
        return new SimpleHash(
                algorithmName,
                CharSequence,
                ByteSource.Util.bytes(salt),
                hashIterations).toHex();
    }

    /**
     * 密码匹配校验
     *
     * @param charSequence
     * @param salt
     * @param encodePassword
     * @return
     */
    public boolean matches(CharSequence charSequence, String salt, String encodePassword) {
        String presentEncodePassWord = encode(charSequence, salt);
        return StringUtil.isNotEmpty(presentEncodePassWord) && presentEncodePassWord.equals(encodePassword);
    }

    /**
     * 根据Md5账号密码加密规则校验
     * @param charSequence
     * @param encodePassword
     * @return
     */
    public boolean matchesBySHA(CharSequence charSequence, String encodePassword) {
        return SHADigestUtil.encrypt(charSequence.toString()).equals(encodePassword);
    }

    /**
     * 创建salt
     *
     * @return
     */
    public String createSalt() {
        return randomNumberGenerator.nextBytes().toHex();
    }



    public static class PasswordGenerator {

        private static final String UPPERCASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        private static final String LOWERCASE = "abcdefghijklmnopqrstuvwxyz";
        private static final String DIGITS = "0123456789";
        private static final String SPECIAL_CHARACTERS = UPPERCASE + LOWERCASE + DIGITS;//"!@#$%^&*()-_=+<>?";
        private static final String ALL_CHARACTERS = UPPERCASE + LOWERCASE + DIGITS ;//+ SPECIAL_CHARACTERS;
        private static final SecureRandom RANDOM = new SecureRandom();
        private static final Pattern PASSWORD_PATTERN = Pattern.compile(
                "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+<>?])[A-Za-z0-9!@#$%^&*()\\-_=+<>?]{6,}$");
        private static final Pattern PASSWORD_PATTERN_V2 = Pattern.compile(
                "^[A-Za-z0-9!@#$%^&*()\\-_=+<>?]{6,}$");


        public static String generatePassword(int length) {
            if (length < 6) {
                throw new IllegalArgumentException("Password length must be at least 6 characters");
            }

            StringBuilder password = new StringBuilder(length);

            // Ensure at least one character from each category
            password.append(UPPERCASE.charAt(RANDOM.nextInt(UPPERCASE.length())));
            password.append(LOWERCASE.charAt(RANDOM.nextInt(LOWERCASE.length())));
            password.append(DIGITS.charAt(RANDOM.nextInt(DIGITS.length())));
            password.append(SPECIAL_CHARACTERS.charAt(RANDOM.nextInt(SPECIAL_CHARACTERS.length())));

            // Fill the rest with random characters from all categories
            for (int i = 4; i < length; i++) {
                password.append(ALL_CHARACTERS.charAt(RANDOM.nextInt(ALL_CHARACTERS.length())));
            }

            // Shuffle the password to ensure randomness
            char[] passwordArray = password.toString().toCharArray();
            for (int i = passwordArray.length - 1; i > 0; i--) {
                int index = RANDOM.nextInt(i + 1);
                char temp = passwordArray[index];
                passwordArray[index] = passwordArray[i];
                passwordArray[i] = temp;
            }

            return new String(passwordArray);
        }
        public static boolean validatePassword(String password) {
            if (password == null) {
                return false;
            }
            return PASSWORD_PATTERN.matcher(password).matches();
        }


        public static boolean validateSimplePassword(String password) {
            if (password == null) {
                return false;
            }
            return PASSWORD_PATTERN_V2.matcher(password).matches();
        }

        public static void main(String[] args) {
            String pwd = generatePassword(6);
            System.out.println("pwd:" + pwd + " ..... " + validateSimplePassword(pwd));
        }
    }
}
