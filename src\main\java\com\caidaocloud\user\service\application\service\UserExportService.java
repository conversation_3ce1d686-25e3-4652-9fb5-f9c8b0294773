package com.caidaocloud.user.service.application.service;

import java.io.IOException;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.handler.impl.ExcelDataHandlerDefaultImpl;
import cn.afterturn.easypoi.handler.inter.IExcelDataHandler;
import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.dto.MetadataPropertyDto;
import com.caidaocloud.hrpaas.metadata.sdk.dto.PropertyValue;
import com.caidaocloud.hrpaas.metadata.sdk.enums.PropertyDataType;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.infrastructure.util.ExcelUtil;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoQueryDto;
import com.caidaocloud.user.service.interfaces.vo.UserDetailInfoVo;
import com.caidaocloud.util.DateUtil;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> Zhou
 * @date 2024/11/6
 */
@Service
@Slf4j
public class UserExportService {
	@Resource
	private UserBaseInfoService userBaseInfoService;

	public void export(HttpServletResponse response, UserBaseInfoQueryDto queryDto) {
		queryDto.setPageSize(5000);
		PageResult<UserDetailInfoVo> data = userBaseInfoService.page(queryDto);
		ExportParams userExportParam = new ExportParams();
		userExportParam.setDataHandler(new ExcelDataHandlerDefaultImpl() {
			@Override
			public String[] getNeedHandlerFields() {
				return new String[] {"用户状态"};
			}

			@Override
			public Object exportHandler(Object obj, String name, Object value) {
				if (value == null) {
					return null;
				}
				return AccountStatusEnum.getName((Integer) value);
			}
		});
		Workbook workbook = ExcelExportUtil.exportBigExcel(userExportParam, UserDetailInfoVo.class, new IExcelExportServer() {
			@Override
			public List<Object> selectListForExcelExport(Object queryParams, int page) {
				UserBaseInfoQueryDto dto = (UserBaseInfoQueryDto) queryParams;
				dto.setPageNo(page);
				PageResult<UserDetailInfoVo> result = userBaseInfoService.page(dto);
				return Sequences.sequence(result.getItems()).map(obj -> (Object) obj).toList();
			}
		}, queryDto);
		try {
			ExcelUtil.downLoadSXSSFExcel("员工账号列表", response, workbook);
		}
		catch (IOException e) {
			log.error("导出员工账号失败", e);
			throw new ServerException("导出员工账号失败", e);
		}
	}
}
