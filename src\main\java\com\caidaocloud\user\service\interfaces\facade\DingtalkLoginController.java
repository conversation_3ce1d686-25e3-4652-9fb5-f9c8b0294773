package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.user.service.application.dto.DingResponse;
import com.caidaocloud.user.service.application.dto.DingUserIdDto;
import com.caidaocloud.user.service.application.dto.DingUserInfoDto;
import com.caidaocloud.user.service.application.dto.DingUserInfoResponse;
import com.caidaocloud.user.service.application.feign.DingTalkFeignClient;
import com.caidaocloud.user.service.application.service.login.IAccountLoginService;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.user.service.interfaces.vo.AccountLoginVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description 钉钉免登陆接口
 * @Date 2023/2/1 上午9:30
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/api/user/dingtalk")
public class DingtalkLoginController {


    @Value("${sso.dingtalk.cropId:}")
    private String CROP_ID;

    @Value("${sso.dingtalk.appKey:}")
    private String APP_KEY ;

    @Value("${sso.dingtalk.appSecret:}")
    private String APP_SECRET ;


    @Autowired
    private DingTalkFeignClient dingTalkFeignClient;

    @ApiOperation(value = "获取corpId信息")
    @GetMapping(value = "/v1/corpInfo")
    public Result getCorpId() {

        return Result.ok(CROP_ID);
    }

    @ApiOperation(value = "获取钉钉token信息")
    @GetMapping(value = "/v1/tokenInfo")
    public Result getAccessToken() {
        Map<String, String> ssoToken = dingTalkFeignClient.getSsoToken(APP_KEY, APP_SECRET);
        String accessToken = ssoToken.get("access_token");
        return Result.ok(accessToken);
    }



    @ApiOperation(value = "获取才到免登的accessToken")
    @GetMapping(value = "/v1/accessTokenInfo")
    public Result getCaidaoAccessToken(@RequestParam("code") String code, @RequestParam("loginPlatform") Integer loginPlatform) {

        String prefix = "getCaidaoAccessToken";
        log.info("{}| code=[{}], loginPlatform=[{}]",prefix, code,loginPlatform);

        Map<String, String> ssoToken = dingTalkFeignClient.getSsoToken(APP_KEY, APP_SECRET);
        log.info("{}| getSsoToken --ssoToken:{}",prefix, FastjsonUtil.toJson(ssoToken));

        String accessToken = ssoToken.get("access_token");
        log.info("{}| getSsoToken --access_token:{}",prefix, accessToken);


        DingResponse response = dingTalkFeignClient.getUserId(code, accessToken);
        log.info("{}| getUserId --response:{}",prefix, FastjsonUtil.toJson(response));

        if ("ok".equals(response.getErrmsg())) {
            DingUserIdDto dingUserIdDto = response.getResult();
            log.info("{}| dingUserIdDto:{}",prefix, FastjsonUtil.toJson(dingUserIdDto));
            String userId = dingUserIdDto.getUserid();

            DingUserInfoResponse userInfoResponse = dingTalkFeignClient.getUserInfo(userId, accessToken);
            log.info("{}| getUserInfo --userInfoResponse:{}",prefix, FastjsonUtil.toJson(userInfoResponse));

            if ("ok".equals(userInfoResponse.getErrmsg())) {
                DingUserInfoDto userInfoDto = userInfoResponse.getResult();
                String workNo = userInfoDto.getWorkNo();
                log.info("{}| getUserInfo --userInfoResponse.getResult:{}",prefix, FastjsonUtil.toJson(userInfoDto));

                AccountLoginDto loginDto = new AccountLoginDto();
                loginDto.setAccount(workNo);
                loginDto.setLoginPlatform(3);
                IAccountLoginService samlService = IAccountLoginService.getInstance(GrantType.DING_TALK_GRANT_TYPE);
                log.info("{}| getUserInfo --IAccountLoginService.getInstance:{}",prefix, FastjsonUtil.toJson(samlService));

                AccountLoginVo result = samlService.grant(loginDto);
                log.info("{}| samlService.grant --samlService.grant:{}",prefix, FastjsonUtil.toJson(result));
                return Result.ok(result);
            }
        }

        return Result.fail();

    }



}

 
    
    
    
    