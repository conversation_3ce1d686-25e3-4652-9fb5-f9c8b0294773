package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.foreign.service.IForeignLoginService;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.web.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 一键登录
 */
@Component
public class OneClickLoginService implements IAccountLoginService {
    @Autowired
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Autowired
    private IForeignLoginService foreignLoginService;

    @Override
    public GrantType getGrantType() {
        return GrantType.ONE_CLICK_LOGIN_TYPE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        // 调用阿里云sdk完成一键登录取号
        Result<String> result = foreignLoginService.getMobile(loginDto.getLoginAccessToken());
        PreCheck.preCheckArgument(!result.isSuccess() || StringUtils.isEmpty(result.getData()), LangUtil.getMsg(MsgCodeConstant.ONE_CLICK_LOGIN_FAIL));
        String mobile = result.getData();
        // 账号查询并检查
        List<AccountBaseInfoDo> accountList = accountBaseInfoDomainService.getAndCheckAccountList(mobile, GrantType.ONE_CLICK_LOGIN_TYPE);
        // 用户查询并检查
        List<Long> accountIds = accountList.stream().map(AccountBaseInfoDo::getAccountId).collect(Collectors.toList());
        List<UserBaseInfoDo> userList = userBaseInfoDomainService.getAndCheckUserList(accountIds);
        // 只匹配到一个用户
        boolean singleUser = userList.size() == 1;
        if (singleUser) {
            // 账号已停用或已锁定判断
            long normalCount = userList.stream().filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).count();
            // 账号已停用或已锁定，请联系管理员
            PreCheck.preCheckArgument(normalCount == 0, LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));
        }
        return userList;
    }
}
