package com.caidaocloud.user.service.infrastructure.repository.mongo;

import com.caidaocloud.user.service.infrastructure.config.mongo.MorphiaContext;
import com.mongodb.DBObject;
import org.mongodb.morphia.Datastore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 分页查询组件
 */
@Component
public class MorphiaPaging<T> {

    @Autowired
    private MongodbDao<T> mongodbDao;

    @Autowired
    private MorphiaContext morphiaContext;

    private int pageSize = 5000;
    private String collectionName;
    private DBObject query;
    private DBObject projection;
    private Class<T> clazz;
    private int pageNum = 0;

    public MongodbDao<T> getMongodbDao() {
        return mongodbDao;
    }

    public void setMongodbDao(MongodbDao<T> mongodbDao) {
        this.mongodbDao = mongodbDao;
    }

    public long getTotalPageSize() {
        Datastore datastore = morphiaContext.getDatastore();
        long totalCount = datastore.getDB().getCollection(collectionName).count(query);
        if (pageSize <= 0) {
            pageSize = 1000;
        }
        if (totalCount % pageSize == 0) {
            return totalCount / pageSize;
        } else {
            return (totalCount - totalCount % pageSize) / pageSize + 1;
        }
    }

    public long getTotalCount() {
        Datastore datastore = morphiaContext.getDatastore();
        long totalCount= datastore.getDB().getCollection(collectionName).count(query);
        return totalCount;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public String getCollectionName() {
        return collectionName;
    }

    public void setCollectionName(String collectionName) {
        this.collectionName = collectionName;
    }

    public DBObject getQuery() {
        return query;
    }

    public void setQuery(DBObject query) {
        this.query = query;
    }

    public DBObject getProjection() {
        return projection;
    }

    public void setProjection(DBObject projection) {
        this.projection = projection;
    }

    public Class<T> getClazz() {
        return clazz;
    }

    public void setClazz(Class<T> clazz) {
        this.clazz = clazz;
    }

    public List<T> getNext() {
        pageNum++;
        return mongodbDao.queryWithPaging(query, projection, collectionName, clazz, pageNum, pageSize);
    }

    public List<T> getCurrentPage() {
        return mongodbDao.queryWithPaging(query, projection, collectionName, clazz, pageNum, pageSize);
    }
}
