package com.caidaocloud.user.service.feign;

import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.application.feign.IHrFeign;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * created by: FoAng
 * create time: 9/12/2022 1:29 下午
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class HrFeignTest {

    @Resource
    private IHrFeign hrFeign;

    @Before
    public void init() {
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        securityUserInfo.setUserId(0L);
        securityUserInfo.setTenantId("33");
        SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
    }

    @Test
    public void getWorkInfoByExt() {
        Result<?> result = hrFeign.getEmpIdByUserName("1111");
        System.out.println("result:" + FastjsonUtil.toJson(result));
    }

    private String generateAccessToken() {
        String token = TokenGenerator.getToken("0", "33", 1);
        SecurityUserInfo securityUser = new SecurityUserInfo();
        securityUser.setEmpId(0L);
        securityUser.setTenantId("33");
        securityUser.setIsAdmin(true);
        securityUser.setUserId(0L);
        SecurityUserUtil.setSecurityUserInfo(securityUser);
        return token;
    }
}
