package com.caidaocloud.user.service.application.config;

import feign.Logger;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * created by: FoAng
 * create time: 9/12/2022 1:59 下午
 */
@Configuration
@ConditionalOnProperty(
        name = "feign.log.debug",
        havingValue = "true"
)
public class FeignConfig {

    @Bean
    Logger.Level feignLevel() {
        return Logger.Level.FULL;
    }
}
