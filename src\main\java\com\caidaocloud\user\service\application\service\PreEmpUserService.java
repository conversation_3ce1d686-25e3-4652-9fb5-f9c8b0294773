package com.caidaocloud.user.service.application.service;

import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.dto.preemp.PreEmpUserDto;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.application.utils.PropertyUtil;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoDto;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class PreEmpUserService {

    @Autowired
    private PasswordHelper passwordHelper;

    @Autowired
    private AccountBaseInfoService accountBaseInfoService;

    @Autowired
    private UserBaseInfoService userBaseInfoService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Transactional
    public void createPreEmpUser(PreEmpUserDto preEmpUser) {
        val phoneBindAccount = accountBaseInfoService.getAccountInfo(preEmpUser.getMobileNo());
        UserBaseInfoDo phoneBindUser = null;
        if (phoneBindAccount != null) {
            phoneBindUser = userBaseInfoService
                    .getUserByAccountId(phoneBindAccount.getAccountId()).stream().filter(it ->
                            String.valueOf(it.getTenantId())
                                    .equals(SecurityUserUtil.getSecurityUserInfo().getTenantId()))
                    .findFirst().orElse(null);
        }
        if (preEmpUser.isReentry()) {
            val lastUser = userBaseInfoService.getByEmpId(preEmpUser.getLastEmpId());
            AccountBaseInfoDo lastAccount = null;
            if (lastUser != null) {
                lastUser.setReEmpId(preEmpUser.getLastEmpId());
                lastAccount = accountBaseInfoService.getAccountInfo(lastUser.getAccount());
            }
            if (lastUser == null) {
                if (phoneBindAccount == null) {
                    createUser(preEmpUser, createAccount(preEmpUser));
                } else {
                    val userExist = userBaseInfoService.getUserListByAccount(phoneBindAccount.getAccount()).stream().filter(it ->
                            String.valueOf(it.getTenantId())
                                    .equals(SecurityUserUtil.getSecurityUserInfo().getTenantId())).findFirst();
                    if (userExist.isPresent()) {
                        throw new ServerException("用户账号冲突");
                    } else {
                        createUser(preEmpUser, phoneBindAccount);
                    }
                }
            } else {
                val mobNum = lastAccount.getMobNum();
                if (StringUtils.equals(mobNum, preEmpUser.getMobileNo())) {
                    if (lastUser.getOnboarding()) {
                        lastUser.setUserName(preEmpUser.getUserName());
                        lastUser.setStatus(1);
                        lastUser.setOnboarding(true);
                        lastUser.setEmpId(preEmpUser.getEmpId());
                        userBaseInfoDomainService.saveOrUpdateUser(lastUser);
                        //throw new ServerException("用户账号冲突");
                    } else {
                        //员工转候选人
                        lastUser.setUserName(preEmpUser.getUserName());
                        lastUser.setStatus(1);
                        lastUser.setOnboarding(true);
                        lastUser.setEmpId(preEmpUser.getEmpId());
                        userBaseInfoDomainService.saveOrUpdateUser(lastUser);
                    }
                } else {
                    lastUser.setEmpId(null);
                    userBaseInfoDomainService.saveOrUpdateUser(lastUser);
                    if (phoneBindAccount == null) {
                        createUser(preEmpUser, createAccount(preEmpUser));
                    } else {
                        if (phoneBindUser == null) {
                            createUser(preEmpUser, phoneBindAccount);
                        } else {
                            throw new ServerException("用户账号冲突");
                        }
                    }
                    //throw new ServerException("用户账号冲突");
                }
            }
        } else {
            if (phoneBindAccount == null) {
                createUser(preEmpUser, createAccount(preEmpUser));
            } else {
                if (phoneBindUser == null) {
                    createUser(preEmpUser, phoneBindAccount);
                } else {
                    throw new ServerException("用户账号冲突");
                }
            }
        }
    }

    public void updatePreEmpUser(PreEmpUserDto preEmpUser) {
        UserBaseInfoDo byEmpId = userBaseInfoService.getByEmpId(preEmpUser.getEmpId());
        byEmpId.setUserName(preEmpUser.getUserName());
        userBaseInfoDomainService.saveOrUpdateUser(byEmpId);

        AccountBaseInfoDo accountInfo = new AccountBaseInfoDo();
        accountInfo.setAccountId(byEmpId.getAccountId());
        accountInfo.setEmail(preEmpUser.getEmail());
        accountInfo.setMobNum(preEmpUser.getMobileNo());
        accountBaseInfoService.updateByPrimaryKey(accountInfo);
    }

    public AccountBaseInfoDo createAccount(PreEmpUserDto preEmpUser) {
        String defaultImportPassword = PropertyUtil.getProperty("caidaocloud.import.password:Cd1524");
        log.info("defaultPassword: " + defaultImportPassword);
        String salt = passwordHelper.createSalt();
        String password = passwordHelper.encode(defaultImportPassword, salt);
        AccountBaseInfoDo account = new AccountBaseInfoDo();
        account.setAccount(preEmpUser.getMobileNo());
        account.setMobNum(preEmpUser.getMobileNo());
        account.setEmail(preEmpUser.getEmail());
        account.setPassword(password);
        account.setSalt(salt);
        account.setStatus(1);
        account.setCreateBy(0L);
        account.setCreateTime(System.currentTimeMillis());
        accountBaseInfoService.saveOrUpdateAccount(account);
        return account;
    }

    public UserBaseInfoDto createUser(PreEmpUserDto preEmpUser, AccountBaseInfoDo account) {
        UserBaseInfoDto userBaseInfoDto = new UserBaseInfoDto();
        userBaseInfoDto.setAccountId(account.getAccountId());
        userBaseInfoDto.setAccount(account.getAccount());
        userBaseInfoDto.setEmpId(preEmpUser.getEmpId());
        userBaseInfoDto.setUserName(preEmpUser.getUserName());
        userBaseInfoDto.setTenantId(Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()));
        userBaseInfoDto.setStatus(1);
        userBaseInfoDto.setCorpId(userBaseInfoDto.getTenantId());
        userBaseInfoDto.setOnboarding(true);
        Long userId = userBaseInfoService.saveOrUpdateUserBaseInfo(userBaseInfoDto);
        userBaseInfoDto.setUserId(userId);
        return userBaseInfoDto;
    }

    /**
     * 1860  创建员工账号；
     *
     * @param preEmpUser
     * @param account
     * @return
     */
    public UserBaseInfoDto createEmpUser(PreEmpUserDto preEmpUser, AccountBaseInfoDo account) {
        UserBaseInfoDto userBaseInfoDto = new UserBaseInfoDto();
        userBaseInfoDto.setAccountId(account.getAccountId());
        userBaseInfoDto.setAccount(account.getAccount());
        userBaseInfoDto.setEmpId(preEmpUser.getEmpId());
        userBaseInfoDto.setUserName(preEmpUser.getUserName());
        userBaseInfoDto.setTenantId(Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()));
        userBaseInfoDto.setStatus(1);
        userBaseInfoDto.setCorpId(userBaseInfoDto.getTenantId());
        userBaseInfoDto.setOnboarding(false);
        if(null != SecurityUserUtil.getSecurityUserInfo().getUserId()){
            userBaseInfoDto.setCreateBy(SecurityUserUtil.getSecurityUserInfo().getUserId());
        }
        Long userId = userBaseInfoService.saveOrUpdateUserBaseInfo(userBaseInfoDto);
        userBaseInfoDto.setUserId(userId);
        return userBaseInfoDto;
    }

    /**
     * 将候选人用户转换为员工用户
     *
     * @param empId
     * @param toId
     */
    public void convertToEmpUser(Long empId, Long toId) {
        UserBaseInfoDo userInfo = userBaseInfoService.getByEmpId(empId);
        // 用户不存在
        PreCheck.preCheckNotNull(userInfo, ErrorMessage.fromCode("caidao.exception.error_60016"));
        // 不是候选人用户
        if (userInfo.getOnboarding() == null || !userInfo.getOnboarding()) {
            log.warn("Onboarding user not found,empId = {}", empId);
            return;
        }
        userInfo.setStatus(1);
        userInfo.setEmpId(toId);
        userInfo.setOnboarding(false);
        userBaseInfoDomainService.saveOrUpdateUser(userInfo);
    }
}
