<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.user.service.infrastructure.repository.mapper.HisUserPwdRecMapper">
    <insert id="insertSelective"
            parameterType="com.caidaocloud.user.service.infrastructure.repository.po.HisUserPwdRec">
        insert into his_user_pwd_rec
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recId != null">
                rec_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="salt != null">
                salt,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recId != null">
                #{recId,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                #{salt,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.caidaocloud.user.service.infrastructure.repository.po.HisUserPwdRec">
        update his_user_pwd_rec
        <set>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                salt = #{salt,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
        </set>
        where rec_id = #{recId,jdbcType=BIGINT}
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into his_user_pwd_rec (rec_id, account_id, user_id,
        tenant_id, password, salt,
        create_by, create_time, update_by,
        update_time, deleted)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.recId,jdbcType=BIGINT}, #{record.accountId,jdbcType=BIGINT}, #{record.userId,jdbcType=BIGINT},
            #{record.tenantId,jdbcType=BIGINT}, #{record.password,jdbcType=VARCHAR}, #{record.salt,jdbcType=VARCHAR},
            #{record.createBy,jdbcType=BIGINT}, #{record.createTime,jdbcType=BIGINT},
            #{record.updateBy,jdbcType=BIGINT},
            #{record.updateTime,jdbcType=BIGINT}, #{record.deleted,jdbcType=INTEGER})
        </foreach>
    </insert>
</mapper>