package com.caidaocloud.user.service.feign;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.util.SpringUtil;
import feign.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@ActiveProfiles("local")
@SpringBootTest(classes = Application.class)
public class FeginTest {
/*
    @Bean
    public Contract feignContract() {
        return new feign.Contract.Default();
    }*/


    @Test
    public void testSys(){
        System.out.println(":sdfsd");
    }

    @Test
    public void feignTest() {
        LoginDto loginDto = new LoginDto();
        loginDto.setThirdPart("TencentHR");
        String thirdEmpId = loginDto.getThirdId() + ":" + loginDto.getThirdPart();
        System.out.println(thirdEmpId);

        FeignClientUtils.setAppContext(SpringUtil.getContext());
        IAutoFeginTest feignClient = FeignClientUtils.build("adapter-tencent-service", IAutoFeginTest.class);

        Map headMap = new HashMap();
        headMap.put("aaaa", "bbbb");

        Map map = new HashMap();
        map.put("test", "aaaa");
        Object obj = feignClient.getUrl("/api/config/test", headMap);
        System.out.println(obj);

        obj = feignClient.postJson("/api/config/post", headMap, map);
        System.out.println(obj);

    }




    public interface IAutoFeginTest{
        @PostMapping("{postUrl}")
        String postJson(@PathVariable("postUrl") String postUrl, @RequestHeader Map<String,Object> headMap, @RequestBody Map map);

        @GetMapping("{getUrl}")
        String getUrl(@PathVariable("getUrl") String getUrl, @HeaderMap Map<String,Object> headMap);
    }
}
