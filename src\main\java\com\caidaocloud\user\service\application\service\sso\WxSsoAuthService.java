package com.caidaocloud.user.service.application.service.sso;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.user.service.domain.entity.UserSsoInfo;
import com.caidaocloud.user.service.interfaces.vo.LoginVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * 迪士尼的SSO认证实现
 */
@Service("ssoAuthService:2")
public class WxSsoAuthService implements SsoAuthService{
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private CacheService cacheService;

    @Override
    public LoginVo authorize(UserSsoInfo userSsoInfo) {
        return null;
    }

    public String getAccessToken(UserSsoInfo userSsoInfo){
        String key = String.format("%s_%s_ACCESS_TOKEN", userSsoInfo.getTenantId(), userSsoInfo.getAppid());
        String value = cacheService.getValue(key);
        if(StringUtils.isNotBlank(value)) {
            return value;
        }

        return null;
    }

    private String getTicket(){
        return null;
    }
}
