package com.caidaocloud.user.service.application.service;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.dto.LoginConfigDto;
import com.caidaocloud.user.service.domain.entity.User;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.util.OkHttpUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service("iLoginService:http")
public class UserHttpLoginService implements ILoginService{

    @Override
    public LoginOutDto doLogin(LoginConfigDto loginConfigDto, LoginDto loginDto, User dbUser){
        LoginOutDto loginOutDo = new LoginOutDto();
        loginOutDo.setErrorCode(ErrorCodes.UNKNOW_ERROR);

        doConfigReplace(loginConfigDto, loginDto, dbUser);
        OkHttpUtil.Response<Map> response = OkHttpUtil.postJson(loginConfigDto.getLoginUrl(), Map.class, loginConfigDto.getParams(), loginConfigDto.getHeaders(), null);
        String data = JSON.toJSONString(response.getData());
        log.info("doLogin response data = {}", data);
        if(null == response || !response.isSuccess()){
            return loginOutDo;
        }

        if(StringUtil.isNotEmpty(data) && data.indexOf(loginConfigDto.getSuccessMark()) < 0){
            if(StringUtil.isEmpty(loginConfigDto.getErrorMark())){
                loginOutDo.setErrorCode(ErrorCodes.INVALIDED_USER_OR_PASSWORD);
                return loginOutDo;
            }

            String errMark = doErrorMark(response.getData(), loginConfigDto.getErrorMark());
            if(StringUtil.isEmpty(errMark)){
                return loginOutDo;
            }

            throw new ServerException(errMark);
        }

        loginOutDo.setErrorCode(ErrorCodes.NO_ERROR);
        return loginOutDo;
    }

    @Override
    public LoginOutDto login(User user, LoginDto loginDto) {
        return null;
    }
}
