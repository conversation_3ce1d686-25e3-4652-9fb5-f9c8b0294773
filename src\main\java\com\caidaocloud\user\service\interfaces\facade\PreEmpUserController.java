package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.user.service.application.dto.preemp.PreEmpUserDto;
import com.caidaocloud.user.service.application.service.PreEmpUserService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "导入候选人账号信息")
@Slf4j
@RestController
@RequestMapping("/api/user/v1/pre/emp")
public class PreEmpUserController {

    @Autowired
    private PreEmpUserService preEmpUserService;


    @ApiOperation(value = "新增候选人用户")
    @PostMapping("/create")
    public Result<String> createPreEmpUser(@RequestBody PreEmpUserDto preEmpUser) {
        try {
            preEmpUserService.createPreEmpUser(preEmpUser);
        } catch (Exception e) {
            log.error("user create error : {},{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok("success");
    }

    @ApiOperation(value = "更新候选人用户")
    @PostMapping("/update")
    public Result<String> updatePreEmpUser(@RequestBody PreEmpUserDto preEmpUser) {
        try {
            preEmpUserService.updatePreEmpUser(preEmpUser);
        } catch (Exception e) {
            log.error("update preUser error:{},{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok("success");
    }

    @ApiOperation(value = "候选人用户转为员工用户")
    @PutMapping("toEmp")
    public Result<Boolean> toEmpI(@RequestBody PreEmpUserDto preEmpUser) {
        try {
            preEmpUserService.convertToEmpUser(preEmpUser.getEmpId(), preEmpUser.getToId());
        } catch (Exception e) {
            log.error("occur error when execute toEmpI method", e);
            return Result.ok(false);
        }
        return Result.ok(true);
    }
}
