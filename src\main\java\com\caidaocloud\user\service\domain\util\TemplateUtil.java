package com.caidaocloud.user.service.domain.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.googlecode.totallylazy.Lists;
import org.apache.poi.ss.usermodel.*;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户模板下载
 */
public class TemplateUtil {
    
    public static final int COLUMN_WIDTH = 15;

    public static Workbook createTemplate(List<String> title, List<String> requiredTitle, String description) {
        List<ExcelExportEntity> list = new ArrayList<>();
        title.forEach(o-> list.add(new ExcelExportEntity(o)));

        ExportParams params = new ExportParams();
        params.setTitle(description);
        Workbook workbook = ExcelExportUtil.exportExcel(params, list, Lists.list());
        Cell descriptionCell = workbook.getSheetAt(0).getRow(0).getCell(0);
        descriptionCell.setCellStyle(getDescriptionStyle(workbook));
        for (Cell titleCell : workbook.getSheetAt(0).getRow(1)) {
            String t = titleCell.getStringCellValue();
            titleCell.setCellStyle(getTitleCellStyle(workbook,t, requiredTitle));
            titleCell.setCellValue(requiredTitle.contains(t) ? "*"+t : t);
        }
        return workbook;
    }

    public static CellStyle getDescriptionStyle(Workbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)10);
        cellStyle.setFont(font);
        return cellStyle;
    }

    public static CellStyle getTitleCellStyle(Workbook workbook, String title, List<String> requiredTitle) {
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);
        cellStyle.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short)10);
        font.setBold(true);
        font.setColor(requiredTitle.contains(title) ? IndexedColors.GOLD.getIndex():IndexedColors.WHITE.getIndex());
        cellStyle.setFont(font);
        return cellStyle;
    }

    public static void main(String[] args) {
        List<String> tot = Lists.list("姓名", "工号", "所属角色", "手机号", "邮箱", "密码");
        List<String> required = Lists.list("姓名", "手机号");
        Workbook template = createTemplate(tot, required, "填表须知： 表头橙色项为必填项，白色为非必填项\n" +
                "所属角色：需与已有角色名称保持一致，多个角色需用英文逗号隔开；新增与账号关联的角色，即账号已关联角色A，导入角色B，导入后账号关联A和B");

        try {
            FileOutputStream fos = new FileOutputStream("d:/0816.xlsx");
            template.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
