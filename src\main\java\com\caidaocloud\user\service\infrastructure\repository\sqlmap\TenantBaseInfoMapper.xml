<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.user.service.infrastructure.repository.mapper.TenantBaseInfoMapper">
    <insert id="insertSelective"
            parameterType="com.caidaocloud.user.service.infrastructure.repository.po.TenantBaseInfo">
        insert into tenant_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="tenantName != null">
                tenant_name,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="logo != null">
                logo,
            </if>
            <if test="corpId != null">
                corp_id,
            </if>
            <if test="corpCode != null">
                corp_code,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="tenantName != null">
                #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="logo != null">
                #{logo,jdbcType=VARCHAR},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=BIGINT},
            </if>
            <if test="corpCode != null">
                #{corpCode,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.caidaocloud.user.service.infrastructure.repository.po.TenantBaseInfo">
        update tenant_base_info
        <set>
            <if test="tenantName != null">
                tenant_name = #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="logo != null">
                logo = #{logo,jdbcType=VARCHAR},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="corpCode != null">
                corp_code = #{corpCode,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
        </set>
        where tenant_id = #{tenantId,jdbcType=BIGINT}
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into tenant_base_info (tenant_id, tenant_name, tenant_code,
        logo, corp_id, corp_code,
        create_by, create_time, update_by,
        update_time, deleted,status)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.tenantId,jdbcType=BIGINT}, #{record.tenantName,jdbcType=VARCHAR},
            #{record.tenantCode,jdbcType=VARCHAR},
            #{record.logo,jdbcType=VARCHAR}, #{record.corpId,jdbcType=BIGINT}, #{record.corpCode,jdbcType=VARCHAR},
            #{record.createBy,jdbcType=BIGINT}, #{record.createTime,jdbcType=BIGINT},
            #{record.updateBy,jdbcType=BIGINT},
            #{record.updateTime,jdbcType=BIGINT}, #{record.deleted,jdbcType=INTEGER},
            #{record.status,jdbcType=INTEGER})
        </foreach>
    </insert>

</mapper>