
package com.caidaocloud.user.service.domain.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.repository.IUserBaseInfoRepository;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoQueryDto;
import com.caidaocloud.util.SnowflakeUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserBaseInfoDomainService {
    @Autowired
    private UserBaseInfoDo userBaseInfoDo;
    @Autowired
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Resource
    private IUserBaseInfoRepository userBaseInfoRepository;

    public List<UserBaseInfoDo> getAndCheckUserList(List<Long> accountIds) {
        List<UserBaseInfoDo> userList = getUserListByAccountIds(accountIds);
        PreCheck.preCheckArgument(CollectionUtils.isEmpty(userList), LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));
        return userList;
    }

    public List<UserBaseInfoDo> getAndCheckNormalUserList(List<Long> accountIds) {
        List<UserBaseInfoDo> userList = getUserListByAccountIds(accountIds);
        PreCheck.preCheckArgument(CollectionUtils.isEmpty(userList), LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

        // 过滤掉停用、锁定的用户
        userList = userList.stream().filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus()))
                .collect(Collectors.toList());
        PreCheck.preCheckArgument(CollectionUtils.isEmpty(userList),
                LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));
        return userList;
    }

    public void syncSave(List<UserBaseInfoDo> dataList) throws Exception {
        userBaseInfoDo.syncSave(dataList);
    }

    public int deleteByIds(List<Long> userIds) {
        return userBaseInfoDo.deleteByIds(userIds);
    }

    public void softDeleteByIds(List<Long> userIds) {
        userBaseInfoDo.softDeleteByIds(userIds);
    }

    public void updateDingUserIdByEmpId(Long empId, String dingUserId) {

        userBaseInfoDo.updateDingUserIdByEmpId(empId, dingUserId);
    }

    public List<UserBaseInfoDo> getUserListByAccount(String account) {
        // 根据账号数据查询账号信息
        List<AccountBaseInfoDo> accountList = accountBaseInfoDomainService.getAccountInfoList(account);
        if (CollectionUtils.isEmpty(accountList)) {
            return Lists.newArrayList();
        }
        List<Long> accountIds = accountList.stream().map(AccountBaseInfoDo::getAccountId).collect(Collectors.toList());
        return getUserListByAccountIds(accountIds);
    }

    public List<UserBaseInfoDo> getNormalUserListByAccount(String account) {
        // 根据账号数据查询账号信息
        List<AccountBaseInfoDo> accountList = accountBaseInfoDomainService.getAccountInfoList(account);
        if (CollectionUtils.isEmpty(accountList)) {
            return Lists.newArrayList();
        }

        // 过滤掉停用、锁定的账号
        List<AccountBaseInfoDo> normalAccountList = accountList.stream()
                .filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalAccountList)) {
            return Lists.newArrayList();
        }

        List<Long> accountIds = normalAccountList.stream().map(AccountBaseInfoDo::getAccountId)
                .collect(Collectors.toList());
        List<UserBaseInfoDo> userList = getUserListByAccountIds(accountIds);
        if (CollectionUtils.isEmpty(normalAccountList)) {
            return Lists.newArrayList();
        }

        // 过滤掉停用、锁定的用户
        return userList.stream().filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus()))
                .collect(Collectors.toList());
    }

    public List<UserBaseInfoDo> getUserListByAccountId(Long accountId) {
        return userBaseInfoDo.selectListByAccountId(accountId);
    }

    public List<UserBaseInfoDo> getUserListByAccountIds(List<Long> accountIds) {
        return userBaseInfoDo.selectListByAccountIds(accountIds, false);
    }

    public List<UserBaseInfoDo> selectListByWorkNo(String tenantId, String workNo) {
        return userBaseInfoDo.selectListByWorkNo(tenantId, workNo);
    }

    public List<UserBaseInfoDo> getTenantUserListByAccountIds(List<Long> accountIds) {
        return userBaseInfoDo.selectListByAccountIds(accountIds, true);
    }

    public Optional<UserBaseInfoDo> getByUserId(Long userId) {
        UserBaseInfoDo userBaseInfo = userBaseInfoDo.getByUserId(userId);
        return Optional.ofNullable(userBaseInfo);
    }

    public List<UserBaseInfoDo> getByUserIds(List<Long> userIds) {
        return userBaseInfoDo.getByUserIds(userIds);
    }

    public IPage<UserBaseInfoDo> page(String keywords, int pageNo, int pageSize, Boolean onBoarDing) {
        return userBaseInfoDo.page(keywords, pageNo, pageSize, onBoarDing);
    }

    public IPage<UserBaseInfoDo> page(UserBaseInfoQueryDto dto) {
        return userBaseInfoDo.page(dto);
    }

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public Long saveOrUpdateUser(UserBaseInfoDo userBaseInfoDo) {
        if (userBaseInfoDo == null) {
            throw new ServerException("parameter is null");
        }
        Long userId = userBaseInfoRepository.selectUserId(userBaseInfoDo);
        if (userId == null) {
            userBaseInfoDo.setUserId(snowflakeUtil.createId());
            userId = userBaseInfoRepository.insert(userBaseInfoDo);
        } else {
            userBaseInfoDo.setUserId(userId);
            userBaseInfoRepository.update(userBaseInfoDo);
        }
        return userId;
    }

    public List<UserBaseInfoDo> listByEmpId(Long empId) {
        return userBaseInfoRepository.listByEmpId(empId);
    }

    public List<UserBaseInfoDo> listByEmpIdAndOnboarding(Long empId, Boolean onboarding) {
        return userBaseInfoRepository.listByEmpIdAndOnboarding(empId, onboarding);
    }

    public List<UserBaseInfoDo> listByEmpIds(List<Long> empIds) {
        return userBaseInfoRepository.listByEmpIds(empIds);
    }

    public List<UserBaseInfoDo> listByEmpId(Long empId, Long tenantId) {
        return userBaseInfoRepository.listByEmpId(empId, tenantId);
    }

    public void syncUpdate(List<UserBaseInfoDo> dataList) throws Exception {
        userBaseInfoDo.syncUpdate(dataList);
    }

    public void softDeleteByEmpId(String empId) {
        userBaseInfoDo.softDeleteByEmpId(empId);
    }

    /**
     * 更新用户最后登录时间
     *
     * @param userId 用户ID
     */
    public void updateLastLoginTime(Long userId) {
        if (userId == null) {
            return;
        }
        Optional<UserBaseInfoDo> userOptional = getByUserId(userId);
        if (userOptional.isPresent()) {
            UserBaseInfoDo user = userOptional.get();
            user.setLastLoginTime(System.currentTimeMillis());
            user.setUpdateTime(System.currentTimeMillis());
            userBaseInfoDo.updateById(user);
        }
    }
}
