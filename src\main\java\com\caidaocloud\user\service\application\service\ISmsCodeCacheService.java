package com.caidaocloud.user.service.application.service;


import com.caidaocloud.user.service.application.enums.SmsCodeType;

public interface ISmsCodeCacheService {

    /**
     * 生成验证码
     *
     * @return 验证码
     */
    String generateSmsCode();


    /**
     * 保存验证码
     *
     * @param mobile     手机号
     * @param code       验证码
     * @param expireTime 保存时间 单位：秒
     */
    void saveSmsCode(String mobile, String code, long expireTime, SmsCodeType smsCodeType);

    /**
     * 获取验证码
     *
     * @param mobile 手机号
     * @return 验证码
     */
    String getSmsCode(String mobile, SmsCodeType smsCodeType);

    /**
     * 删除验证码
     *
     * @param mobile
     */
    void removeSmsCode(String mobile, SmsCodeType smsCodeType);

}
