package com.caidaocloud.user.service.application.dto.operate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @date 2022/5/22
 **/
@Data
public class ImportExcelProcessDto {

    /**
     * 进度唯一标识
     */
    private String processUUid;

    /**
     * 总条数
     */
    private int total;

    /**
     * 完成条数
     */
    private int completed;

    /**
     * 未完成条数
     */
    private int notDone;

    /**
     * 已知成功条数
     */
    private int successCount;

    /**
     * 已知失败条数
     */
    private int failCount;

    /**
     * 完成百分比
     */
    private double percentage;

    public ImportExcelProcessDto() {
        this.total = 0;
        this.completed = 0;
        this.notDone = 0;
        this.successCount = 0;
        this.failCount = 0;
        this.percentage = 0;
    }

    public int getNotDone() {
        int notDone = this.total - this.completed;
        return notDone;
    }

    public double getPercentage() {
        this.percentage = (double) completed / (double) total * 100;
        DecimalFormat df = new DecimalFormat("#.00");
        this.percentage = Double.valueOf(df.format(percentage));
        return percentage;
    }

}
