-- 用户登录日志表
create table if not exists user_login_log
(
    id             varchar(50)  not null comment '日志ID',
    user_id        bigint comment '用户ID',
    tenant_id      varchar(50) comment '租户ID',
    account        varchar(100) comment '用户账号',
    user_name      varchar(100) comment '用户名',
    login_time     datetime     not null comment '登录时间',
    login_platform integer comment '登录平台：0为web端，1为移动端, 2 第三方为web端，3 为第三方移动端',
    login_type     integer comment '登录方式：0为账号密码登录，1:为手机验证码登录,2为微信扫描登录，3为支付宝扫码登录，4为腾讯sso登录',
    device_type    integer comment '登录设备类型：web:0 android:1,ios:2',
    ip_address     varchar(50) comment '登录IP地址',
    user_agent     varchar(500) comment '用户代理信息（浏览器信息）',
    login_status   integer      not null default 1 comment '登录状态：1成功，0失败',
    fail_reason    varchar(200) comment '登录失败原因',
    session_id     varchar(100) comment '会话ID',
    ext_info       varchar(500) comment '扩展信息',
    create_time    datetime     not null comment '创建时间',
    primary key (id)
) comment '用户登录日志表' charset = utf8mb4;

-- 创建索引
create index if not exists idx_user_login_log_user_id on user_login_log (user_id);
create index if not exists idx_user_login_log_tenant_id on user_login_log (tenant_id);
create index if not exists idx_user_login_log_login_time on user_login_log (login_time);
create index if not exists idx_user_login_log_ip_address on user_login_log (ip_address);
create index if not exists idx_user_login_log_account on user_login_log (account);
