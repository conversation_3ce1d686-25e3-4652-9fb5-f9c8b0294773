package com.caidaocloud.user.service.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("租户信息")
public class TenantBaseInfoVo {
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("租户名称")
    private String tenantName;
    @ApiModelProperty("租户代码")
    private String tenantCode;
    @ApiModelProperty("租户logo")
    private String logo;
    @ApiModelProperty("集团公司ID")
    private Long corpId;
    @ApiModelProperty("集团公司唯一编码")
    private String corpCode;
}
