package com.caidaocloud.user.service.application.foreign.service;

import com.caidaocloud.util.PropUtil;
import com.caidaocloud.util.StringUtil;

public final class SmsSendFactory {

    public static String smsType = null;

    public static IForeignSmsService getSmsSendService(String signType) {
        return IForeignSmsService.smsManager.get(signType);
    }

    public static IForeignSmsService getSmsSendService() {
        if (StringUtil.isEmpty(smsType)) {
            smsType = PropUtil.getProp("caidaocloud.smsType");
            smsType = StringUtil.isEmpty(smsType) ? "aliyun" : smsType;
        }

        return IForeignSmsService.smsManager.get(smsType);
    }
}
