package com.caidaocloud.user.service;

import com.caidaocloud.security.dto.TokenDto;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.session.SessionUtil;
import com.caidaocloud.security.token.TokenVerify;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class LoginDtoTest {
    @Resource
    private ISessionService sessionRepository;

    @Test
    public void login(){
        String loginUserJson = "{\"deviceType\":0,\"extMap\":{\"globalid\":\"90033\",\"corpkey\":\"TencentHR\",\"userName\":\"TencentHR\",\"corpcode\":\"TENCENT\"},\"loginPlatform\":2,\"loginType\":4,\"thirdId\":\"90033\",\"thirdPart\":\"TencentHR\"}";
        LoginDto loginDto = FastjsonUtil.toObject(loginUserJson, LoginDto.class);

        log.info("test login body=[{}]", FastjsonUtil.toJson(loginDto));
        //ITokenGranter granter = TokenGranterBuilder.getGranter(loginDto.getLoginType());
//        Result result = granter.grant(loginDto);
//        log.info("login result={}", FastjsonUtil.toJson(result));
    }

    @Test
    public void accessToken(){
        String accessToken = "eyJhbGciOiJIUzUxMiJ9.eyJ0ZW5hbnRJZCI6ImNhaWRhb3Rlc3QiLCJ0aW1lIjoxNjM1MTU3MzU1MTE0LCJ0eXBlIjoyLCJ1c2VySWQiOiI4MzM5MSJ9.r-qIozXTLYGigbFcSaW4wbNBjjQLt0NvWgLEHpI8Lqy1t_9V7nyN44qHJ3C04h8XTHHuyB5XyullTzvNelVzPg";
        TokenDto tokenDataModel = TokenVerify.getTokenDataModel(accessToken);
        log.info("tokenDataModel={}", FastjsonUtil.toJson(tokenDataModel));

        String sessionKey = SessionUtil.getSessionKeyByTokenDataModel(tokenDataModel);
        if (StringUtil.isEmpty(sessionKey) || null == sessionRepository.getUserInfo()) {
            log.error("sessionKey=[{}],tokenDto=[{}]", sessionKey, FastjsonUtil.toJson(tokenDataModel));
        }

        sessionRepository.doUpdate();
    }
}
