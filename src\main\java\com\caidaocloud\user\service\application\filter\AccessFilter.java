package com.caidaocloud.user.service.application.filter;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.session.SessionUtil;
import com.caidaocloud.user.service.domain.entity.UserTenant;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.web.RequestHelper;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/7/2021 8:08 PM
 * 4
 */
@Component
public class AccessFilter implements Filter {

    @Autowired
    private ISessionService sessionService;
    @Autowired
    private UserTenant userTenant;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        ServletRequest requestWrapper = null;
        if (servletRequest instanceof HttpServletRequest) {
            requestWrapper = new BodyReaderWrapper((HttpServletRequest) servletRequest);
        }
        if (requestWrapper == null) {
            filterChain.doFilter(servletRequest, servletResponse);
        } else if (isCloudLoginUri()) {
            // 过滤openCloud登录地址
        } else if(isNewLoginUri()){
        } else if (isLoginUri()) {
            String contentStr = "";
            ServletInputStream is = requestWrapper.getInputStream();
            contentStr = IOUtils.toString(is, "utf-8");
            LoginDto loginRequest = JSON.parseObject(contentStr, LoginDto.class);

            doLoginDto(loginRequest, requestWrapper);
        }
        filterChain.doFilter(requestWrapper, servletResponse);
    }

    private void doLoginDto(LoginDto loginRequest, ServletRequest requestWrapper){
        if(null == loginRequest){
            return;
        }

        if(null != loginRequest.getLoginType() && 4 == loginRequest.getLoginType()){
            requestWrapper.setAttribute(SessionUtil.LOGIN_PLATFORM, loginRequest.getLoginPlatform());
            requestWrapper.setAttribute(SessionUtil.SESSION_THIRD_PART_IDENTITY, loginRequest.getThirdPart());
            requestWrapper.setAttribute(SessionUtil.SESSION_THIRD_ID_IDENTITY, loginRequest.getThirdPart() + loginRequest.getThirdId());
            sessionService.doCreate();
            return;
        }

        if (StringUtils.isNotEmpty(loginRequest.getAccount())) {
            requestWrapper.setAttribute(SessionUtil.LOGIN_PLATFORM, loginRequest.getLoginPlatform());
            UserTenant userTenantR = userTenant.getByUserIdOrAccount(0, loginRequest.getAccount());
            if (userTenantR != null) {
                requestWrapper.setAttribute(SessionUtil.SESSION_USER_IDENTITY, userTenantR.getUserId());
                requestWrapper.setAttribute(SessionUtil.SESSION_TENANT_IDENTITY, userTenantR.getTenantId());
                sessionService.doCreate();
            }
        }
    }

    private boolean isLoginUri() {
        boolean isLoginUri = false;
        HttpServletRequest httpServletRequest = RequestHelper.getRequest();
        String loginUri = httpServletRequest.getRequestURI();
        if (StringUtils.endsWithAny(loginUri, "login")) {
            isLoginUri = true;
        }
        return isLoginUri;
    }

    private boolean isCloudLoginUri() {
        HttpServletRequest httpServletRequest = RequestHelper.getRequest();
        String loginUri = httpServletRequest.getRequestURI();
        return StringUtils.endsWithAny(loginUri, "login")
                && StringUtils.isNotEmpty("cloud-receipt");
    }

    private boolean isNewLoginUri() {
        boolean isLoginUri = false;
        HttpServletRequest httpServletRequest = RequestHelper.getRequest();
        String loginUri = httpServletRequest.getRequestURI();
        if (StringUtils.endsWithAny(loginUri, "/account/login","/receipt/login")) {
            isLoginUri = true;
        }
        return isLoginUri;
    }
}
