package com.caidaocloud.user.service.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.user.service.domain.entity.UserSsoInfo;
import com.caidaocloud.user.service.domain.repository.IAuthorizeRepository;
import com.caidaocloud.user.service.infrastructure.repository.mapper.UserSsoInfoMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class AuthorizeRepositoryImpl implements IAuthorizeRepository {
    @Resource
    private UserSsoInfoMapper ssoInfoMapper;

    @Override
    public UserSsoInfo getById(Long id) {
        return ssoInfoMapper.selectById(id);
    }

    @Override
    public UserSsoInfo getByCode(String tenantCode) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("tenant_code", "tenantCode");
        return ssoInfoMapper.selectOne(queryWrapper);
    }
}
