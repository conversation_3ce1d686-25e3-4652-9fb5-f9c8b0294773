package com.caidaocloud.user.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.user.service.domain.entity.HisUserPwdRecDo;
import com.caidaocloud.user.service.domain.repository.IHisUserPwdRecRepository;
import com.caidaocloud.user.service.infrastructure.repository.mapper.HisUserPwdRecMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.HisUserPwdRec;
import com.caidaocloud.util.ObjectConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class HisUserPwdRecRepository implements IHisUserPwdRecRepository {
    @Autowired
    private HisUserPwdRecMapper hisUserPwdRecMapper;

    @Override
    public void insertBatch(List<HisUserPwdRecDo> dataList) {
        hisUserPwdRecMapper.insertBatch(ObjectConverter.convertList(dataList, HisUserPwdRec.class));
    }

    @Override
    public void insert(HisUserPwdRecDo data) {
        hisUserPwdRecMapper.insert(ObjectConverter.convert(data, HisUserPwdRec.class));
    }

    @Override
    public void update(HisUserPwdRecDo data) {
        hisUserPwdRecMapper.updateById(ObjectConverter.convert(data, HisUserPwdRec.class));
    }

    @Override
    public int insertSelective(HisUserPwdRecDo record) {
        return hisUserPwdRecMapper.insertSelective(ObjectConverter.convert(record, HisUserPwdRec.class));
    }

    @Override
    public int updateByPrimaryKeySelective(HisUserPwdRecDo record) {
        return hisUserPwdRecMapper.updateByPrimaryKeySelective(ObjectConverter.convert(record, HisUserPwdRec.class));
    }

    @Override
    public void delete(List<Long> ids) {
        hisUserPwdRecMapper.deleteBatchIds(ids);
    }

    @Override
    public List<HisUserPwdRecDo> getListByAccountId(Long tenantId, Long accountId) {
        LambdaQueryWrapper<HisUserPwdRec> queryWrapper = new QueryWrapper<HisUserPwdRec>().lambda();
        queryWrapper.eq(HisUserPwdRec::getTenantId, tenantId).eq(HisUserPwdRec::getAccountId, accountId);
        queryWrapper.ne(HisUserPwdRec::getDeleted, 1);
        queryWrapper.orderByDesc(HisUserPwdRec::getCreateTime);
        List<HisUserPwdRec> list = hisUserPwdRecMapper.selectList(queryWrapper);
        return ObjectConverter.convertList(list, HisUserPwdRecDo.class);
    }

    @Override
    public List<HisUserPwdRecDo> getPageListByAccountId(Long tenantId, Long accountId, long current, long size) {
        LambdaQueryWrapper<HisUserPwdRec> queryWrapper = new QueryWrapper<HisUserPwdRec>().lambda();
        queryWrapper.eq(HisUserPwdRec::getTenantId, tenantId).eq(HisUserPwdRec::getAccountId, accountId);
        queryWrapper.ne(HisUserPwdRec::getDeleted, 1);
        queryWrapper.orderByDesc(HisUserPwdRec::getCreateTime);
        Page<HisUserPwdRec> page = new Page<>(current, size);
        IPage<HisUserPwdRec> pageList = hisUserPwdRecMapper.selectPage(page, queryWrapper);
        return ObjectConverter.convertList(pageList.getRecords(), HisUserPwdRecDo.class);
    }
}
