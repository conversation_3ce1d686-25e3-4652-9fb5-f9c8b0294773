package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.google.common.collect.Lists;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.List;
import java.util.Optional;

/**
 * created by: FoAng
 * create time: 31/8/2022 4:09 下午
 */
@Service
public class ThirdTokenLoginService implements IAccountLoginService{

    @Value("${sso.login.thirdSignKey:}")
    private String thirdSignKey;

    @Value("${sso.login.tenantId:}")
    private String tenantId;

    @Value("${sso.login.thirdBidKey:user_id}")
    private String thirdBidKey;

    @Autowired
    private UserBaseInfoService userBaseInfoService;

    @Override
    public GrantType getGrantType() {
        return GrantType.SSO_TOKEN_GRANT_TYPE;
    }

    private Claims verifyThirdToken(final String token, String signKey) {
        Claims claims;
        try {
            byte[] key = Base64.getDecoder().decode(signKey);
            claims = Jwts.parser().setSigningKey(key).parseClaimsJws(token).getBody();
        } catch (ExpiredJwtException var2) {
            return var2.getClaims();
        } catch (Exception var3) {
            return null;
        }
        return claims;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        final String thirdToken = loginDto.getThirdId();
        if (StringUtils.isEmpty(thirdToken)) {
            throw new ServerException("third token required");
        } else {
            val userInfo = new SecurityUserInfo();
            userInfo.setTenantId(tenantId);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            Claims claims = verifyThirdToken(thirdToken, thirdSignKey);
            return Optional.ofNullable(claims)
                    .map(it -> {
                        final String thirdBid = (String) it.get(thirdBidKey);
                        if (!StringUtils.isEmpty(thirdBid)) {
                            UserBaseInfoDo userBaseInfo = userBaseInfoService.getByUserId(Long.valueOf(thirdBid));
                            SecurityUserUtil.removeSecurityUserInfo();
                            if (userBaseInfo != null) {
                                return Lists.newArrayList(userBaseInfo);
                            }
                        }
                        return null;
                    }).orElseThrow(() -> new ServerException("token错误"));
        }
    }


    /*sso.login.service 需要配置参数*/
    @Override
    public String getSsoServiceKey() {
        return "third.token";
    }
}
