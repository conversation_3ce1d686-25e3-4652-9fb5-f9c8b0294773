package com.caidaocloud.user.service.application.service;

import com.caidaocloud.user.service.application.dto.UserInfoDto;
import com.caidaocloud.user.service.application.dto.operate.CreateAccountDto;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoDto;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class AccountBaseInfoService {

    @Autowired
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Autowired
    private UserBaseInfoService userBaseInfoService;

    public List<AccountBaseInfoDo> getAccountInfoList(String account) {
        return accountBaseInfoDomainService.getAccountInfoList(account);
    }

    public AccountBaseInfoDo getAccountInfo(String account) {
        return accountBaseInfoDomainService.getAccountInfo(account);
    }

    public Long saveOrUpdateAccount(AccountBaseInfoDo data) {
        return accountBaseInfoDomainService.syncSave(data);
    }

    public List<AccountBaseInfoDo> getAccount(String mobNum, String emails) {
        return accountBaseInfoDomainService.getAccount(mobNum, emails);
    }

    /**
     * 创建帐户和用户
     *
     * @param accountDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public UserInfoDto createAccount(CreateAccountDto accountDto) {
        Long accountId;
        if (accountDto.getAccountId() == null) {
            accountId = saveOrUpdateAccount(accountDto.getAccount());
        } else {
            accountId = accountDto.getAccountId();
        }
        AccountBaseInfoDo account = accountBaseInfoDomainService.getAccountByAccountId(accountId);
        UserBaseInfoDto accountBaseInfoDto = new UserBaseInfoDto();
        accountBaseInfoDto.setAccountId(accountId);
        accountBaseInfoDto.setAccount(account.getAccount());
        accountBaseInfoDto.setEmpId(accountDto.getEmpId());
        accountBaseInfoDto.setUserName(accountDto.getName());
        accountBaseInfoDto.setTenantId(Long.parseLong(accountDto.getTenantId()));
        accountBaseInfoDto.setStatus(1);
        accountBaseInfoDto.setCorpId(accountDto.getCorpid());
        Long userId = userBaseInfoService.saveOrUpdateUserBaseInfo(accountBaseInfoDto);
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setAccountId(accountId);
        userInfoDto.setUserId(userId);
        userInfoDto.setAccount(account.getAccount());
        return userInfoDto;
    }

    public void updateByPrimaryKey(AccountBaseInfoDo data) {
        accountBaseInfoDomainService.updateByPrimaryKey(data);
    }
}
