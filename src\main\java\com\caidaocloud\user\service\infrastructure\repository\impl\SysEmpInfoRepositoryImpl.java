package com.caidaocloud.user.service.infrastructure.repository.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.dto.SysEmpInfoDto;
import com.caidaocloud.user.service.application.dto.masterdata.EmpSearchDto;
import com.caidaocloud.user.service.application.dto.masterdata.EmpWorkInfoDto;
import com.caidaocloud.user.service.application.feign.IMasterdataFeign;
import com.caidaocloud.user.service.domain.repository.ISysEmpInfoRepository;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2023/5/26
 */
@Repository
@Slf4j
public class SysEmpInfoRepositoryImpl implements ISysEmpInfoRepository {
	@Autowired
	private IMasterdataFeign masterdataFeign;

	@Override
	public List<SysEmpInfoDto> getEmpInfoByEmpIds(String empIds) {
		EmpSearchDto dto = new EmpSearchDto();
		dto.setEmpIds(Arrays.asList(empIds.split(",")));
		dto.setDatetime(System.currentTimeMillis());
		Result<List<EmpWorkInfoDto>> result = masterdataFeign.getEmpInfoByEmpIds(dto);
		if (!result.isSuccess()) {
			throw new ServerException("获取员工信息失败");
		}
		return Sequences.sequence(result.getData())
				.map(emp -> {
					SysEmpInfoDto info = ObjectConverter.convert(emp, SysEmpInfoDto.class);
					info.setEmpid(emp.getEmpId());
					info.setEmail(emp.getCompanyEmail());
					return info;
				}).toList();
	}

	@Override
	public List<SysEmpInfoDto> getEmpInfoByWorkno(String workno) {
		Result<EmpWorkInfoDto> result = masterdataFeign.loadEmpInfoByWorkno(workno, System.currentTimeMillis());
		if (!result.isSuccess() ) {
			log.error("error request to masterdata, parameter=[{}]", workno);
			throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_60007"));
		}
		EmpWorkInfoDto emp = result.getData();
		if (emp==null) {
			return new ArrayList<>();
		}
		SysEmpInfoDto info = ObjectConverter.convert(emp, SysEmpInfoDto.class);
		info.setEmpid(emp.getEmpId());
		info.setEmail(emp.getCompanyEmail());
		return Lists.list(info);
	}

	@Override
	public List<SysEmpInfoDto> getEmpInfoByWorknoAndEmail(String workno, String companyEmail) {
		EmpSearchDto dto = new EmpSearchDto();
		dto.setWorkNo(workno);
		dto.setKeyword(workno);
		dto.setCompanyEmail(companyEmail);
		dto.setDatetime(System.currentTimeMillis());
		dto.setPageNo(1);
		dto.setPageSize(100);
		Result<List<EmpWorkInfoDto>> result = masterdataFeign.listByWorknoAndEmail(dto);
		if (!result.isSuccess()) {
			throw new ServerException("getEmpInfoByWorknoAndEmail fail !!!!");
		}
		return Sequences.sequence(result.getData())
				.map(emp -> {
					SysEmpInfoDto info = ObjectConverter.convert(emp, SysEmpInfoDto.class);
					info.setEmpid(emp.getEmpId());
					info.setEmail(emp.getCompanyEmail());
					return info;
				}).toList();
	}
}
