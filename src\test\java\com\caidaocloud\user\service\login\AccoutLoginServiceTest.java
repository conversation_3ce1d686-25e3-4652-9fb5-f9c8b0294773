package com.caidaocloud.user.service.login;

import com.caidaocloud.user.service.application.service.login.IAccountLoginService;
import com.caidaocloud.user.service.application.service.login.ThirdTokenLoginService;
import com.caidaocloud.user.service.application.service.login.WorkNoAutoLoginService;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AccoutLoginServiceTest {
    ConcurrentHashMap<GrantType, IAccountLoginService> grantServiceManager = new ConcurrentHashMap();
    @Resource
    private WorkNoAutoLoginService workNoAutoLoginService;
    @Resource
    private ThirdTokenLoginService thirdTokenLoginService;
    @Test
    public void testServiceInit(){
        // ssoGrantServiceManager

        grantServiceManager.put(workNoAutoLoginService.getGrantType(), workNoAutoLoginService);
        grantServiceManager.put(thirdTokenLoginService.getGrantType(), thirdTokenLoginService);

        IAccountLoginService iAccountLoginService = grantServiceManager.get(GrantType.SSO_TOKEN_GRANT_TYPE);
        iAccountLoginService.getSsoServiceKey();
    }
}
