create table if not exists tenant_base_info
(
    tenant_id   bigint      not null comment '租户ID',
    tenant_name varchar(50) not null comment '租户名称',
    tenant_code varchar(50) comment '租户代码',
    logo        varchar(200) comment '租户logo',
    corp_id     bigint comment '集团公司ID',
    corp_code   varchar(50) comment '集团公司唯一编码',
    create_by   bigint      not null comment '创建人',
    create_time bigint      not null comment '创建时间',
    update_by   bigint comment '修改人',
    update_time bigint comment '修改时间',
    deleted     integer              default 0 not null comment '删除状态 0 未删除 1 已删除',
    status      integer     not null default 1 comment '租户状态',
    primary key (tenant_id)
    ) comment '租户基本信息表' charset = utf8mb4;

create table if not exists account_base_info
(
    account_id           bigint  not null comment '账号ID',
    account_login_prefix varchar(50) comment '使用用户统一注册账号登录时的前缀，例如：caidao-，登录时输入：前缀-账号进行登录',
    account              varchar(50) comment '用户统一注册账号，例如统一工号',
    mob_num              varchar(50) comment '注册手机号',
    email                varchar(50) comment '注册邮箱',
    password             varchar(100) comment '密码',
    salt                 varchar(50) comment '盐值',
    gesture              varchar(50) comment '手势密码',
    status               integer not null default 1 comment '账号状态：1 正常 2 停用 3 锁定',
    reg_type             varchar(50) comment '注册方式、注册类型',
    create_by            bigint  not null comment '创建人',
    create_time          bigint  not null comment '创建时间',
    update_by            bigint comment '修改人',
    update_time          bigint comment '修改时间',
    deleted              integer          default 0 not null comment '删除状态 0 未删除 1 已删除',
    primary key (account_id)
    ) comment '账号基本信息表' charset = utf8mb4;

create table if not exists user_base_info
(
    user_id       bigint      BIGINT NOT NULL AUTO_INCREMENT comment '用户ID',
    account_id    bigint      not null comment '账号信息ID',
    account       varchar(50) comment '用户统一注册账号，例如统一工号',
    tenant_id     bigint      not null comment '所属租户ID',
    user_name     varchar(50) not null comment '用户姓名',
    sex           integer comment '性别',
    head_portrait varchar(200) comment '头像',
    status        integer     not null default 1 comment '用户状态：1 正常 2 停用 3 锁定',
    if_default    boolean              default false comment '是否为默认登录用户',
    emp_id        bigint comment '此用户关联的员工ID',
    corp_id       bigint comment '集团公司ID',
    ext_info      varchar(500) comment '用户扩展信息',
    create_by     bigint      not null comment '创建人',
    create_time   bigint      not null comment '创建时间',
    update_by     bigint comment '修改人',
    update_time   bigint comment '修改时间',
    deleted       integer              default 0 not null comment '删除状态 0 未删除 1 已删除',
    primary key (user_id)
    ) comment '用户基本信息表' charset = utf8mb4;

create table if not exists pwd_rule
(
    pwd_rule_id       bigint            not null comment '密码规则ID',
    tenant_id         bigint            not null comment '租户ID',
    is_first_rule     boolean default false comment '是否启用初始、重置密码生成规则',
    first_rule        varchar(20) comment '初始密码生成规则的复杂度:1、数字 2、大写字母 3、小写字母 4 特殊字符 5、手机号码后六位 6、身份证后六位',
    first_length      integer comment '初始密码生成规则的密码长度',
    is_valid          boolean default false comment '是否启用修改密码规则',
    is_pwd_change     boolean default false comment '使用初始密码登陆后是否强制修改密码',
    pwd_len1          integer comment '密码长度（最小长度）',
    pwd_len2          integer comment '密码长度（最大长度）',
    pwd_complexity    varchar(20) comment '密码复杂度：1.包含数字、2.大写字母、3.小写字母 4.特殊字符',
    not_pwd_same_num  integer comment '不可与前多少次历史密码相同：自定义次数',
    pwd_valid_time    integer comment '密码期限：自定义密码有效时长，单位为天',
    pwd_expires_day   integer default 0 comment '密码到期前提醒天数',
    lock_account_num  integer default 5 comment '登录时密码错误超过次数限制将被锁定：默认5次',
    lock_account_time integer comment '超过锁定次数后的锁定时间：自定义密码锁定时间，单位为分钟',
    auto_unlock_time  integer comment '自动解锁时间，锁定用户时更新锁定时间到用户表上，在根据 锁定时间判断是否可解锁',
    corp_id           bigint comment '集团公司ID',
    create_by         bigint            not null comment '创建人',
    create_time       bigint            not null comment '创建时间',
    update_by         bigint comment '修改人',
    update_time       bigint comment '修改时间',
    deleted           integer default 0 not null comment '删除状态 0 未删除 1 已删除',
    primary key (pwd_rule_id)
    ) comment '密码规则表' charset = utf8mb4;

create table if not exists private_environment
(
    id            bigint            not null comment '主键',
    tenant_id     bigint            not null comment '租户ID',
    tenant_name   varchar(50)       not null comment '租户名称',
    tenant_code   varchar(50) comment '租户代码',
    url           varchar(200) comment '客户地址',
    matched_email varchar(100) comment '匹配邮箱',
    type          varchar(50) comment '类型',
    create_by     bigint comment '创建人',
    create_time   bigint comment '创建时间',
    update_by     bigint comment '修改人',
    update_time   bigint comment '修改时间',
    deleted       integer default 0 not null comment '删除状态 0 未删除 1 已删除',
    primary key (id)
    ) comment '客户环境表' charset = utf8mb4;

create table if not exists his_user_pwd_rec
(
    rec_id      bigint            not null comment '修改记录ID',
    account_id  bigint            not null comment '账号ID',
    user_id     bigint comment '用户ID',
    tenant_id   bigint            not null comment '租户ID',
    password    varchar(100) comment '密码',
    salt        varchar(50) comment '盐值',
    create_by   bigint            not null comment '创建人',
    create_time bigint            not null comment '创建时间',
    update_by   bigint comment '修改人',
    update_time bigint comment '修改时间',
    deleted     integer default 0 not null comment '删除状态 0 未删除 1 已删除',
    primary key (rec_id)
    ) comment '用户密码历史表' charset = utf8mb4;

create table if not exists user_sso_info
(
    id               bigint      not null comment '主键',
    tenant_id        bigint      not null comment '租户ID',
    tenant_code      varchar(50) comment '租户CODE',
    appid            varchar(64) not null comment '租户appid',
    appsecret        varchar(255) comment '租户appsecert',
    corpid           varchar(64) comment '企业id',
    corpsecret       varchar(255) comment '企业secert',
    sso_type         integer comment '1企业号；2公众号；3第三方app；4标准SSO',
    auth_page_url    varchar(255) comment '租户认证的页面地址',
    auth_success_url varchar(500) comment '租户认证成功的页面地址',
    push_msg_type    integer comment '推送消息到第三方；1推送到公众号；2推送到企业微信；3推送到第三方app',
    server_host      varchar(255) comment '第三方服务器地址',
    home_page        varchar(500) comment '认证后的首页',
    auth_type        integer default 0 comment '0企业微信认证；1工号认证；2公司邮箱认证；3手机号认证',
    primary key (id)
    ) comment '租户标准SSO单点登录表' charset = utf8mb4;