package com.caidaocloud.user.service.domain.entity;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.caidaocloud.user.service.domain.repository.IUserBaseInfoRepository;
import com.caidaocloud.user.service.domain.util.ListUtil;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoQueryDto;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
@Data
public class UserBaseInfoDo extends BaseEntity {
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 账号ID
     */
    private Long accountId;
    /**
     * 用户统一注册账号
     */
    private String account;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 头像
     */
    private String headPortrait;
    /**
     * 用户状态：1 正常 2 停用 3 锁定
     */
    private Integer status;
    /**
     * 是否为默认用户
     */
    private Boolean ifDefault=false;
    /**
     * 员工ID
     */
    private Long empId;
    /**
     * 集团公司ID
     */
    private Long corpId;
    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 是否候选人账号
     */
    private Boolean onboarding;

    /**
     * 钉钉userId
     */
    private String dingUserId;

    private Long lastLoginTime;


    /**
     * 二次入职原有员工id
     */
    private Long reEmpId;

    @Autowired
    private IUserBaseInfoRepository userBaseInfoRepository;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    /***
     * 同步接入用户信息
     *
     * @param dataList
     * @throws Exception
     */
    @Transactional
    public void syncSave(List<UserBaseInfoDo> dataList) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(dataList);
        // 数据保存
        List<List<UserBaseInfoDo>> lists = ListUtil.split(dataList, 500);
        for (List<UserBaseInfoDo> list : lists) {
            for (UserBaseInfoDo baseInfoDo : list) {
                if (baseInfoDo.getUserId() == null) {
                    baseInfoDo.setUserId(snowflakeUtil.createId());
                }
            }
            userBaseInfoRepository.insertBatch(list);
        }
    }

    @Transactional
    public void syncUpdate(List<UserBaseInfoDo> dataList) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(dataList);
        // 数据保存
        List<List<UserBaseInfoDo>> lists = ListUtil.split(dataList, 500);
        for (List<UserBaseInfoDo> list : lists) {
            userBaseInfoRepository.updateBatch(list);
        }
    }

    public void updateById(UserBaseInfoDo user) {
        userBaseInfoRepository.update(user);
    }

    public int deleteByIds(List<Long> userIds) {
        return userBaseInfoRepository.deleteByIds(userIds);
    }

    public void softDeleteByIds(List<Long> userIds) {
        userBaseInfoRepository.softDeleteByIds(userIds);
    }

    public List<UserBaseInfoDo> selectListByAccountId(Long accountId) {
        return userBaseInfoRepository.selectListByAccountId(accountId);
    }

    public List<UserBaseInfoDo> selectListByAccountIds(List<Long> accountIds,boolean tenantFiltered) {
        return userBaseInfoRepository.selectListByAccountIds(accountIds, tenantFiltered);
    }

    public List<UserBaseInfoDo> selectListByWorkNo(String tenantId, String workNo) {
        return userBaseInfoRepository.selectListByWorkNo(tenantId, workNo);
    }


    public UserBaseInfoDo getByUserId(Long userId) {
        return userBaseInfoRepository.selectByUserId(userId);
    }

    public List<UserBaseInfoDo> getByUserIds(List<Long> userIds) {
        return userBaseInfoRepository.getByUserIds(userIds);
    }

    public UserBaseInfoDo getUserInfoByAccount(Long corpId, String account) {
        return userBaseInfoRepository.getUserInfoByAccount(corpId, account);
    }

    public IPage<UserBaseInfoDo> page(String keywords, int pageNo, int pageSize,Boolean onBoarDing) {
        return userBaseInfoRepository.page(keywords, pageNo, pageSize,onBoarDing);
    }

    public IPage<UserBaseInfoDo> page(UserBaseInfoQueryDto dto) {
        return userBaseInfoRepository.page(dto);
    }

    public void softDeleteByEmpId(String empId) {
        userBaseInfoRepository.softDeleteByEmpId(empId);
    }

    public void updateDingUserIdByEmpId(Long empId, String dingUserId) {
        userBaseInfoRepository.updateDingUserIdByEmpId(empId, dingUserId);

    }
}
