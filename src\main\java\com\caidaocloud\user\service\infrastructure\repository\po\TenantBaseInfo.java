package com.caidaocloud.user.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.user.service.domain.entity.AbstractBaseEntity;
import lombok.Data;

@Data
@TableName("tenant_base_info")
public class TenantBaseInfo extends AbstractBaseEntity {
    /**
     * 租户ID
     */
    @TableId
    private Long tenantId;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 租户代码
     */
    private String tenantCode;
    /**
     * 租户logo
     */
    private String logo;
    /**
     * 集团公司ID
     */
    private Long corpId;

    /**
     * 集团公司唯一编码
     */
    private String corpCode;

    /**
     * 状态
     */
    private Integer status;
}
