package com.caidaocloud.user.service.application.feign;

import com.caidaocloud.user.service.application.dto.DingResponse;
import com.caidaocloud.user.service.application.dto.DingUserInfoResponse;
import com.caidaocloud.user.service.application.feign.fallback.DingTalkFeignClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(value = "dingTalkFeignClient",
        url = "https://oapi.dingtalk.com",
        fallback = DingTalkFeignClientFallback.class)
public interface DingTalkFeignClient {


    @GetMapping("/gettoken")
    Map<String, String> getSsoToken(@RequestParam("appkey") String appkey, @RequestParam("appsecret") String appsecret);


    @PostMapping("topapi/v2/user/getuserinfo")
    DingResponse getUserId(@RequestParam("code") String code, @RequestParam("access_token") String accessToken);


    @PostMapping("/topapi/v2/user/get")
    DingUserInfoResponse getUserInfo(@RequestParam("userid") String userid, @RequestParam("access_token") String accessToken);



}
