package com.caidaocloud.user.service.domain.repository;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/5/8
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class ITenantBaseInfoRepositoryTests {

	@Autowired
	private ITenantBaseInfoRepository repo;

	@Autowired
	private TenantBaseInfoDo tenantBaseInfoDo;

	@Before
	public void setUp() throws Exception {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		RequestHelper.getRequest().setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(userInfo, UserInfo.class));
		SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
		try {
			Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
			field.setAccessible(true);
			field.set(service, true);
		}
		catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);



	@Test
	public void testInsertBatch() {
		// Create test data
		// TenantBaseInfoDo tenant1 = new TenantBaseInfoDo(1L, "Tenant 1", "TNT1", 1L, "CRP1");
		// TenantBaseInfoDo tenant2 = new TenantBaseInfoDo(2L, "Tenant 2", "TNT2", 1L, "CRP1");
		// List<TenantBaseInfoDo> tenants = new ArrayList<>();
		// tenants.add(tenant1);
		// tenants.add(tenant2);
		//
		// // Call insertBatch method on repository
		// tenantBaseInfoDo.syncSave(tenants);

		// Verify that the data was inserted correctly
		List<TenantBaseInfoDo> savedData = repo.getTenantListByCorpCode("CRP1");
		assertEquals(2, savedData.size());
		// for (int i = 0; i < savedData.size(); i++) {
		// 	TenantBaseInfoDo excpet = tenants.get(i), data = savedData.get(i);
		// 	assertEquals(excpet.getTenantId(), data.getTenantId());
		// 	assertEquals(excpet.getTenantName(), data.getTenantName());
		// 	assertEquals(excpet.getTenantCode(), data.getTenantCode());
		// 	assertEquals(excpet.getCorpId(), data.getCorpId());
		// 	assertEquals(excpet.getCorpCode(), data.getCorpCode());
		// }
	}

}