package com.caidaocloud.user.service.application.dto;

import lombok.Data;

/**
 * 魔学院单点登陆配置类
 *
 * <AUTHOR>
 */
@Data
public class MxySsoConfigDto {
    /**
     * 绑定租户ID
     */
    private String tenantId;

    /**
     * 魔学院CorpId
     */
    private String corpId;

    /**
     * 魔学院corpSecret
     */
    private String CorpSecret;

    /**
     * 魔学院token
     * token Token和Key通常用于免登录和消息订阅推送等的加解密。
     */
    private String token;

    /**
     * 魔学院Key
     */
    private String mxyKey;

    /**
     * HostUrl
     */
    private String hostUrl = "https://open.moxueyuan.com/api/v1/sso/login";

    /**
     * pc端链接地址
     */
    private String pcUrl = "https://study.moxueyuan.com";

    /**
     * h5端链接地址
     */
    private String mbUrl = "https://m.moxueyuan.com";

    /**
     * 管理端地址
     */
    private String adminUrl = "https://admin.moxueyuan.com";

    /**
     * scope
     */
    private String scope = "base";
}
