package com.caidaocloud.user.service.domain.repository;

import java.util.List;

import com.caidaocloud.user.service.application.dto.SysEmpInfoDto;

import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 * @date 2023/5/26
 */
public interface ISysEmpInfoRepository {
	List<SysEmpInfoDto> getEmpInfoByEmpIds(@RequestParam("empIds") String empIds);


	List<SysEmpInfoDto> getEmpInfoByWorkno(@RequestParam("worknos") String workno);

	List<SysEmpInfoDto> getEmpInfoByWorknoAndEmail(@RequestParam("workno") String workno, @RequestParam("companyEmail") String companyEmail);
}
