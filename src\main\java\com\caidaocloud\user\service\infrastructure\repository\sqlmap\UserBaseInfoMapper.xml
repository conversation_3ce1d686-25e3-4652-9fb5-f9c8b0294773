<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.user.service.infrastructure.repository.mapper.UserBaseInfoMapper">
    <insert id="insertSelective"
            parameterType="com.caidaocloud.user.service.infrastructure.repository.po.UserBaseInfo">
        insert into user_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="account != null">
                account,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="headPortrait != null">
                head_portrait,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="ifDefault != null">
                if_default,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="corpId != null">
                corp_id,
            </if>
            <if test="extInfo != null">
                ext_info,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="onboarding != null">
                onboarding,
            </if>
            <if test="reEmpId != null">
                re_emp_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="account != null">
                #{account,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=INTEGER},
            </if>
            <if test="headPortrait != null">
                #{headPortrait,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="ifDefault != null">
                #{ifDefault,jdbcType=BIT},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=BIGINT},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=BIGINT},
            </if>
            <if test="extInfo != null">
                #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
            <if test="onboarding != null">
                #{onboarding,jdbcType=BIT},
            </if>
            <if test="reEmpId != null">
                #{reEmpId,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.caidaocloud.user.service.infrastructure.repository.po.UserBaseInfo">
        update user_base_info
        <set>
            <if test="accountId != null">
                account_id = #{accountId,jdbcType=BIGINT},
            </if>
            <if test="account != null">
                account = #{account,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=INTEGER},
            </if>
            <if test="headPortrait != null">
                head_portrait = #{headPortrait,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="ifDefault != null">
                if_default = #{ifDefault,jdbcType=BIT},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=BIGINT},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="extInfo != null">
                ext_info = #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
            <if test="onboarding != null">
                onboarding = #{onboarding,jdbcType=BIT},
            </if>
        </set>
        where user_id = #{userId,jdbcType=BIGINT}
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into user_base_info (user_id, account_id, account,
        tenant_id, user_name, sex,
        head_portrait, status, if_default,
        emp_id, corp_id, ext_info,
        create_by, create_time, update_by,
        update_time, deleted,onboarding)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.userId,jdbcType=BIGINT}, #{record.accountId,jdbcType=BIGINT}, #{record.account,jdbcType=VARCHAR},
            #{record.tenantId,jdbcType=BIGINT}, #{record.userName,jdbcType=VARCHAR}, #{record.sex,jdbcType=INTEGER},
            #{record.headPortrait,jdbcType=VARCHAR}, #{record.status,jdbcType=INTEGER},
            #{record.ifDefault,jdbcType=BIT},
            #{record.empId,jdbcType=BIGINT}, #{record.corpId,jdbcType=BIGINT}, #{record.extInfo,jdbcType=VARCHAR},
            #{record.createBy,jdbcType=BIGINT}, #{record.createTime,jdbcType=BIGINT},
            #{record.updateBy,jdbcType=BIGINT},
            #{record.updateTime,jdbcType=BIGINT}, #{record.deleted,jdbcType=INTEGER},
            #{record.onboarding,jdbcType=BIT})
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="records" item="bean" index="index" open="" close="" separator=";">
            update user_base_info
            <set>
                <if test="bean.account != null">
                    account = #{bean.account,jdbcType=VARCHAR},
                </if>
                <if test="bean.tenantId != null">
                    tenant_id = #{bean.tenantId,jdbcType=BIGINT},
                </if>
                <if test="bean.userName != null">
                    user_name = #{bean.userName,jdbcType=VARCHAR},
                </if>
                <if test="bean.sex != null">
                    sex = #{bean.sex,jdbcType=INTEGER},
                </if>
                <if test="bean.headPortrait != null">
                    head_portrait = #{bean.headPortrait,jdbcType=VARCHAR},
                </if>
                <if test="bean.status != null">
                    status = #{bean.status,jdbcType=INTEGER},
                </if>
                <if test="bean.ifDefault != null">
                    if_default = #{bean.ifDefault,jdbcType=BIT},
                </if>
                <if test="bean.empId != null">
                    emp_id = #{bean.empId,jdbcType=BIGINT},
                </if>
                <if test="bean.corpId != null">
                    corp_id = #{bean.corpId,jdbcType=BIGINT},
                </if>
                <if test="bean.extInfo != null">
                    ext_info = #{bean.extInfo,jdbcType=VARCHAR},
                </if>
                <if test="bean.createBy != null">
                    create_by = #{bean.createBy,jdbcType=BIGINT},
                </if>
                <if test="bean.createTime != null">
                    create_time = #{bean.createTime,jdbcType=BIGINT},
                </if>
                <if test="bean.updateBy != null">
                    update_by = #{bean.updateBy,jdbcType=BIGINT},
                </if>
                <if test="bean.updateTime != null">
                    update_time = #{bean.updateTime,jdbcType=BIGINT},
                </if>
                <if test="bean.deleted != null">
                    deleted = #{bean.deleted,jdbcType=INTEGER},
                </if>
                <if test="bean.onboarding != null">
                    onboarding = #{bean.onboarding,jdbcType=BIT},
                </if>
            </set>
            where user_id = #{bean.userId,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>