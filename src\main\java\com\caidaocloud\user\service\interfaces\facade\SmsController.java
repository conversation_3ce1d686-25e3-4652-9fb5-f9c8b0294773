package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.enums.SmsCodeType;
import com.caidaocloud.user.service.application.service.ISmsService;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.infrastructure.util.RegexUtil;
import com.caidaocloud.user.service.interfaces.dto.MobileCodeDto;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/user/sms/v2")
@Api(value = "/api/user/sms/v2", description = "短信服务接口", tags = "短信服务接口")
public class SmsController {
    @Autowired
    private ISmsService smsService;
    @Autowired
    private UserBaseInfoService userBaseInfoService;
    @Autowired
    private CacheService cacheService;

    private Result<Boolean> preCheck(String mobile, SmsCodeType smsCodeType) {
        // 登录、忘记密码、重置密码时用户账号检查
        if (smsCodeType != SmsCodeType.LOGIN && smsCodeType != SmsCodeType.RETRIEVE_PASSWORD
                && smsCodeType != SmsCodeType.RESET_PASSWORD) {
            return Result.ok(true);
        }

        List<UserBaseInfoDo> userList = userBaseInfoService.getUserListByAccount(mobile);
        // 检查用户是否存在
        if (CollectionUtils.isEmpty(userList)) {
            return Result.status(false, ErrorCodes.USER_NOT_EXISTS, LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));
        }
        // 只匹配到一个用户
        boolean singleUser = userList.size() == 1;
        if (singleUser) {
            // 检查账号是否已停用或已锁定
            long normalCount = userList.stream().filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).count();
            if (normalCount == 0) {
                // 账号已停用或已锁定，请联系管理员
                return Result.status(false, -1, LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));
            }
        }
        return Result.ok(true);
    }

    @GetMapping("/code")
    @ApiOperation("获短信验证码")
    public Result<Boolean> sendMessageCode(@RequestParam("mobile") String mobile,
        @RequestParam(name = "smsCodeType", defaultValue = "LOGIN") SmsCodeType smsCodeType) {
        String [] split = mobile.split("\\+");
        String code = split.length > 1 ? "+" + split[1] : "";
        if (("".equals(code) || "+86".equals(code)) && !RegexUtil.isMatchMobile(split[0])) {
            return Result.fail(LangUtil.getMsg(MsgCodeConstant.MOBILE_PHONE_FORMAT_ERROR));
        }

        // 检查
        Result<Boolean> checkResult = preCheck(split[0], smsCodeType);
        if (!checkResult.getData()) {
            return checkResult;
        }

        boolean tooMuch = true;
        for (int i = 0; i < 5; i++) {
            val exist = cacheService.containsKey("user_verify_code_count_" + split[0] + "_" + i);
            if (!exist) {
                cacheService.cacheValue("user_verify_code_count_" + split[0] + "_" + i, "1", 10 * 60);
                tooMuch = false;
                break;
            }
        }
        if (tooMuch) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_" + MsgCodeConstant.VERIFY_CODE_TOO_FREQUENTLY));
        }
        boolean sendResult = smsService.sendMessageCode(split[0], code, smsCodeType);
        return sendResult ? Result.status(true, 0, LangUtil.getMsg(MsgCodeConstant.SEND_SUCCESS))
                : Result.status(false, -1, LangUtil.getMsg(MsgCodeConstant.SEND_FAIL));
    }

    @PostMapping("/verify")
    @ApiOperation("验证短信验证码")
    public Result<Boolean> verify(@RequestBody MobileCodeDto dto) {
        String [] split = dto.getMobile().split("\\+");
        dto.setMobile(split[0]);
        String code = split.length > 1 ? "+" + split[1] : "";
        if (("".equals(code) || "+86".equals(code)) && !RegexUtil.isMatchMobile(dto.getMobile())) {
            return Result.fail(LangUtil.getMsg(MsgCodeConstant.MOBILE_PHONE_FORMAT_ERROR));
        }
        if (StringUtil.isEmpty(dto.getVerifyCode())) {
            return Result.fail(LangUtil.getMsg(MsgCodeConstant.VERIFICATION_CODE_CANNOT_EMPTY));
        }
        // 检查
        Result<Boolean> checkResult = preCheck(dto.getMobile(), dto.getSmsCodeType());
        if (!checkResult.getData()) {
            return checkResult;
        }
        return Result.ok(smsService.verify(dto));
    }
}
