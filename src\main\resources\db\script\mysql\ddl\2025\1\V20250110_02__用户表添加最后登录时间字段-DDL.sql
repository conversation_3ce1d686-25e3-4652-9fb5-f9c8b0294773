-- 为用户基础信息表添加最后登录时间字段
-- 作者: AI Assistant
-- 日期: 2025-01-10
-- 描述: 为用户信息新增lastLoginTime属性，记录用户上次登录时间

-- 检查字段是否已存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'user_base_info' 
     AND COLUMN_NAME = 'last_login_time') = 0,
    'ALTER TABLE user_base_info ADD COLUMN last_login_time BIGINT(20) NULL COMMENT ''最后登录时间''',
    'SELECT ''Column last_login_time already exists'' AS message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为MongoDB用户表添加索引（如果需要的话）
-- 注意：MongoDB的索引需要在应用程序中创建，这里只是注释说明
-- db.basic_e_user_[tenantId].createIndex({"lastLoginTime": 1})
