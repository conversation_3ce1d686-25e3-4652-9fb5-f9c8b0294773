package com.caidaocloud.user.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.user.service.domain.entity.AbstractBaseEntity;
import com.caidaocloud.user.service.domain.enums.PrivateEnvironmentType;
import lombok.Data;

@Data
@TableName("private_environment")
public class PrivateEnvironmentPo extends AbstractBaseEntity {

    @TableId
    private Long id;

    private Long tenantId;

    private String tenantName;

    private String tenantCode;

    private String url;

    private String matchedEmail;

    private PrivateEnvironmentType type;
}
