create table if not exists user_sso_info
(
    id   bigint      not null,
    tenant_id   bigint      not null,
    tenant_code varchar(50),
    appid                      varchar(64) not null,
    appsecret                  varchar(255),
    corpid                      varchar(64),
    corpsecret                  varchar(255),
    sso_type    integer,
    auth_page_url               varchar(255),
    auth_success_url       varchar(500),
    push_msg_type                integer,
    server_host             varchar(255),
    home_page               varchar(500),
    auth_type         integer default 0,
    primary key (id)
);

comment on table user_sso_info is '租户标准SSO单点登录表';
comment on column user_sso_info.tenant_id is '租户ID';
comment on column user_sso_info.tenant_code is '租户CODE';
comment on column user_sso_info.appid is '租户appid';
comment on column user_sso_info.appsecret is '租户appsecert';
comment on column user_sso_info.corpid is '企业id';
comment on column user_sso_info.corpsecret is '企业secert';
comment on column user_sso_info.sso_type is '1企业号；2公众号；3第三方app；4标准SSO';
comment on column user_sso_info.auth_page_url is '租户认证的页面地址';
comment on column user_sso_info.auth_success_url is '租户认证成功的页面地址';
comment on column user_sso_info.push_msg_type is '推送消息到第三方；1推送到公众号；2推送到企业微信；3推送到第三方app';
comment on column user_sso_info.server_host is '第三方服务器地址';
comment on column user_sso_info.home_page is '认证后的首页';
comment on column user_sso_info.auth_type is '0企业微信认证；1工号认证；2公司邮箱认证；3手机号认证';