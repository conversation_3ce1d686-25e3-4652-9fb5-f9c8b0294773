package com.caidaocloud.user.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.caidaocloud.em.DeleteStatusEnum;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.repository.IUserBaseInfoRepository;
import com.caidaocloud.user.service.infrastructure.repository.mapper.UserBaseInfoMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.UserBaseInfo;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoQueryDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
public class UserBaseInfoRepositoryImpl implements IUserBaseInfoRepository {
    private static final int BATCH_SIZE = 500;
    @Autowired
    private UserBaseInfoMapper userBaseInfoMapper;
    @Autowired
    private UserBaseInfoRepositoryServiceImpl userBaseInfoRepositoryService;

    @Value("${db.integer.type:BIGINT}")
    private String dbIntegerType;

    private Long getTenantId() {
        val tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        return Long.valueOf(tenantId);
    }

    @Override
    public void insertBatch(List<UserBaseInfoDo> dataList) {
        List<UserBaseInfo> poList = ObjectConverter.convertList(dataList, UserBaseInfo.class);
//        userBaseInfoMapper.insertBatch(poList);
        userBaseInfoRepositoryService.saveBatch(poList, BATCH_SIZE);
    }

    @Override
    public Long insert(UserBaseInfoDo data) {
        UserBaseInfo userBaseInfo = ObjectConverter.convert(data, UserBaseInfo.class);
        userBaseInfo.setCreateTime(System.currentTimeMillis());
        if (userBaseInfo.getCreateBy() == null) {
            userBaseInfo.setCreateBy(0L);
        }
        userBaseInfoMapper.insert(userBaseInfo);
        return userBaseInfo.getUserId();
    }

    @Override
    public void update(UserBaseInfoDo data) {
        UserBaseInfo userBaseInfo = ObjectConverter.convert(data, UserBaseInfo.class);
        userBaseInfo.setUpdateTime(System.currentTimeMillis());
        userBaseInfoMapper.updateById(userBaseInfo);
    }

    @Override
    public int insertSelective(UserBaseInfoDo record) {
        return userBaseInfoMapper.insertSelective(ObjectConverter.convert(record, UserBaseInfo.class));
    }

    @Override
    public int updateByPrimaryKeySelective(UserBaseInfoDo record) {
        return userBaseInfoMapper.updateByPrimaryKeySelective(ObjectConverter.convert(record, UserBaseInfo.class));
    }

    @Override
    public int deleteByIds(List<Long> userIds) {
        return userBaseInfoMapper.deleteBatchIds(userIds);
    }

    @Override
    public void softDeleteByIds(List<Long> userIds) {
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setDeleted(DeleteStatusEnum.DELETED.getIndex());
        userBaseInfo.setUpdateTime(System.currentTimeMillis());
        LambdaQueryWrapper<UserBaseInfo> mQueryInfo = new QueryWrapper<UserBaseInfo>().lambda();
        mQueryInfo.in(UserBaseInfo::getUserId, userIds);
        userBaseInfoMapper.update(userBaseInfo, mQueryInfo);
    }

    @Override
    public void updateDingUserIdByEmpId(Long empId, String dingUserId) {
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setUpdateTime(System.currentTimeMillis());
        userBaseInfo.setDingUserId(dingUserId);
        LambdaQueryWrapper<UserBaseInfo> mQueryInfo = new QueryWrapper<UserBaseInfo>().lambda();
        mQueryInfo.eq(UserBaseInfo::getEmpId, empId);
        userBaseInfoMapper.update(userBaseInfo, mQueryInfo);
    }

    @Override
    public List<UserBaseInfoDo> selectListByAccountId(Long accountId) {
        LambdaQueryWrapper<UserBaseInfo> mQueryInfo = new QueryWrapper<UserBaseInfo>().lambda();
        mQueryInfo.eq(UserBaseInfo::getAccountId, accountId).ne(UserBaseInfo::getDeleted, 1);
        return ObjectConverter.convertList(userBaseInfoMapper.selectList(mQueryInfo), UserBaseInfoDo.class);
    }

    @Override
    public List<UserBaseInfoDo> selectListByAccountIds(List<Long> accountIds, boolean tenantFiltered) {
        LambdaQueryWrapper<UserBaseInfo> mQueryInfo = new QueryWrapper<UserBaseInfo>().lambda();
        mQueryInfo.in(UserBaseInfo::getAccountId, accountIds).ne(UserBaseInfo::getDeleted, 1);
        if (tenantFiltered) {
            mQueryInfo.eq(UserBaseInfo::getTenantId, getTenantId());
        }
        return ObjectConverter.convertList(userBaseInfoMapper.selectList(mQueryInfo), UserBaseInfoDo.class);
    }

    public List<UserBaseInfoDo> selectListByWorkNo(String tenantId, String workNo) {
        LambdaQueryWrapper<UserBaseInfo> mQueryInfo = new QueryWrapper<UserBaseInfo>().lambda();
        mQueryInfo.inSql(UserBaseInfo::getEmpId, "select cast(emp_id as " + dbIntegerType + ") from hrpaas.entity_hr_emp_work_info_" +
                tenantId + " where workno = '" + workNo.replaceAll("\\s", "") + "'").ne(UserBaseInfo::getDeleted, 1);
        return ObjectConverter.convertList(userBaseInfoMapper.selectList(mQueryInfo), UserBaseInfoDo.class);
    }

    @Override
    public UserBaseInfoDo selectByUserId(Long userId) {
        UserBaseInfo user = userBaseInfoMapper.selectById(userId);
        return user == null ? null : ObjectConverter.convert(user, UserBaseInfoDo.class);
    }

    @Override
    public List<UserBaseInfoDo> getByUserIds(List<Long> userIds) {
        List<UserBaseInfo> list = userBaseInfoMapper.selectBatchIds(userIds);
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : ObjectConverter.convertList(list, UserBaseInfoDo.class);
    }

    @Override
    public UserBaseInfoDo getUserInfoByAccount(Long corpId, String account) {
        LambdaQueryWrapper<UserBaseInfo> mQueryInfo = new QueryWrapper<UserBaseInfo>().lambda();
        mQueryInfo.eq(UserBaseInfo::getCorpId, corpId).eq(UserBaseInfo::getAccount, account).
                ne(UserBaseInfo::getDeleted, 1);
        List<UserBaseInfo> list = userBaseInfoMapper.selectList(mQueryInfo);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return ObjectConverter.convert(list.get(0), UserBaseInfoDo.class);
    }

    @Override
    public IPage<UserBaseInfoDo> page(String keywords, int pageNo, int pageSize, Boolean onBoarDing) {
        val wrapper = new QueryWrapper<UserBaseInfo>().lambda();
        wrapper.eq(UserBaseInfo::getTenantId, getTenantId()).eq(UserBaseInfo::getOnboarding, onBoarDing).ne(UserBaseInfo::getDeleted, 1);
        if (StringUtils.isNotEmpty(keywords)) {
            wrapper.and(it ->
                    it.like(UserBaseInfo::getAccount, keywords).or().like(UserBaseInfo::getUserName, keywords));
        }
        IPage<UserBaseInfo> page = userBaseInfoMapper.selectPage(new Page(pageNo, pageSize), wrapper);
        val list = page.getRecords().stream()
                .map(it -> FastjsonUtil.convertObject(it, UserBaseInfoDo.class))
                .collect(Collectors.toList());
        IPage<UserBaseInfoDo> result = new Page(pageNo, pageSize, page.getTotal());
        result.setRecords(list);
        return result;
    }

    @Override
    public IPage<UserBaseInfoDo> page(UserBaseInfoQueryDto dto) {

        val wrapper = new QueryWrapper<UserBaseInfo>().lambda();
        wrapper.eq(UserBaseInfo::getTenantId, getTenantId()).eq(UserBaseInfo::getOnboarding, dto.getOnboarding()).ne(UserBaseInfo::getDeleted, 1);
        if (StringUtils.isNotEmpty(dto.getKeywords())) {
            wrapper.and(it -> it.like(UserBaseInfo::getAccount, dto.getKeywords()).or().like(UserBaseInfo::getUserName, dto.getKeywords()));
        }
        if (!CollectionUtils.isEmpty(dto.getEmpIds())) {
            wrapper.in(UserBaseInfo::getEmpId, dto.getEmpIds());
        }
        if (dto.getAccountId() != null) {
            wrapper.eq(UserBaseInfo::getAccountId, dto.getAccountId());
        }
        if (!CollectionUtils.isEmpty(dto.getStatus())) {
            wrapper.in(UserBaseInfo::getStatus, dto.getStatus());
        }
        IPage<UserBaseInfo> page = userBaseInfoMapper.selectPage(new Page(dto.getPageNo(), dto.getPageSize()), wrapper);
        val list = page.getRecords().stream()
                .map(it -> FastjsonUtil.convertObject(it, UserBaseInfoDo.class))
                .collect(Collectors.toList());
        IPage<UserBaseInfoDo> result = new Page(dto.getPageNo(), dto.getPageSize(), page.getTotal());
        result.setRecords(list);
        return result;
    }

    @Override
    public Long selectUserId(UserBaseInfoDo userBaseInfoDo) {
        var queryWrapper = new QueryWrapper<UserBaseInfo>().lambda()
                .select(UserBaseInfo::getUserId)
                .eq(UserBaseInfo::getTenantId, userBaseInfoDo.getTenantId())
                .eq(UserBaseInfo::getAccount, userBaseInfoDo.getAccount())
                .ne(UserBaseInfo::getDeleted, 1);
        UserBaseInfo userBaseInfo = userBaseInfoMapper.selectOne(queryWrapper);
        return userBaseInfo == null ? null : userBaseInfo.getUserId();
    }

    @Override
    public List<UserBaseInfoDo> listByEmpIdAndOnboarding(Long empId, Boolean onboarding) {
        val wrapper = new QueryWrapper<UserBaseInfo>().lambda()
                .eq(UserBaseInfo::getEmpId, empId)
                .eq(UserBaseInfo::getDeleted, 0)
                .eq(UserBaseInfo::getTenantId, Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()))
                .eq(UserBaseInfo::getOnboarding, onboarding);
        return userBaseInfoMapper.selectList(wrapper).stream()
                .map(it -> FastjsonUtil.convertObject(it, UserBaseInfoDo.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<UserBaseInfoDo> listByEmpId(Long empId) {
        return listByEmpId(empId, Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()));
    }

    @Override
    public List<UserBaseInfoDo> listByEmpId(Long empId, Long tenantId) {
        val wrapper = new QueryWrapper<UserBaseInfo>().lambda()
                .eq(UserBaseInfo::getEmpId, empId)
                .eq(UserBaseInfo::getDeleted, 0)
                .eq(UserBaseInfo::getTenantId, tenantId);
        val result = userBaseInfoMapper.selectList(wrapper).stream()
                .map(it -> FastjsonUtil.convertObject(it, UserBaseInfoDo.class))
                .collect(Collectors.toList());
        if (Objects.isNull(result)) {
            return Lists.newArrayList();
        }
        return result;
    }

    @Override
    public List<UserBaseInfoDo> listByEmpIds(List<Long> empIds) {
        val wrapper = new QueryWrapper<UserBaseInfo>().lambda()
                .in(UserBaseInfo::getEmpId, empIds)
                .eq(UserBaseInfo::getDeleted, 0)
                .eq(UserBaseInfo::getTenantId, Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()));
        val result = userBaseInfoMapper.selectList(wrapper).stream()
                .map(it -> FastjsonUtil.convertObject(it, UserBaseInfoDo.class))
                .collect(Collectors.toList());
        if (Objects.isNull(result)) {
            return Lists.newArrayList();
        }
        return result;
    }

    @Override
    public int updateBatch(List<UserBaseInfoDo> dataList) {
        return userBaseInfoMapper.updateBatch(ObjectConverter.convertList(dataList, UserBaseInfo.class));
    }

    @Override
    public int softDeleteByEmpId(String empId) {
        UserBaseInfo userBaseInfo = new UserBaseInfo();
        userBaseInfo.setUpdateTime(System.currentTimeMillis());
        userBaseInfo.setDeleted(DeleteStatusEnum.DELETED.getIndex());
        LambdaQueryWrapper<UserBaseInfo> mQueryInfo = new QueryWrapper<UserBaseInfo>().lambda();
        mQueryInfo.eq(UserBaseInfo::getEmpId, Long.valueOf(empId));
        mQueryInfo.in(UserBaseInfo::getTenantId, Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()));
        return userBaseInfoMapper.update(userBaseInfo, mQueryInfo);
    }
}
