package com.caidaocloud.user.service.domain.entity;

import com.caidaocloud.user.service.domain.enums.PrivateEnvironmentType;
import com.caidaocloud.user.service.domain.repository.IPrivateEnvironmentRepository;
import com.caidaocloud.util.SpringUtil;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Data
public class PrivateEnvironment extends BaseEntity {

    private Long id;

    private Long tenantId;

    private String tenantName;

    private String tenantCode;

    private String url;

    private String matchedEmail;

    private PrivateEnvironmentType type;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private static IPrivateEnvironmentRepository repository;

    private static IPrivateEnvironmentRepository repository(){
        if(repository == null){
            synchronized (PrivateEnvironment.class){
                if(repository == null){
                    repository = SpringUtil.getBean(IPrivateEnvironmentRepository.class);
                }
            }
        }
        return repository;
    }

    public static List<PrivateEnvironment> list(PrivateEnvironmentType type){
        return repository().list(type);
    }
}
