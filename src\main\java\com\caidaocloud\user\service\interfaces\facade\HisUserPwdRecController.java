package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.user.service.application.enums.DataOpEnum;
import com.caidaocloud.user.service.application.service.HisUserPwdRecService;
import com.caidaocloud.user.service.domain.entity.HisUserPwdRecDo;
import com.caidaocloud.user.service.interfaces.dto.SyncHisUserPwdRecDto;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/user/hispwd/v2")
@Api(value = "/api/user/hispwd/v2", tags = "用户中心2.0-用户历史密码")
public class HisUserPwdRecController {
    @Autowired
    private HisUserPwdRecService hisUserPwdRecService;

    @PostMapping("/sync")
    @ApiOperation("同步保存密码修改记录")
    public Result<Boolean> syncPwdrule(@RequestBody SyncHisUserPwdRecDto dto) {
        try {
            PreCheck.preCheckArgument(dto == null, "Data is empty");
            PreCheck.preCheckArgument(dto.getRecId() == null, "主键为空");
            PreCheck.preCheckArgument(!(dto.getOp() == DataOpEnum.INSERT || dto.getOp() == DataOpEnum.UPDATE), "Op Field value can only be [INSERT UPDATE]");
            hisUserPwdRecService.syncSave(new ArrayList<>(Arrays.asList(ObjectConverter.convert(dto, HisUserPwdRecDo.class))));
        } catch (Exception e) {
            log.error("HisUserPwdRecController.syncPwdrule error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }

    @PostMapping("/batchSync")
    @ApiOperation("批量同步保存密码修改记录")
    public Result<Boolean> batchSyncPwdrule(@RequestBody List<SyncHisUserPwdRecDto> dtos) {
        try {
            PreCheck.preCheckArgument(CollectionUtils.isEmpty(dtos), "Data is empty");
            long nullCount = dtos.stream().filter(o -> o.getRecId() == null).count();
            PreCheck.preCheckArgument(nullCount > 0, "主键为空");
            long count = dtos.stream().filter(dto -> !(dto.getOp() == DataOpEnum.INSERT || dto.getOp() == DataOpEnum.UPDATE)).count();
            PreCheck.preCheckArgument(count > 0, "Op Field value can only be [INSERT UPDATE]");
            hisUserPwdRecService.syncSave(ObjectConverter.convertList(dtos, HisUserPwdRecDo.class));
        } catch (Exception e) {
            log.error("PwdRuleController.batchSyncPwdrule error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }

}
