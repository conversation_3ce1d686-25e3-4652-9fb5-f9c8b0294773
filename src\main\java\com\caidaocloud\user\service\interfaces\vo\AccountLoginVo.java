package com.caidaocloud.user.service.interfaces.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class AccountLoginVo {
    @ApiModelProperty("租户是否已确定")
    private Boolean tenantFocused;
    @ApiModelProperty("租户确定场景下用户ID")
    private Long userId;
    @ApiModelProperty("账号ID")
    private Long accountId;
    @ApiModelProperty("用户统一注册账号")
    private String account;
    @ApiModelProperty("账号状态：1 正常 2 停用 3 锁定")
    private Integer status;
    @ApiModelProperty("注册手机号")
    private String mobNum;
    @ApiModelProperty("注册邮箱")
    private String email;
    @ApiModelProperty("租户确定场景下token")
    private String token;
    @ApiModelProperty("租户确定场景下用户名")
    private String userName;
    @ApiModelProperty("用户头像")
    private String headPortrait;
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty("租户不确定场景下的租户列表")
    private List<UserTenantBaseInfoVo> tenantList;
    @ApiModelProperty("租户不确定场景下二次访问回执")
    private String receipt;
    @ApiModelProperty("是否强制修改密码: true 是 false 否")
    private Boolean changePassword = false;
    @ApiModelProperty("密码是否过期: true 是 false 否")
    private Boolean passwordInvalid = false;

}
