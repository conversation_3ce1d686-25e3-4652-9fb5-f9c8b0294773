package com.caidaocloud.user.service.application.service.sso;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.dto.MxySsoConfigDto;
import com.caidaocloud.user.service.application.utils.mxySso.MxyEncryptException;
import com.caidaocloud.user.service.application.utils.mxySso.MxyEncryptor;
import com.caidaocloud.user.service.application.utils.UrlParamsUtil;
import com.caidaocloud.user.service.domain.entity.MxySsoConfigDo;
import com.caidaocloud.user.service.domain.service.MxySsoConfigDomainService;
import com.caidaocloud.user.service.infrastructure.util.UserContext;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.Map;
import java.util.Random;

/**
 * 魔学院单点登陆
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MxySsoService {
    /**
     * 默认登陆地址
     */
    private static String DEFAULT_LOGIN_URL = "https://study.moxueyuan.com/new/login";

    @Resource
    private MxySsoConfigDomainService mxySsoConfigDomainService;

    public String getMxySsoLink(String platform) {
        UserInfo userInfo = UserContext.preCheckUser();
        if (userInfo.getStaffId() == null) {
            log.warn("Please bind the account to an employee, account[{}]", userInfo.getAccount());
            return DEFAULT_LOGIN_URL;
        }
//        String corpId = "mxym2ex7l4c4wzl6ovk";
//        String CorpSecret = "5pYOrs3x6pMhr8HkwSvFaP9dO2rIGfBl1GozY1E3m4Yrllnh7T9KL3bBEvScTKGG";
//        String token = "qQj9EGJXrv";
//        String key = "JBupkhEq9f7CzVKiTIVzKI5Xd1InL2pCNDkItjXgJw0";

        MxySsoConfigDo mxySsoConfigDo = mxySsoConfigDomainService.getById(userInfo.getTenantId());
        if (mxySsoConfigDo == null) {
            log.warn("dont get MxySsoConfig,Please set MxySsoConfig tenantId[{}]", userInfo.getTenantId());
            return DEFAULT_LOGIN_URL;
        }

        MxySsoConfigDto mxySsoConfig = new MxySsoConfigDto();
        mxySsoConfig.setCorpId(mxySsoConfigDo.getCorpId());
        mxySsoConfig.setToken(mxySsoConfigDo.getToken());
        mxySsoConfig.setMxyKey(mxySsoConfigDo.getMxyKey());
        mxySsoConfig.setCorpSecret(mxySsoConfigDo.getCorpSecret());

        log.info("MxySsoConfig：{}", FastjsonUtil.toJson(mxySsoConfig));

        String redirectUrl = platform.equals("pc") ? mxySsoConfig.getPcUrl() : mxySsoConfig.getMbUrl();
        long timestamp = System.currentTimeMillis();
        int nonce = new Random().nextInt(Integer.MAX_VALUE);
        Map<String, String> jsonMap = null;
        try {
            // 同步信息会把 empId 作为 userId 同步至魔学院
            Map<String, Object> userExtMap = Maps.newHashMap();
            userExtMap.put("userid", userInfo.getStaffId());
            final String plainText = JSON.toJSONString(userExtMap);
            MxyEncryptor mxyEncryptor = new MxyEncryptor(mxySsoConfig.getToken(), mxySsoConfig.getMxyKey(),
                    mxySsoConfig.getCorpId(), 0);
            jsonMap = mxyEncryptor.getEncryptedMap(plainText, timestamp, nonce + "");
        } catch (MxyEncryptException e) {
            log.error("获取魔学院加密参数错误:{}", e.getMessage(), e);
            return DEFAULT_LOGIN_URL;
        }
        if (jsonMap != null) {
            try {
                Map<String, String> params = Maps.newHashMap();
                params.put("scope", mxySsoConfig.getScope());
                params.put("corpid", mxySsoConfig.getCorpId());
                params.put("timestamp", timestamp + "");
                params.put("nonce", nonce + "");
                params.put("sign", jsonMap.get("msg_signature"));
                params.put("ticket", URLEncoder.encode(jsonMap.get("encrypt")));
                params.put("redirect_uri", URLEncoder.encode(redirectUrl));
                String ssoUrl = UrlParamsUtil.buildUrl(mxySsoConfig.getHostUrl(), params, false);
                log.info("mxy sso login url: {}", ssoUrl);
                return ssoUrl;
            } catch (Exception e) {
                log.error("魔学院单点登陆参数构建链接失败:{}", e.getMessage(), e);
                return DEFAULT_LOGIN_URL;
            }
        } else {
            log.error("mxySSOLoginErrJsonMapIsNull");
            return DEFAULT_LOGIN_URL;
        }
    }
}
