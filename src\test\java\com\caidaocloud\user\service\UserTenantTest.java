package com.caidaocloud.user.service;

import com.caidaocloud.user.service.domain.entity.UserTenant;
import com.caidaocloud.user.service.domain.repository.IUserTenantRepository;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/8/2021 1:58 PM
 * 4
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class UserTenantTest {
    @Autowired
    private IUserTenantRepository userTenantRepository;

    @Test
    public void save_test() {
        List<UserTenant> list = new ArrayList<>();
        UserTenant userTenant = new UserTenant();
        userTenant.setUserId(1);
        userTenant.setUserAccount("max");
        userTenant.setTenantId("5ff7fd9d83a5837cc0f30c38");
        userTenant.setCreatedTime(System.currentTimeMillis());
        list.add(userTenant);
        userTenantRepository.save(list);
    }

    @Test
    public void get_by_user_id_or_account_test() {
        UserTenant userTenant = userTenantRepository.getByUserIdOrAccount(0, "max");
        Assert.assertNotNull(userTenant);
    }
}
