package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * created by: FoAng
 * create time: 28/8/2023 2:27 下午
 */
@Service
public class OpenCloudLoginService implements IAccountLoginService {

    @Autowired
    private UserBaseInfoService userBaseInfoService;

    @Override
    public GrantType getGrantType() {
        return GrantType.OPEN_CLOUD_GRANT_TYPE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        final String account = loginDto.getAccount();
        final String thirdId = loginDto.getThirdId();
        PreCheck.preCheckArgument(StringUtils.isEmpty(account) || StringUtils.isEmpty(thirdId),
                "登录失败，请求参数错误");
        try{
            SecurityUserInfo userInfo = new SecurityUserInfo();
            userInfo.setTenantId(thirdId);
            SecurityUserUtil.setSecurityUserInfo(userInfo);
            List<UserBaseInfoDo> userBaseInfoDos = userBaseInfoService.getUserListByAccount(account);
            return CollectionUtils.isEmpty(userBaseInfoDos) ? null : userBaseInfoDos.stream().filter(it -> String.valueOf(it.getTenantId())
                    .equals(thirdId)).collect(Collectors.toList());
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}
