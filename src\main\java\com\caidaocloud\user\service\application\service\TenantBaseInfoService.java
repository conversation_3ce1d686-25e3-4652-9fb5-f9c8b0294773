
package com.caidaocloud.user.service.application.service;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.dto.UserInfoDto;
import com.caidaocloud.user.service.application.dto.operate.CreateAccountDto;
import com.caidaocloud.user.service.application.feign.IAuthFeign;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.user.service.domain.service.TenantBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.config.thread.ThreadPoolExector;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class TenantBaseInfoService {
    private final String ADMIN_NAME = "管理员";
    private final String CONFIG_NAME = "配置管理员";

    @NacosValue(value = "${caidaocloud.import.password:111111}", autoRefreshed = true)
    private String DEFAULT_PASS;

    @Autowired
    private TenantBaseInfoDomainService tenantBaseInfoDomainService;

    @Autowired
    private AccountBaseInfoService accountBaseInfoService;

    @Autowired
    private PasswordHelper passwordHelper;

    @Autowired
    private IAuthFeign authFeign;
    @Resource
    private ThreadPoolExector threadPoolExector;

    @Transactional
    public void syncSave(List<TenantBaseInfoDo> dataList) throws Exception {
        tenantBaseInfoDomainService.syncSave(dataList);
    }

    @Transactional
    public void deleteByIds(List<Long> tenantIds) {
        tenantBaseInfoDomainService.deleteByIds(tenantIds);
    }

    @Transactional
    public void softDeleteByIds(List<Long> tenantIds) {
        tenantBaseInfoDomainService.softDeleteByIds(tenantIds);
    }

    public List<TenantBaseInfoDo> getTenantList(List<Long> tenantIds) {
        return tenantBaseInfoDomainService.getTenantList(tenantIds);
    }

    /**
     * 初始化租户信息以及admin账号、用户、角色
     * @param tenantId 租户id
     * @param tenantName 租户名称
     * @param code 租户code
     * @return admin 用户id、账号id
     */
    public void initTenant(String tenantId, String tenantName, String code) {
        // 创建租户信息
        Long tId = Long.valueOf(tenantId);
        TenantBaseInfoDo tenantBaseInfoDo = new TenantBaseInfoDo(tId, tenantName, code, tId, code);
        tenantBaseInfoDomainService.syncSave(Lists.list(tenantBaseInfoDo));


        log.info("创建admin用户、账号,tenant={}", tenantBaseInfoDo);
        createAdminAccount(tenantId, code);

        log.info("创建配置管理员,tenant={}", tenantBaseInfoDo);
        createConfigAccount(tenantId,code);
    }

    private void createConfigAccount(String tenantId, String code) {
        AccountBaseInfoDo account = createAccount(code,(key)->String.format("%s-%s", key,"config"));
        CreateAccountDto accountDto = new CreateAccountDto();
        accountDto.setName(CONFIG_NAME);
        accountDto.setCorpid(Long.valueOf(tenantId));
        accountDto.setAccount(account);
        accountDto.setTenantId(tenantId);
        UserInfoDto userInfo = accountBaseInfoService.createAccount(accountDto);
        authFeign.initConfig(tenantId, String.valueOf(userInfo.getUserId()));
    }

    private void createAdminAccount(String tenantId, String code) {
        AccountBaseInfoDo account = createAccount(code,(key)->String.format("%s-%s", key,"admin"));
        CreateAccountDto accountDto = new CreateAccountDto();
        accountDto.setName(ADMIN_NAME);
        accountDto.setCorpid(Long.valueOf(tenantId));
        accountDto.setAccount(account);
        accountDto.setTenantId(tenantId);

        // 创建账号
        UserInfoDto userInfo = accountBaseInfoService.createAccount(accountDto);

        // 初始化admin角色
        authFeign.initAdmin(tenantId, String.valueOf(userInfo.getUserId()));
    }

    private AccountBaseInfoDo createAccount(String code, AccountFormatter accountFormatter) {
        String adminAccount = accountFormatter.format(code);
        String salt = passwordHelper.createSalt();
        String password = passwordHelper.encode(DEFAULT_PASS, salt);
        AccountBaseInfoDo account = new AccountBaseInfoDo();
        account.setAccount(adminAccount);
        account.setAccountLoginPrefix(String.format("%s-", code));
        account.setPassword(password);
        account.setSalt(salt);
        account.setStatus(1);
        account.setCreateBy(0L);
        account.setCreateTime(System.currentTimeMillis());

        AccountBaseInfoDo accountInfo = accountBaseInfoService.getAccountInfo(adminAccount);
        if (accountInfo != null) {
            account.setAccountId(accountInfo.getAccountId());
        }
        return account;
    }

    @Transactional
    public void initAll() {
        List<TenantBaseInfoDo> allTenant = tenantBaseInfoDomainService.loadAllTenant();
        for (TenantBaseInfoDo tenantBaseInfoDo : allTenant) {
            threadPoolExector.getThreadPool().execute(()->{
                try {
                    SecurityUserInfo userInfo = new SecurityUserInfo();
                    userInfo.setTenantId(String.valueOf(tenantBaseInfoDo.getTenantId()));
                    userInfo.setEmpId(0L);
                    userInfo.setUserId(0L);
                    SecurityUserUtil.setSecurityUserInfo(userInfo);
                    initTenant(String.valueOf(tenantBaseInfoDo.getTenantId()), tenantBaseInfoDo.getTenantName(), tenantBaseInfoDo.getTenantCode());

                }finally {
                    SecurityUserUtil.removeSecurityUserInfo();
                }
            });
        }
    }

    @FunctionalInterface
    private interface AccountFormatter{
        String format(String code);
    }
}
