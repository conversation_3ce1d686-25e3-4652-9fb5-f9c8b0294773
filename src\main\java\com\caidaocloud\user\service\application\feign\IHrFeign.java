package com.caidaocloud.user.service.application.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.user.service.application.feign.fallback.HrFeignFallback;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * created by: FoAng
 * create time: 8/12/2022 3:51 下午
 */
@FeignClient(
        value = "caidaocloud-hr-service",
        fallback = HrFeignFallback.class,
        configuration = FeignConfiguration.class,
        contextId = "hr-service"
)
public interface IHrFeign {

    String PRE_FIX = "/api/hr/emp/work/v1";

    @GetMapping(PRE_FIX + "/getEmpByExt")
    Result<?> getEmpIdByUserName(@RequestParam(name = "userName") String userName);
}
