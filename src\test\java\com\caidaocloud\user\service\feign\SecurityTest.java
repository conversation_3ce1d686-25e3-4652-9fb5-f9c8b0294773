package com.caidaocloud.user.service.feign;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.user.service.Application;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@ActiveProfiles("local")
@SpringBootTest(classes = Application.class)
public class SecurityTest {
    @Autowired
    private IMenuClient menuClient;

    @Test
    public void test(){
        KeywordBasePageDto dto = new KeywordBasePageDto();
        Result result = menuClient.getMenuList(dto);
        log.info(JSON.toJSONString(result));
    }
}
