package com.caidaocloud.user.service.application.utils;

import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.util.FastjsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 处理导入进度条
 *
 * <AUTHOR>
 * @date 2022/5/27
 **/
@Component
public class OperateProcessBarUtil {


    private static RedisTemplate<String, Object> redisTemplate;

    @Resource(name = "redisTemplate")
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        OperateProcessBarUtil.redisTemplate = redisTemplate;
    }

    /**
     * 进度条处理
     *
     * @param processKey
     * @param processDto
     */
    public static void handler(String processKey, ImportExcelProcessDto processDto) {
        Object execute = redisTemplate.execute(new SessionCallback<Object>() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                operations.watch(processKey);
                operations.multi();
                Object obj = operations.opsForValue().get(processKey);
                ImportExcelProcessDto cacheDto = null;
                if (obj != null) {
                    String str = obj.toString().replaceAll("\"\\{", "{").replaceAll("}\"", "}").replaceAll("\\\\", "");
                    cacheDto = FastjsonUtil.toObject(str, ImportExcelProcessDto.class);
                }
                if (cacheDto != null) {
                    int completed = processDto.getCompleted() + cacheDto.getCompleted();
                    int successCount = processDto.getSuccessCount() + cacheDto.getSuccessCount();
                    int failCount = processDto.getFailCount() + cacheDto.getFailCount();
                    processDto.setCompleted(completed);
                    processDto.setSuccessCount(successCount);
                    processDto.setFailCount(failCount);
                }
                ValueOperations<String, Object> operate = operations.opsForValue();
                String json = FastjsonUtil.toJson(processDto);
                System.out.println(json);
                operate.set(processKey, json, 300, TimeUnit.SECONDS);
                return operations.exec();
            }
        });
    }

}
