package com.caidaocloud.user.service.domain.util;

import io.netty.util.internal.StringUtil;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 密码生成工具类
 */
@Component
public class PassGenerateHelp {

    /**
     * 数字密码
     */
    public static final int TYPE_NUMBER = 0x01;
    /**
     * 大写字母
     */
    public static final int TYPE_UPPER = 0x02;
    /**
     * 小写字母
     */
    public static final int TYPE_LOWER = 0x04;
    /**
     * 特殊字符
     */
    public static final int TYPE_OTHER = 0x08;
    /**
     * 手机后六位
     */
    public static final int TYPE_MOBILE = 0x10;
    /**
     * 身份证后六位
     */
    public static final int TYPE_CARD = 0x20;

    /**
     * 初始默认密码长度
     */
    private static final int MIN_PWD_LENGTH = 6;

    private static final String CHAR_LOWER = "abcdefghijklmnopqrstuvwxyz";

    private static final String CHAR_UPPER = CHAR_LOWER.toUpperCase();

    private static final String NUMBER = "0123456789";

    private static final String OTHER_CHAR = "!@#$%&*()_+-=[]?";

    private static final String PASSWORD_ALLOW_BASE = CHAR_LOWER + CHAR_UPPER + NUMBER + OTHER_CHAR;

    private static final String PASSWORD_ALLOW_BASE_SHUFFLE = shuffleString(PASSWORD_ALLOW_BASE);

    private static SecureRandom random = new SecureRandom();

    private static String shuffleString(String string) {
        List<String> letters = Arrays.asList(string.split(""));
        Collections.shuffle(letters);
        return String.join("", letters);
    }

    /**
     * 默认初始密码生成规则
     *
     * @param type
     * @param length
     * @param mobile
     * @param cardNo
     * @return
     */
    public static String generatePasswordByRule(int type, int length, String mobile, String cardNo) {
        if (length >= MIN_PWD_LENGTH) {
            if ((type & TYPE_MOBILE) == TYPE_MOBILE && !StringUtil.isNullOrEmpty(mobile)) {
                // 初始密码设置为手机号
                final int mobileLength = mobile.length();
                return mobile.substring(mobileLength - 6, mobileLength);
            } else if ((type & TYPE_CARD) == TYPE_CARD && !StringUtil.isNullOrEmpty(cardNo)) {
                final int cardLength = cardNo.length();
                return cardNo.substring(cardLength - 6, cardLength);
            } else {
                StringBuilder mBuilder = new StringBuilder();
                StringBuilder mDefaultAllow = new StringBuilder();
                if ((type & TYPE_NUMBER) == TYPE_NUMBER) {
                    mBuilder.append(NUMBER.charAt(random.nextInt(NUMBER.length())));
                    mDefaultAllow.append(NUMBER);
                }
                if ((type & TYPE_UPPER) == TYPE_UPPER) {
                    mBuilder.append(CHAR_UPPER.charAt(random.nextInt(CHAR_UPPER.length())));
                    mDefaultAllow.append(CHAR_UPPER);
                }

                if ((type & TYPE_LOWER) == TYPE_LOWER) {
                    mBuilder.append(CHAR_LOWER.charAt(random.nextInt(CHAR_LOWER.length())));
                    mDefaultAllow.append(CHAR_LOWER);
                }

                if ((type & TYPE_OTHER) == TYPE_OTHER) {
                    mBuilder.append(OTHER_CHAR.charAt(random.nextInt(OTHER_CHAR.length())));
                    mDefaultAllow.append(OTHER_CHAR);
                }
                final int curLength = mBuilder.length();
                /*更改密码策略，采取选中规则*/
                final String shuffleAllow = shuffleString(mDefaultAllow.toString());
                for (int loop = 0; loop < length - curLength; loop++) {
                    int rndCharAt = random.nextInt(shuffleAllow.length());
                    char rndChar = shuffleAllow.charAt(rndCharAt);
                    mBuilder.append(rndChar);
                }
                return shuffleString(mBuilder.toString());
            }
        }
        return null;
    }

}
