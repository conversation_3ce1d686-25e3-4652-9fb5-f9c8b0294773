package com.caidaocloud.user.service.infrastructure.util;


import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class UserContext {

    private final static ThreadLocal<UserInfo> USER_CONTEXT = new ThreadLocal<>();

    public static void setCurrentUser(UserInfo user) {
        USER_CONTEXT.remove();
        if (user != null) {
            USER_CONTEXT.set(user);
        }
    }

    /**
     * 获取用户信息
     * @return
     */
    public static UserInfo getCurrentUser() {
        return USER_CONTEXT.get();
    }

    /**
     * 清除用户信息
     */
    public static void remove() {
        USER_CONTEXT.remove();
    }

    public static UserInfo preCheckUser() {
        UserInfo user = getCurrentUser();
        if(null != user){
            return user;
        }

        UserInfo userInfo = null;
        try {
            userInfo = SpringUtil.getBean(ISessionService.class).getUserInfo();
        } catch (Exception e){
            log.warn("UserContext,get user by session err.");
        }

        if(null == userInfo){
            SecurityUserInfo ui = SecurityUserUtil.getSecurityUserInfo();
            if(null != ui){
                userInfo = new UserInfo();
                userInfo.setTenantId(ui.getTenantId());
                userInfo.setUserid(null != ui.getUserId() ? ui.getUserId().intValue() : null);
                userInfo.doSetUserId(ui.getUserId());
                userInfo.setStaffId(ui.getEmpId());
            }
        }

        PreCheck.preCheckArgument(checkUser(userInfo), LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));
        return userInfo;
    }

    public static String getUserId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getUserId() ? null : userInfo.getUserId().toString();
    }

    public static String getTenantId() {
        UserInfo userInfo = UserContext.preCheckUser();
        return null == userInfo || null == userInfo.getTenantId() ? null : userInfo.getTenantId();
    }

    public static boolean checkUser(UserInfo userInfo) {
        return null == userInfo || null == userInfo.getUserId();
    }

    public static String checkStaffIfPresent() {
        Long staffId = UserContext.preCheckUser().getStaffId();
        if (null == staffId) {
            return null;
        }

        return staffId.toString();
    }
}
