create table if not exists private_environment
(
    id   bigint      not null,
    tenant_id   bigint      not null,
    tenant_name varchar(50) not null,
    tenant_code varchar(50),
    url         varchar(200),
    matched_email     varchar(100),
    type   varchar(50),
    create_by   bigint,
    create_time bigint,
    update_by   bigint,
    update_time bigint,
    constraint pk_private_environment_id primary key (id)
    );
comment on table private_environment is '客户环境表';
comment on column private_environment.tenant_id is '租户ID';
comment on column private_environment.tenant_name is '租户名称';
comment on column private_environment.tenant_code is '租户代码';
comment on column private_environment.url is '客户地址';
comment on column private_environment.matched_email is '匹配邮箱';
comment on column private_environment.type is '类型';