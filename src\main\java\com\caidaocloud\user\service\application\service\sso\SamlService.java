package com.caidaocloud.user.service.application.service.sso;

import cn.hutool.http.HttpUtil;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.user.service.application.service.UserAppService;
import com.caidaocloud.user.service.application.service.login.IAccountLoginService;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.util.FastjsonUtil;
import com.onelogin.saml2.Auth;
import com.onelogin.saml2.settings.Saml2Settings;
import com.onelogin.saml2.settings.SettingsBuilder;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class SamlService {

    @Resource
    private UserAppService userAppService;

    @Value("${saml.x509cert:}")
    private String myX509Cert;

    @Value("${saml.sp.x509Cert:}")
    private String spX509Cert;

    @Value("${saml.sso.url:}")
    private String singleSignOnServiceUrl;

    @Value("${saml.sso.idp.metadata:}")
    private String idpMetadata;

    @Value("${saml.sp.prefix:}")
    private String urlPrefix;

    @Value("${saml.index.page.web:}")
    private String indexWebPage;

    @Value("${saml.index.page.h5:}")
    private String indexH5Page;

    @Value("${saml.index.page.webLogin:}")
    private String indexWebLoginPage;

    @Value("${saml.index.page.h5Login:}")
    private String indexH5LoginPage;

    @Value("${saml.slo.url:}")
    private String singleLogoutServiceUrl;

    private static final String SSO_URL = "onelogin.saml2.idp.single_sign_on_service.url";

    private static final String SLO_URL = "onelogin.saml2.idp.single_logout_service.url";

    private static final String CONSUMER_URL = "onelogin.saml2.sp.assertion_consumer_service.url";

    private static final String SP_SLO_SERVICE_URL = "onelogin.saml2.sp.single_logout_service.url";

    private static final String IDP_X509_CERT = "onelogin.saml2.idp.x509cert";

    private static final String SP_X509_CERT = "onelogin.saml2.sp.x509cert";

    private static final String STRICT_SAML = "onelogin.saml2.strict";

    private static final String IDP_METADATA = "onelogin.saml2.idp.entityid";

    private static final String SP_METADATA = "onelogin.saml2.sp.entityid";

    private Saml2Settings initSetting(HttpServletRequest request, HttpServletResponse response){
        Map<String, Object> samlData = new HashMap<>();
        samlData.put(STRICT_SAML, "false");
        samlData.put(SP_METADATA, urlPrefix + "/api/user/base/v2/saml/sp/metadata");
        samlData.put(CONSUMER_URL, urlPrefix + "/api/user/base/v2/saml/sso/callback");
        samlData.put(IDP_METADATA, idpMetadata);
        samlData.put(SSO_URL, singleSignOnServiceUrl);
        samlData.put(IDP_X509_CERT, myX509Cert);
        samlData.put(SLO_URL, singleLogoutServiceUrl);
        samlData.put(SP_SLO_SERVICE_URL, urlPrefix + "/api/user/base/v2/saml/sso/logout");
        samlData.put(SP_X509_CERT, spX509Cert);
        SettingsBuilder builder = new SettingsBuilder();
        Saml2Settings settings = builder.fromValues(samlData).build();
        return settings;
    }

    public void getSpMetadata(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Saml2Settings settings = initSetting(request, response);
        String metadata = settings.getSPMetadata();
        response.getOutputStream().println(metadata);
    }

    @SneakyThrows
    public void samlLogout(HttpServletRequest request, HttpServletResponse response) {
        Saml2Settings settings = initSetting(request, response);
        Auth auth = new Auth(settings, request, response);
        auth.logout();
        //auth.login(relayState=returnUrl)
    }

    public void logoutCallback(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Saml2Settings settings = initSetting(request, response);
        Auth auth = new Auth(settings, request, response);
        auth.processSLO();
        List<String> errors = auth.getErrors();
        if (errors.isEmpty()) {
            userAppService.logout();
        } else {
            log.info("logout error: {}", FastjsonUtil.toJson(errors));
            throw new ServerException("logout failed");
        }
    }


    public void samlLogin(HttpServletRequest request, HttpServletResponse response, String type) throws Exception {
        Saml2Settings settings = initSetting(request, response);
        Auth auth = new Auth(settings, request, response);
        auth.login("&type=" + type);
    }

    public void callback(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Saml2Settings settings = initSetting(request, response);
        Auth auth = new Auth(settings, request, response);
        auth.processResponse();
        String responseXml = auth.getLastResponseXML();
        log.info("saml response:" + responseXml);
        String relayState = request.getParameter("RelayState");
        if(auth.isAuthenticated() && auth.getErrors().isEmpty()) {
            log.info("saml attributes:" + FastjsonUtil.toJson(auth.getAttributes()));
            AccountLoginDto loginDto = new AccountLoginDto();
            loginDto.setSamlProps(auth.getAttributes());
            IAccountLoginService samlService = IAccountLoginService.getInstance(GrantType.SAML_TOKEN_GRANT_TYPE);
            loginDto.setEnforceSecondLogin(true);
            val result = samlService.grant(loginDto);
            Map<String, String> paramsMap = HttpUtil.decodeParamMap(relayState, StandardCharsets.UTF_8);
            boolean webType = paramsMap.isEmpty() || paramsMap.getOrDefault("type", "web").equals("web");
            if (!result.getTenantList().isEmpty()) {
                String tenantId = String.valueOf(result.getTenantList().get(0).getTenantId());
                LogRecordContext.putVariable("tenantId", tenantId);
                String redirectUrl = (webType ? indexWebPage : indexH5Page) + "?receipt=" + result.getReceipt() + "&tenantId=" + tenantId;
                log.info("send redirect url:{}", redirectUrl);
                response.sendRedirect(redirectUrl);
            } else {
                log.info("get login account error, redirect to loginPage");
                response.sendRedirect(webType ? indexWebLoginPage : indexH5LoginPage);
            }
        }else{
            throw new ServerException("login failed");
        }
    }
}
