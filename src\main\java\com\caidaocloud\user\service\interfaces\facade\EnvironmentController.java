package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.user.service.application.service.PrivateEnvironmentService;
import com.caidaocloud.user.service.domain.entity.PrivateEnvironment;
import com.caidaocloud.user.service.domain.enums.PrivateEnvironmentType;
import com.caidaocloud.user.service.interfaces.vo.PrivateEnvironmentVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/environment/v1")
public class EnvironmentController {

    @Autowired
    private PrivateEnvironmentService privateEnvironmentService;

    @ApiOperation(value = "获取所有租户私有环境", produces = "application/json")
    @RequestMapping(value = "/private", method = RequestMethod.GET)
    public Result<List<PrivateEnvironmentVo>> privateList(@RequestParam(value = "type") PrivateEnvironmentType type){
        List<PrivateEnvironment> list = privateEnvironmentService.list(type);
        List<PrivateEnvironmentVo> result = FastjsonUtil.convertList(list, PrivateEnvironmentVo.class);
        return Result.ok(result);
    }

}
