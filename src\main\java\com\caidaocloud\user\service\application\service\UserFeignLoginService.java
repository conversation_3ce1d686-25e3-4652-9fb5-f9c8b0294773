package com.caidaocloud.user.service.application.service;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.dto.LoginConfigDto;
import com.caidaocloud.user.service.domain.entity.User;
import com.caidaocloud.user.service.infrastructure.util.LRUCache;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Service("iLoginService:feign")
public class UserFeignLoginService implements ILoginService{
    private final LRUCache<String, Object> BEAN_CACHE = new LRUCache<>(50);

    @Autowired
    private User user;

    @Override
    public User getUser() {
        return user;
    }

    @Override
    public LoginOutDto doLogin(LoginConfigDto loginConfigDto, LoginDto loginDto, User dbUser){
        LoginOutDto loginOutDo = new LoginOutDto();
        loginOutDo.setErrorCode(ErrorCodes.UNKNOW_ERROR);

        if(StringUtil.isEmpty(loginConfigDto.getServerName())){
            return loginOutDo;
        }

        log.info("doConfigReplace parser ============= {}", JSON.toJSONString(loginConfigDto));
        doConfigReplace(loginConfigDto, loginDto, dbUser);

        IAutoLoginFegin loginFegin = buildClient(loginConfigDto.getServerName(), IAutoLoginFegin.class);
        if(null == loginFegin){
            return loginOutDo;
        }

        String data = "";
        if("POSTJSON".equals(loginConfigDto.getReqMethod().toUpperCase())){
            data = loginFegin.postJson(loginConfigDto.getLoginUrl(), loginConfigDto.getHeaders(), loginConfigDto.getParams());
            /*UserDto userDto = new UserDto();
            userDto.setGlobalid("90033");
            userDto.setThirdId("90033:TencentHR");
            userDto.setEmpid(526311);
            userDto.setBelongOrgId(59994);
            userDto.setCorpid(11651);
            userDto.setEmpname("testHello");
            userDto.setThirdPart("TENCENT");
            userDto.setUserid(35144);
            userDto.setTenantId("TencentHR");
            data = FastjsonUtil.toJson(Result.ok(userDto));*/
        }

        log.info("doLogin IAutoLoginFegin response data = {}", data);
        Map dataMap = null;
        if(StringUtil.isNotEmpty(data) && data.indexOf(loginConfigDto.getSuccessMark()) < 0){
            if(StringUtil.isEmpty(loginConfigDto.getErrorMark())){
                loginOutDo.setErrorCode(ErrorCodes.INVALIDED_USER_OR_PASSWORD);
                return loginOutDo;
            }

            dataMap = JSON.parseObject(data, Map.class);
            String errMark = doErrorMark(dataMap, loginConfigDto.getErrorMark());
            if(StringUtil.isEmpty(errMark)){
                return loginOutDo;
            }

            throw new ServerException(errMark);
        } else {
            dataMap = JSON.parseObject(data, Map.class);
        }

        if(null == dataMap || null == dataMap.get("data")){
            log.error("IAutoLoginFegin response data err, The returned data cannot be empty");
            loginOutDo.setErrorCode(ErrorCodes.UN_AUTHORIZED);
            return loginOutDo;
        }

        if(StringUtil.isNotEmpty(loginConfigDto.getAfterExec())){
            doAfterExec(loginConfigDto.getAfterExec(), dataMap, dbUser);
        }
        log.info("login after user = {}", dbUser);

        loginOutDo.setErrorCode(ErrorCodes.NO_ERROR);
        return loginOutDo;
    }

    @Override
    public LoginOutDto login(User user, LoginDto loginDto) {
        return null;
    }

    private <T> T buildClient(String serverName, Class<T> targetClass) {
        T t = (T) BEAN_CACHE.getCache().get(serverName);
        if (Objects.isNull(t)) {
            synchronized (this){
                if (Objects.isNull(t)) {
                    FeignClientBuilder.Builder<T> builder = new FeignClientBuilder(SpringUtil.getContext()).forType(targetClass, serverName);
                    t = builder.build();
                    BEAN_CACHE.getCache().put(serverName, t);
                }
            }
        }
        return t;
    }

    public interface IAutoLoginFegin{
        @PostMapping("{postUrl}")
        String postJson(@PathVariable("postUrl") String postUrl, @RequestHeader Map headMap, @RequestBody Map map);

        //@PostMapping(value = "{postUrl}", consumes = {"application/x-www-form-urlencoded"})
        //String postForm(@PathVariable("postUrl") String postUrl, @HeaderMap Map headMap, Map map);

        @GetMapping("{getUrl}")
        String getUrl(@PathVariable("getUrl") String getUrl, @RequestHeader Map headMap, @RequestParam Map<String,Object> map);
    }
}
