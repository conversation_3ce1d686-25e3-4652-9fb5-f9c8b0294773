package com.caidaocloud.user.service;

import com.caidaocloud.user.service.infrastructure.repository.mongo.MongodbDao;
import com.caidaocloud.user.service.infrastructure.repository.po.UserPo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2020-01-13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class SysUserTest {

    @Autowired
    private MongodbDao<UserPo> mongodbDao;

    @Test
    public void saveTest(){
        UserPo userPo = new UserPo();
        userPo.setTenantId("1").setStaffid("1")
            .setEmail("1").setMobnum("1").setEmpid(1)
                .setEmpname("1").setUserid(1);
        mongodbDao.save(userPo, "basic_e_user_" + userPo.getTenantId());
    }

}
