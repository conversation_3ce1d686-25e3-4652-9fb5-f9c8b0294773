package com.caidaocloud.user.service.application.service;

import com.caidaocloud.user.service.domain.entity.HisUserPwdRecDo;
import com.caidaocloud.user.service.domain.service.HisUserPwdRecDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class HisUserPwdRecService {
    @Autowired
    private HisUserPwdRecDomainService hisUserPwdRecDomainService;

    @Transactional
    public void syncSave(List<HisUserPwdRecDo> dataList) throws Exception {
        hisUserPwdRecDomainService.syncSave(dataList);
    }

    public List<HisUserPwdRecDo> getPageListByAccountId(Long tenantId, Long accountId, long current, long size) {
        return hisUserPwdRecDomainService.getPageListByAccountId(tenantId, accountId, current, size);
    }
}
