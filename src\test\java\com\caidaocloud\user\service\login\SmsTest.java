package com.caidaocloud.user.service.login;

import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.application.enums.SmsCodeType;
import com.caidaocloud.user.service.application.foreign.aliyun.service.AliyunSmsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class SmsTest {
    @Resource
    private AliyunSmsService aliyunSmsService;
    @Test
    public void sendCodeTest(){
        // String mobile, String code, String smsCode, SmsCodeType smsCodeType
        aliyunSmsService.sendMessageCode("15121095842", "+86", "123456", SmsCodeType.LOGIN);
    }
}
