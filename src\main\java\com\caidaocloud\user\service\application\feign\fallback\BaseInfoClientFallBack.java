package com.caidaocloud.user.service.application.feign.fallback;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.user.service.application.dto.SysEmpInfoDto;
import com.caidaocloud.user.service.application.feign.IBaseInfoClient;
import com.caidaocloud.user.service.interfaces.dto.SimpleEmpDto;
import com.caidaocloud.user.service.interfaces.dto.UserDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BaseInfoClientFallBack implements IBaseInfoClient {
    @Override
    public Result<Integer> saveBaseInfoUserSave(UserDto userDto) {
        return null;
    }

    @Override
    public Result<SimpleEmpDto> getBaseInfoSimpleEmp(String thirdEmpId) {
        return null;
    }

    @Override
    public Result<UserInfo> saveMasterDataUser(UserDto userDto) {
        return Result.fail("saveMasterDataUser Client FallBack");
    }

    @Override
    public Result<SimpleEmpDto> getMasterDataEmp(String thirdId, String thirdPart) {
        return null;
    }

    @Override
    public Result<List<SysEmpInfoDto>> getEmpInfoByEmpIds(String empIds) {
        return Result.fail("getEmpInfoByEmpIds fail");
    }

    @Override
    public Result getEmpInfoByWorkno(String workno) {
        return null;
    }

}
