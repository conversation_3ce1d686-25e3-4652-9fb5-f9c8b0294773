package com.caidaocloud.user.service.domain.repository;

import com.caidaocloud.user.service.infrastructure.repository.mongo.MongodbDao;
import com.mongodb.BasicDBObject;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-05-27
 */
public interface IBaseRepository<T> {
    /**
     * 获取泛型的 class
     * @return Class<T>
     */
    Class<T> getPoClass();

    /**
     * 获取 mongo 表名前缀
     * @return
     */
    String getCollectionNamePrefix();

    /**
     * 根据租户 ID 获取 mongo 表名
     * @param tenantId
     * @return
     */
    default String getCollectionName(String tenantId) {
        return getCollectionNamePrefix() + tenantId;
    }

    /**
     * 获取mongo实例
     * @return
     */
    MongodbDao<T> getMongodbDao();

    /**
     * 获取 List<T>
     * @param query
     * @param tenantId
     * @return
     */
    default List<T> query(BasicDBObject query, String tenantId) {
        return getMongodbDao().query(query, this.getCollectionName(tenantId), getPoClass());
    }

    /**
     * 获取 list 中的第一个
     * @param entities
     * @return
     */
    default T getOneByList(List<T> entities){
        if(null == entities || entities.isEmpty()){
            return null;
        }

        return entities.get(0);
    }

    /**
     * 批量保存
     * @param list
     * @param tenantId
     */
    default void save(List<T> list, String tenantId){
        getMongodbDao().save(list, getCollectionName(tenantId));
    }
}
