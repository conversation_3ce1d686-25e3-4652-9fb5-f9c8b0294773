package com.caidaocloud.user.service.domain.service;

import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.AccountPayslipInfoDo;
import com.caidaocloud.user.service.domain.enums.AccountType;
import com.caidaocloud.user.service.domain.repository.IAccountPayslipInfoRepository;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.infrastructure.util.RegexUtil;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AccountPayslipInfoDomainService {
    @Autowired
    private AccountPayslipInfoDo accountPayslipInfoDo;

    @Resource
    private IAccountPayslipInfoRepository iAccountPayslipInfoRepository;

    @Resource
    private PasswordHelper passwordHelper;

    public List<AccountPayslipInfoDo> getAndCheckAccountList(String account, GrantType grantType) {
        List<AccountPayslipInfoDo> accountList = getAccountInfoList(account);
        if (CollectionUtils.isEmpty(accountList)) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ACCOUNT_NOT_EXIST));
        }

        return accountList;
    }

    public List<AccountPayslipInfoDo> getAndCheckNormalAccountList(String account, GrantType grantType) {
        List<AccountPayslipInfoDo> accountList = getAccountInfoList(account);
        if (CollectionUtils.isEmpty(accountList)) {
            if (grantType == GrantType.MOBILE_CODE_TOKEN_GRANT_TYPE) {
                // 手机号+验证码登录
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.MOBILE_NUMBER_NOT_REGISTERED));
            } else if (grantType == GrantType.ONE_CLICK_LOGIN_TYPE || grantType == GrantType.LOCAL_NUMBER_VERIFICATION_TYPE) {
                // 一键登录、本机号码验证
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.MOBILE_NUMBER_NOT_REGISTERED));
            } else {
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ACCOUNT_NOT_EXIST));
            }
        }
        // 过滤掉停用、锁定的账号
        List<AccountPayslipInfoDo> normalAccountList = accountList.stream().
                filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).collect(Collectors.toList());
        PreCheck.preCheckArgument(CollectionUtils.isEmpty(normalAccountList), LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));

        return normalAccountList;
    }

    public List<AccountPayslipInfoDo> getAccountInfoList(String account) {
        return accountPayslipInfoDo.getList(account, AccountType.ACCOUNT);
    }

    public AccountPayslipInfoDo getAccountInfo(String account) {
        List<AccountPayslipInfoDo> list = getAccountInfoList(account);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    public Long syncSave(AccountPayslipInfoDo data) {
        return accountPayslipInfoDo.syncSave(data);
    }

    public Long syncInsert(AccountPayslipInfoDo data) {
        return accountPayslipInfoDo.syncInsert(data);
    }

    public List<AccountPayslipInfoDo> getListByIds(List<Long> accountIds) {
        return accountPayslipInfoDo.getListByIds(accountIds);
    }

    public AccountPayslipInfoDo getById(Long accountId) {
        return accountPayslipInfoDo.getById(accountId);
    }

    public void softDeleteByIds(List<Long> accountIds) {
        accountPayslipInfoDo.softDeleteByIds(accountIds);
    }

    public List<AccountPayslipInfoDo> getAccount(String mobNum, String email) {
        return iAccountPayslipInfoRepository.selectAccountByMobNumAndEmail(mobNum, email);
    }

    public AccountPayslipInfoDo getAccountByAccountId(Long accountId) {
        return iAccountPayslipInfoRepository.getAccountByAccountId(accountId);
    }

    public void updateById(AccountPayslipInfoDo data) {
        accountPayslipInfoDo.updateById(data);
    }

    public void updateByPrimaryKey(AccountPayslipInfoDo data) {
        accountPayslipInfoDo.updateByPrimaryKey(data);
    }

    public void syncSave(List<AccountPayslipInfoDo> dataList) throws Exception {
        accountPayslipInfoDo.syncSave(dataList);
    }

    public void syncUpdate(List<AccountPayslipInfoDo> dataList) throws Exception {
        accountPayslipInfoDo.syncUpdate(dataList);
    }

    public List<AccountPayslipInfoDo> getAccountByMobNumOrEmail(List<String> mobNums,List<String> emails) {
        return iAccountPayslipInfoRepository.getAccountByMobNumOrEmail(mobNums,emails);
    }

    public List<AccountPayslipInfoDo> getAccountByMobNums(List<String> mobNums) {
        return iAccountPayslipInfoRepository.getAccountByMobNums(mobNums);
    }

    public void changePassword(String password, Long userId, AccountPayslipInfoDo accountPayslipInfoDo) {
        checkPassword(password);
        // 修改密码
        accountPayslipInfoDo.setSalt(passwordHelper.createSalt());
        accountPayslipInfoDo.setPassword(passwordHelper.encode(password, accountPayslipInfoDo.getSalt()));
        accountPayslipInfoDo.setUpdateBy(userId);
        accountPayslipInfoDo.setUpdateTime(System.currentTimeMillis());
        updateById(accountPayslipInfoDo);
    }

    public void changeGesture(String gesture, Long userId, AccountPayslipInfoDo accountPayslipInfoDo) {
        // 修改密码
        accountPayslipInfoDo.setGesture(gesture);
        accountPayslipInfoDo.setUpdateBy(userId);
        accountPayslipInfoDo.setUpdateTime(System.currentTimeMillis());
        updateById(accountPayslipInfoDo);
    }

    private void checkPassword(String password) {
        if (password.length()<6) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.password.length.short"));
        }
        if (password.length()>12){
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.password.length.long"));
        }
        if (!PasswordHelper.PasswordGenerator.validateSimplePassword(password)) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.password.valid"));
        }
    }
}
