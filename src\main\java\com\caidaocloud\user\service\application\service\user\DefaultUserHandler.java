package com.caidaocloud.user.service.application.service.user;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.user.service.application.feign.IOpenCloudClient;
import com.caidaocloud.user.service.application.service.user.annotation.UserHandlerType;
import com.caidaocloud.user.service.domain.entity.SessionDo;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.vo.AccountLoginVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@UserHandlerType(PermissionType.CAIDAO)
@Service("iUserHandler::def")
public class DefaultUserHandler implements IUserHandler{
    @NacosValue("${caidaocloud.user.sourceAuth:caidao}")
    private String sourceAuthority;
    @Resource
    private IOpenCloudClient openCloudClient;
    @Resource
    private LoginDomainService loginDomainService;

    @Override
    public void handler(AccountLoginDto loginDto, AccountLoginVo loginVo, UserBaseInfoDo user) {
        // 加载权限
        if(!PermissionType.CAIDAO.toString().equals(sourceAuthority.toUpperCase())){
            return;
        }

        Result<List<String>> result = openCloudClient.getAllMenu(loginVo.getToken());
        log.info("result={}", FastjsonUtil.toJson(result));
        List<String> menuList = null;
        if(null == result || null == (menuList = result.getData()) || menuList.isEmpty()){
            return;
        }

        SessionDo sessionData = new SessionDo();
        sessionData.setLoginPlatform(loginDto.getLoginPlatform());
        sessionData.setUserId(loginVo.getUserId());
        String tenantId = String.valueOf(user.getTenantId());
        sessionData.setTenantId(tenantId);
        sessionData.addCodeList(menuList);
        loginDomainService.sessionRefresh(sessionData);
    }
}
