package com.caidaocloud.user.service.application.feign.fallback;

import com.caidaocloud.user.service.application.feign.ICareerClient;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

@Component
public class CareerClientFallBack implements ICareerClient {
    @Override
    public Result login(LoginDto loginDto) {
        return Result.fail();
    }
}
