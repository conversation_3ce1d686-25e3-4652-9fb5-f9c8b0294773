package com.caidaocloud.user.service.application.service.user;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.service.user.annotation.UserHandlerType;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * User Handler管理器
 *
 * <AUTHOR>
 * @date 2022/5/16
 **/
@Component
@Slf4j
public class UserHandlerManger implements ApplicationContextAware {

    private static Map<Enum<PermissionType>, IUserHandler> handlerMap = Maps.newConcurrentMap();

    private DefaultListableBeanFactory factory;

    private static String permissionType;

    /**
     * caidaocloud.permission.type
     * - CAIDAO        1.5权限
     * - CAIDAOCLOUD   2.0权限
     *
     * @param permissionType
     */
    @NacosValue("${caidaocloud.user.permission:}")
    public void setPermissionType(String permissionType) {
        UserHandlerManger.permissionType = permissionType;
    }

    @PostConstruct
    public void init() {
        var subTypesOf =
                new Reflections("com.caidaocloud.user").getSubTypesOf(IUserHandler.class);
        if (!subTypesOf.isEmpty()) {
            subTypesOf.stream().forEach(e -> {
                var userHandlerType = e.getAnnotation(UserHandlerType.class);
                if (userHandlerType == null) {
                    throw new ServerException("not found UserHandlerType annotation in IUserHandler class");
                }
                var service = e.getAnnotation(Service.class);
                String beanName = null;
                if (service != null) {
                    beanName = service.value();
                }
                if (StringUtils.isBlank(beanName)) {
                    char[] chars = e.getSimpleName().toCharArray();
                    chars[0] += 32;
                    beanName = String.valueOf(chars);
                }
                var userHandler = factory.getBean(beanName, IUserHandler.class);
                if (userHandler == null) {
                    throw new ServerException(String.format("not found bean in factory, bean name is %s", beanName));
                }
                var key = userHandlerType.value();
                handlerMap.put(key, userHandler);
            });
        }
    }

    public static IUserHandler getDefaultUserHandler() {
        if (StringUtils.isNotBlank(permissionType)) {
            return handlerMap.get(PermissionType.valueOf(permissionType.toUpperCase()));
        }
        return handlerMap.get(PermissionType.CAIDAO);
    }

    public static IUserHandler getUserHandler(PermissionType permissionType) {
        if (permissionType == null) {
            throw new ServerException("paramter is null");
        }
        var iUserHandler = handlerMap.get(permissionType);
        if (iUserHandler == null) {
            throw new ServerException(String.format("not found implemention of IUserHandler, type is %s", permissionType));
        }
        return iUserHandler;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        var configurableApplicationContext = (ConfigurableApplicationContext) applicationContext;
        this.factory = (DefaultListableBeanFactory) configurableApplicationContext.getBeanFactory();
    }

}
