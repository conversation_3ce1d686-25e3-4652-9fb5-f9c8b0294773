
package com.caidaocloud.user.service.domain.service;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.dto.MxySsoConfigDto;
import com.caidaocloud.user.service.domain.entity.MxySsoConfigDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.repository.IUserBaseInfoRepository;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MxySsoConfigDomainService {
    @Resource
    private MxySsoConfigDo mxySsoConfigDo;

    public MxySsoConfigDo getById(String tenantId) {
        return mxySsoConfigDo.getMxySsoConfigById(tenantId);
    }

}
