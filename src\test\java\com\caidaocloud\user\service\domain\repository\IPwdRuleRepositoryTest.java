package com.caidaocloud.user.service.domain.repository;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.domain.entity.PwdRuleDo;
import com.caidaocloud.user.service.infrastructure.repository.impl.PwdRuleRepositoryImpl;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import com.googlecode.totallylazy.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.credential.PasswordService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = Application.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class IPwdRuleRepositoryTest {

    @Autowired
    private IPwdRuleRepository pwdRuleRepositoryMock;

    @Before
    public void setUp() throws Exception {
        SecurityUserInfo userInfo = new SecurityUserInfo();
        userInfo.setTenantId("8");
        userInfo.setUserId(0L);
        userInfo.setEmpId(0L);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        RequestHelper.getRequest().setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(userInfo, UserInfo.class));
        SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
        try {
            Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
            field.setAccessible(true);
            field.set(service, true);
        }
        catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);


    @Autowired
    private PwdRuleDo pwdRuleDo;

    @SneakyThrows
    @Test
    public void testInsertBatch() {
        // Create some test data
        PwdRuleDo pwdRule1 = createPwdRule();
        PwdRuleDo pwdRule2 = createPwdRule();
        List<PwdRuleDo> pwdRules = Lists.list(pwdRule1, pwdRule2);

        // Call the service method
        pwdRuleDo.syncSave(pwdRules);
        PwdRuleDo pwdRule3 = createPwdRule();
        pwdRules.add(pwdRule3);
        pwdRuleRepositoryMock.insertSelective(pwdRule3);
        pwdRule3.setPwdComplexity("test complexity");
        pwdRuleRepositoryMock.updateByPrimaryKeySelective(pwdRule3);

        // Call the getPwdRuleListByTenantIds API
        List<PwdRuleDo> pwdRuleList = pwdRuleRepositoryMock.getPwdRuleListByTenantIds(
                Collections.singletonList(Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId())));

        // Verify that the returned data matches the saved parameters
        for (int i = 0; i < pwdRules.size(); i++) {
            PwdRuleDo pwdRule = pwdRules.get(i);
            PwdRuleDo pwdRuleFromDb = pwdRuleList.get(i);
            assertEquals(pwdRule.getPwdRuleId(), pwdRuleFromDb.getPwdRuleId());
            assertEquals(pwdRule.getTenantId(), pwdRuleFromDb.getTenantId());
            assertEquals(pwdRule.getIsFirstRule(), pwdRuleFromDb.getIsFirstRule());
            assertEquals(pwdRule.getFirstRule(), pwdRuleFromDb.getFirstRule());
            assertEquals(pwdRule.getFirstLength(), pwdRuleFromDb.getFirstLength());
            assertEquals(pwdRule.getIsValid(), pwdRuleFromDb.getIsValid());
            assertEquals(pwdRule.getIsPwdChange(), pwdRuleFromDb.getIsPwdChange());
            assertEquals(pwdRule.getPwdLen1(), pwdRuleFromDb.getPwdLen1());
            assertEquals(pwdRule.getPwdLen2(), pwdRuleFromDb.getPwdLen2());
            assertEquals(pwdRule.getPwdComplexity(), pwdRuleFromDb.getPwdComplexity());
            assertEquals(pwdRule.getNotPwdSameNum(), pwdRuleFromDb.getNotPwdSameNum());
            assertEquals(pwdRule.getPwdValidTime(), pwdRuleFromDb.getPwdValidTime());
            assertEquals(pwdRule.getPwdExpiresDay(), pwdRuleFromDb.getPwdExpiresDay());
            assertEquals(pwdRule.getLockAccountNum(), pwdRuleFromDb.getLockAccountNum());
            assertEquals(pwdRule.getLockAccountTime(), pwdRuleFromDb.getLockAccountTime());
            assertEquals(pwdRule.getAutoUnlockTime(), pwdRuleFromDb.getAutoUnlockTime());
            assertEquals(pwdRule.getCorpId(), pwdRuleFromDb.getCorpId());
        }

    }

    public PwdRuleDo createPwdRule(){
        PwdRuleDo pwdRule = new PwdRuleDo();
        pwdRule.setPwdRuleId(snowflakeUtil.createId());
        pwdRule.setTenantId(Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()));
        pwdRule.setIsFirstRule(true);
        pwdRule.setFirstRule("123456");
        pwdRule.setFirstLength(6);
        pwdRule.setIsValid(true);
        pwdRule.setIsPwdChange(true);
        pwdRule.setPwdLen1(8);
        pwdRule.setPwdLen2(16);
        pwdRule.setPwdComplexity("1234");
        pwdRule.setNotPwdSameNum(5);
        pwdRule.setPwdValidTime(30);
        pwdRule.setPwdExpiresDay(3);
        pwdRule.setLockAccountNum(5);
        pwdRule.setLockAccountTime(30);
        pwdRule.setAutoUnlockTime(10);
        pwdRule.setCorpId(3L);

        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        long timestamp = System.currentTimeMillis();
        pwdRule.setCreateBy(userInfo.getUserId());
        pwdRule.setUpdateBy(userInfo.getUserId());
        pwdRule.setCreateTime(timestamp);
        pwdRule.setUpdateTime(timestamp);
        pwdRule.setDeleted(0);
        return pwdRule;
    }
}