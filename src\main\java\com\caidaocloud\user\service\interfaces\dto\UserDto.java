package com.caidaocloud.user.service.interfaces.dto;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/6/2021 11:37 AM
 * 4
 */
@Data
@Accessors(chain = true)
@ToString
public class UserDto {
    public String tenantId;
    private String staffid;
    private Integer userid;
    private Integer corpid;
    private String account;
    private String passwd;
    private String empname;
    private String mobnum;
    private String linktel;
    private String email;
    private Integer empid;
    private Boolean issuperadmin;
    private Integer status;
    private String salt;
    private Integer crtuser;
    private Long crttime;
    private Integer upduser;
    private Long updtime;
    private Integer belongOrgId;
    private String channelId;
    private Integer mobType;
    private String passremind;
    private String deadline;
    private String logintimes;
    private Long locktime;
    private String gesture;
    private String roleids;
    private Integer orgid;
    private Integer postId;
    private String photoUrl;
    private Integer clockType;

    /**
     * 第三方平台编码
     */
    private String thirdPart;
    /**
     * 第三方数据ID
     */
    private String thirdId;

    private String globalid;
}
