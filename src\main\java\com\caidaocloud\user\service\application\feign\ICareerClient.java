package com.caidaocloud.user.service.application.feign;

import com.caidaocloud.user.service.application.feign.fallback.CareerClientFallBack;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "adapter-career-service", fallback = CareerClientFallBack.class)
public interface ICareerClient {
    String API_PREFIX = "/api/career/user/v1";

    @PostMapping(value = API_PREFIX + "/login")
    Result login(@RequestBody LoginDto loginDto);
}
