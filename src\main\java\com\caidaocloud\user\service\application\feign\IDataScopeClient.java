package com.caidaocloud.user.service.application.feign;

import com.caidaocloud.user.service.application.feign.fallback.DataScopeClientFallBack;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(value = "caidaocloud-business-config-center", fallback = DataScopeClientFallBack.class)
public interface IDataScopeClient {
    String API_PREFIX = "/api/bcc/datascope";

    @GetMapping(value = API_PREFIX + "/load")
    Result datascopeLoad(@RequestHeader("Access-Token") String token);

    @GetMapping(value = "/api/bcc/menu/codeList")
    Result getCodeList(@RequestHeader("Access-Token") String token);
}
