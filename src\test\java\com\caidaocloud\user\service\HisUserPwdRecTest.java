package com.caidaocloud.user.service;


import com.caidaocloud.user.service.application.enums.DataOpEnum;
import com.caidaocloud.user.service.application.service.HisUserPwdRecService;
import com.caidaocloud.user.service.application.service.TenantBaseInfoService;
import com.caidaocloud.user.service.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.SyncTenantBaseInfoDto;
import com.caidaocloud.util.ObjectConverter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class HisUserPwdRecTest {
    @Autowired
    private HisUserPwdRecService hisUserPwdRecService;
    @Autowired
    private TenantBaseInfoService tenantBaseInfoService;

    @Test
    public void testGetPageListByAccountId() throws Exception {
//        List<HisUserPwdRecDo> list = hisUserPwdRecService.getPageListByAccountId(1L, 1L, 2, 5);
//        System.out.println(list.size());
        SyncTenantBaseInfoDto dto = new SyncTenantBaseInfoDto();
        dto.setOp(DataOpEnum.INSERT);
        dto.setCorpCode("*********");
        dto.setCorpId(1111111L);
        dto.setTenantName("11111");
        dto.setTenantId(11122222L);
        tenantBaseInfoService.syncSave(new ArrayList<>(Arrays.asList(ObjectConverter.convert(dto, TenantBaseInfoDo.class))));

        System.out.println(111);
    }
}
