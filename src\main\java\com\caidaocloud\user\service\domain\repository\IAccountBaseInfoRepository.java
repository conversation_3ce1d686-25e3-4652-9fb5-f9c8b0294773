package com.caidaocloud.user.service.domain.repository;

import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.enums.AccountType;

import java.util.List;

public interface IAccountBaseInfoRepository {
    List<AccountBaseInfoDo> selectList(String account, AccountType accountType);

    void insertBatch(List<AccountBaseInfoDo> dataList);

    void insert(AccountBaseInfoDo data);

    void update(AccountBaseInfoDo data);

    int insertSelective(AccountBaseInfoDo record);

    int updateByPrimaryKeySelective(AccountBaseInfoDo record);

    void deleteByIds(List<Long> accountIds);

    void softDeleteByIds(List<Long> accountIds);

    List<AccountBaseInfoDo> getListByIds(List<Long> accountIds);

    AccountBaseInfoDo getById(Long accountId);

    List<AccountBaseInfoDo> selectAccountByMobNumAndEmail(String mobNum, String email);

    List<AccountBaseInfoDo> getAccountByMobNumAndEmail(String mobNum, String email);

    AccountBaseInfoDo getAccountByAccountId(Long accountId);

    List<AccountBaseInfoDo> getAccountByMobNumOrEmail(List<String> mobNums, List<String> emails);

    int updateBatch(List<AccountBaseInfoDo> list);

    List<AccountBaseInfoDo> getAccountByMobNums(List<String> mobNums);
}
