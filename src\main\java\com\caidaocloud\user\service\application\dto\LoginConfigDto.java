package com.caidaocloud.user.service.application.dto;

import lombok.Data;

import java.util.Map;

@Data
public class LoginConfigDto {
    /**
     * loginType 登录类型 ：http，暂时只支持 http 调用
     */
    private String loginType;

    /**
     * 登录 url
     */
    private String loginUrl;

    /**
     * 服务名字
     */
    private String serverName;

    /**
     * 登录方法,POSTJSON,POSTFORM,GET
     */
    private String reqMethod;

    /**
     * 请求头参数
     */
    private Map headers;

    /**
     * 请求参数
     */
    private Map params;

    /**
     * 前置处理 el 表达式代码调用
     */
    private String beforeExec;

    /**
     * 后置处理 el 表达式代码调用
     */
    private String afterExec;

    /**
     * 异常处理 el 表达式代码调用
     */
    private String errorExec;

    /**
     * 判断请求成功的标志
     */
    private String successMark;

    /**
     * 判断请求返回错误的标志
     */
    private String errorMark;
}
