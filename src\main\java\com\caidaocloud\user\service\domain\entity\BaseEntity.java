package com.caidaocloud.user.service.domain.entity;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Data
public class BaseEntity extends AbstractBaseEntity {
    public static void setDefValueOfRequiredField(List<? extends AbstractBaseEntity> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        for (AbstractBaseEntity data : dataList) {
            setDefValueOfRequiredField(data);
        }
    }

    public static void setDefValueOfRequiredField(AbstractBaseEntity data) {
        if (data.getUpdateBy() == null) {
            data.setUpdateBy(0L);
        }
        if (data.getUpdateTime() == null) {
            data.setUpdateTime(System.currentTimeMillis());
        }
        if (data.getCreateBy() == null) {
            data.setCreateBy(data.getUpdateBy());
        }
        if (data.getCreateTime() == null) {
            data.setCreateTime(data.getUpdateTime());
        }
        if (data.getDeleted() == null) {
            data.setDeleted(0);
        }
    }

}
