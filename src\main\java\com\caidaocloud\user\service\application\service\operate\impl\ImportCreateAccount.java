package com.caidaocloud.user.service.application.service.operate.impl;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.dto.UserInfoDto;
import com.caidaocloud.user.service.application.dto.operate.CreateAccountDto;
import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.user.service.application.exception.ImportException;
import com.caidaocloud.user.service.application.service.AccountBaseInfoService;
import com.caidaocloud.user.service.application.service.operate.AbstarctImportOperationLink;
import com.caidaocloud.user.service.application.utils.MessgeToCacheUtil;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.application.utils.PropertyUtil;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 创建账号
 *
 * <AUTHOR>
 * @date 2022/5/20
 **/
@Slf4j
public class ImportCreateAccount extends AbstarctImportOperationLink {

    private AccountBaseInfoService accountBaseInfoService;

    private PasswordHelper passwordHelper;
    
    private MsgNoticeService msgNoticeService;
    
    private CacheService cacheService;

    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    public ImportCreateAccount(String processId) {
        super(processId);
        this.accountBaseInfoService = SpringUtil.getBean(AccountBaseInfoService.class);
        this.passwordHelper = SpringUtil.getBean(PasswordHelper.class);
        this.msgNoticeService = SpringUtil.getBean(MsgNoticeService.class);
        this.cacheService = SpringUtil.getBean(CacheService.class);
        this.accountBaseInfoDomainService = SpringUtil.getBean(AccountBaseInfoDomainService.class);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operate(UserImportDto importUser, ImportExcelProcessDto processDto, UserImportErrorDto error) {
        try {
            createAccount(importUser, error);
        } catch (Exception e) {
            if(e instanceof ImportException){
                throw e;
            }else{
                log.error("导入账号异常", e);
                String msg = LangUtil.getMsg(MsgCodeConstant.UNKNOWN_EXCEPTION);
                error.setErrorMsg(msg);
                throw new ImportException(msg);
            }
        }
        if (next() != null) {
            next().operate(importUser, processDto, error);
        }
    }

    private void createAccount(UserImportDto importUser, UserImportErrorDto error) {
        String pwd = importUser.getPassword();
        String defaultImportPassword = StringUtil.isNoneBlank(pwd) ? pwd : PasswordHelper.PasswordGenerator.generatePassword(6);
        log.info("createAccountImportUser: {}", FastjsonUtil.toJson(importUser));
        log.info("importUser.getName(): " + importUser.getName() + " defaultImportPassword: " + defaultImportPassword);
        CreateAccountDto accountDto = new CreateAccountDto();
        accountDto.setName(importUser.getName());
        accountDto.setEmpId(importUser.getEmpId());
        accountDto.setCorpid(importUser.getCorpid());
        if (importUser.getAccountId() == null) {
            String salt = passwordHelper.createSalt();
            String password = passwordHelper.encode(defaultImportPassword, salt);
            AccountBaseInfoDo account = createAccount(importUser, password, salt, System.currentTimeMillis());
            accountDto.setAccount(account);
        } else {
            if(StringUtil.isNotBlank(pwd)){
                AccountBaseInfoDo account = accountBaseInfoDomainService.getById(importUser.getAccountId());
                account.setPassword(passwordHelper.encode(importUser.getPassword(),  account.getSalt()));
                accountBaseInfoDomainService.updateById(account);
            }
            accountDto.setAccountId(importUser.getAccountId());
        }
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        String tenantId = securityUserInfo.getTenantId();
        accountDto.setTenantId(tenantId);
        UserInfoDto userInfoDto = accountBaseInfoService.createAccount(accountDto);
        importUser.setAccountId(userInfoDto.getAccountId());
        importUser.setUserId(userInfoDto.getUserId());
        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("empId", String.valueOf(accountDto.getEmpId()));
        extMap.put("onboarding.account.password", defaultImportPassword);
        extMap.put("onboarding.account", userInfoDto.getAccount());
        List<String> subjects = Lists.newArrayList(String.valueOf(accountDto.getEmpId()));
        msgNoticeService.sendMsgNoticeEvent(NoticeType.EMPLOYEE_ACCOUNT_CREATE, subjects, extMap, "user", 0);
        String key = String.format("change_pwd_account:%s", userInfoDto.getAccountId());
        cacheService.cacheObject(key, 1L, -1L);
    }

    private AccountBaseInfoDo createAccount(UserImportDto userImportDto, String password, String salt, long currentTimeMillis) {
        AccountBaseInfoDo account = new AccountBaseInfoDo();
        account.setAccount(userImportDto.getMobile());
        account.setMobNum(userImportDto.getMobile());
        account.setEmail(userImportDto.getEmail());
        account.setPassword(password);
        account.setSalt(salt);
        account.setStatus(1);
        account.setCreateBy(0L);
        account.setCreateTime(currentTimeMillis);
        return account;
    }

}
