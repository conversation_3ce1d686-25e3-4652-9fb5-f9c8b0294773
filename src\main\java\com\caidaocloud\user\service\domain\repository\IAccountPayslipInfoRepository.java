package com.caidaocloud.user.service.domain.repository;

import com.caidaocloud.user.service.domain.entity.AccountPayslipInfoDo;
import com.caidaocloud.user.service.domain.enums.AccountType;

import java.util.List;

public interface IAccountPayslipInfoRepository {
    List<AccountPayslipInfoDo> selectList(String account, AccountType accountType);

    void insertBatch(List<AccountPayslipInfoDo> dataList);

    void insert(AccountPayslipInfoDo data);

    void update(AccountPayslipInfoDo data);

    int insertSelective(AccountPayslipInfoDo record);

    int updateByPrimaryKeySelective(AccountPayslipInfoDo record);

    void deleteByIds(List<Long> accountIds);

    void softDeleteByIds(List<Long> accountIds);

    List<AccountPayslipInfoDo> getListByIds(List<Long> accountIds);

    AccountPayslipInfoDo getById(Long accountId);

    List<AccountPayslipInfoDo> selectAccountByMobNumAndEmail(String mobNum, String email);

    List<AccountPayslipInfoDo> getAccountByMobNumAndEmail(String mobNum, String email);

    AccountPayslipInfoDo getAccountByAccountId(Long accountId);

    List<AccountPayslipInfoDo> getAccountByMobNumOrEmail(List<String> mobNums, List<String> emails);

    int updateBatch(List<AccountPayslipInfoDo> list);

    List<AccountPayslipInfoDo> getAccountByMobNums(List<String> mobNums);
}
