package com.caidaocloud.user.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.user.service.domain.entity.AbstractBaseEntity;
import lombok.Data;

@Data
@TableName("account_base_info")
public class AccountBaseInfo extends AbstractBaseEntity {
    /**
     * 账号ID
     */
    @TableId(type = IdType.INPUT)
    private Long accountId;
    /**
     * 使用用户统一注册账号登录时的前缀，例如：caidao-，登录时输入：前缀-账号进行登录
     */
    private String accountLoginPrefix;
    /**
     * 用户统一注册账号
     */
    private String account;
    /**
     * 注册手机号
     */
    private String mobNum;
    /**
     * 注册邮箱
     */
    private String email;
    /**
     * 密码
     */
    private String password;
    /**
     * 盐值
     */
    private String salt;
    /**
     * 手势密码
     */
    private String gesture;
    /**
     * 账号状态：1 正常 2 停用 3 锁定
     */
    private Integer status;
    /**
     * 注册方式
     */
    private String regType;
}
