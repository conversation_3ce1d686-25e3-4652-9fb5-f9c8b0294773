package com.caidaocloud.user.service.application.foreign.aliyun.service;


import com.aliyun.dypnsapi20170525.models.*;
import com.caidaocloud.user.service.application.foreign.aliyun.enums.VerifyResultEnum;
import com.caidaocloud.user.service.application.foreign.aliyun.util.AliyunApiHelper;
import com.caidaocloud.user.service.application.foreign.service.IForeignLoginService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AliyunLoginService implements IForeignLoginService {
    @Value("${aliyun.api.pnvs.accessKeyId:}")
    private String accessKeyId;

    @Value("${aliyun.api.pnvs.accessSecret:}")
    private String accessSecret;

    /**
     * 完成一键登录取号
     *
     * @param accessToken
     * @return
     */
    @Override
    public Result<String> getMobile(String accessToken) {
        try {
            com.aliyun.dypnsapi20170525.Client client = AliyunApiHelper.createClient(accessKeyId, accessSecret);
            GetMobileRequest getMobileRequest = new GetMobileRequest();
            getMobileRequest.setAccessToken(accessToken);
            GetMobileResponse resp = client.getMobile(getMobileRequest);
            GetMobileResponseBody responseBody = resp.getBody();
            log.info("GetMobileResponseBody:" + FastjsonUtil.toJson(responseBody));
            if (!"OK".equals(responseBody.getCode())) {
                return Result.fail(responseBody.getMessage());
            }
            GetMobileResponseBody.GetMobileResponseBodyGetMobileResultDTO resultDTO = responseBody.getGetMobileResultDTO();
            return Result.ok(resultDTO.getMobile());
        } catch (Exception e) {
            log.error("AliyunApiService.getMobile error msg:{}", e.getMessage(), e);
            return Result.fail("完成一键登录取号失败");
        }
    }

    /**
     * 完成本机号码校验认证
     *
     * @param accessCode
     * @param phoneNumber
     * @return
     */
    @Override
    public Result<Boolean> verifyMobile(String accessCode, String phoneNumber) {
        try {
            com.aliyun.dypnsapi20170525.Client client = AliyunApiHelper.createClient(accessKeyId, accessSecret);
            VerifyMobileRequest verifyMobileRequest = new VerifyMobileRequest();
            verifyMobileRequest.setAccessCode(accessCode);
            verifyMobileRequest.setPhoneNumber(phoneNumber);
            VerifyMobileResponse resp = client.verifyMobile(verifyMobileRequest);
            VerifyMobileResponseBody responseBody = resp.getBody();
            log.info("VerifyMobileResponseBody:" + FastjsonUtil.toJson(responseBody));
            if (!"OK".equals(responseBody.getCode())) {
                return Result.fail(responseBody.getMessage());
            }
            VerifyMobileResponseBody.VerifyMobileResponseBodyGateVerifyResultDTO resultDTO = responseBody.getGateVerifyResultDTO();
            if (VerifyResultEnum.PASS.toString().equals(resultDTO.verifyResult)) {
                return Result.ok(true);
            }
            return Result.fail("本机号码校验认证失败");
        } catch (Exception e) {
            log.error("AliyunApiService.verifyMobile error msg:{}", e.getMessage(), e);
            return Result.fail("完成本机号码校验认证");
        }
    }
}
