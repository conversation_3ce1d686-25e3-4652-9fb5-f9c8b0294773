package com.caidaocloud.user.service.domain.entity;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.domain.repository.ITenantBaseInfoRepository;
import com.caidaocloud.user.service.domain.util.ListUtil;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@Data
@NoArgsConstructor
public class TenantBaseInfoDo extends BaseEntity {
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 租户代码
     */
    private String tenantCode;
    /**
     * 租户logo
     */
    private String logo;
    /**
     * 集团公司ID
     */
    private Long corpId;
    /**
     * 集团公司唯一编码
     */
    private String corpCode;
    /**
     * 状态： 0 无效 1 正常 2 封存
     */
    private Integer status;

    @Autowired
    private ITenantBaseInfoRepository tenantBaseInfoRepository;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    public TenantBaseInfoDo(Long tenantId, String tenantName, String tenantCode, Long corpId, String corpCode) {
        this.tenantId = tenantId;
        this.tenantName = tenantName;
        this.tenantCode = tenantCode;
        this.corpId = corpId;
        this.corpCode = corpCode;
    }

    /**
     * 同步接入租户数据
     *
     * @param dataList
     * @throws Exception
     */
    @Transactional
    public void syncSave(List<TenantBaseInfoDo> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        long count = dataList.stream().filter(o -> o.getTenantId() == null).count();
        if (count > 0) {
            throw new ServerException("租户ID不允许为空");
        }
        List<Long> tenantIds = dataList.stream().map(TenantBaseInfoDo::getTenantId).distinct().collect(Collectors.toList());
        // 清除老数据
        tenantBaseInfoRepository.delete(tenantIds);
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(dataList);
        // 状态判断
        dataList.forEach(o -> {
            if (null == o.getStatus()) {
                o.setStatus(1);
            }
        });
        // 数据保存
        List<List<TenantBaseInfoDo>> lists = ListUtil.split(dataList, 500);
        for (List<TenantBaseInfoDo> list : lists) {
            tenantBaseInfoRepository.insertBatch(list);
        }
    }

    public void deleteByIds(List<Long> tenantIds) {
        tenantIds = tenantIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tenantIds)) {
            return;
        }
        tenantBaseInfoRepository.delete(tenantIds);
    }

    public void softDeleteByIds(List<Long> tenantIds) {
        tenantIds = tenantIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tenantIds)) {
            return;
        }
        tenantBaseInfoRepository.softDelete(tenantIds);
    }

    public List<TenantBaseInfoDo> getTenantList(List<Long> tenantIds) {
        return tenantBaseInfoRepository.getTenantList(tenantIds);
    }

    public TenantBaseInfoDo getTenantByCode(String tenantCode) {
        return tenantBaseInfoRepository.getTenantByCode(tenantCode);
    }

    public List<TenantBaseInfoDo> getTenantListByCorpCode(String corpCode) {
        return tenantBaseInfoRepository.getTenantListByCorpCode(corpCode);
    }
}
