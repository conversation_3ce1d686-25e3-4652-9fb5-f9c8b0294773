package com.caidaocloud.user.service.application.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.caidaocloud.user.service.application.dto.LoginConfigDto;
import com.caidaocloud.user.service.domain.entity.User;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.user.service.interfaces.dto.UserDto;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface ILoginService {
    LoginOutDto login(User user, LoginDto loginDto);

    default LoginOutDto doLogin(LoginConfigDto loginConfigDto, LoginDto loginDto, User dbUser){
        return null;
    }

    default User getUser(){
        return null;
    }

    default User doAfterExec(String afterExec, Map resultMap, User dbUser){
        Map afterMap = JSON.parseObject(afterExec, Map.class);
        if(null == afterMap || afterMap.isEmpty() || null == resultMap || resultMap.isEmpty()){
            return null;
        }

        if("saveUser".equals(afterMap.get("afterExecAction")) && null != afterMap.get("afterExecData") && null == dbUser.getUserid()){
            UserDto userDto = doAfterExec(resultMap, (Map) afterMap.get("afterExecData"));
            User newUser = ObjectConverter.convert(userDto, User.class);
            if(StringUtil.isNotEmpty(dbUser.getStaffid())){
                newUser.setStaffid(dbUser.getStaffid());
            }
            List<User> saveList = new ArrayList<>();
            saveList.add(newUser);
            getUser().save(saveList, userDto.getTenantId());
            BeanUtils.copyProperties(newUser, dbUser);
        } else if("saveOrUpdateUser".equals(afterMap.get("afterExecAction"))){

        }

        return dbUser;
    }

    default UserDto doAfterExec(Map dataMap, Map<String, Object> afterExecMap){
        EvaluationContext context = new StandardEvaluationContext();
        // 为了让表达式可以访问该对象, 先把对象放到上下文中
        context.setVariable("dataMap", dataMap);

        ExpressionParser parser = new SpelExpressionParser();
        String entryVal = "";
        for (Map.Entry<String, Object> entry : afterExecMap.entrySet()) {
            if(null == entry.getValue()){
                continue;
            }

            entryVal = entry.getValue().toString();
            if(entryVal.indexOf("#") > -1){
                entry.setValue(parser.parseExpression(entryVal).getValue(context));
            }

        }

        return JSONObject.parseObject(JSON.toJSONString(afterExecMap), UserDto.class);
    }

    default void doConfigReplace(LoginConfigDto loginConfigDto, LoginDto loginDto, User dbUser){
        // 表达式的上下文
        EvaluationContext context = new StandardEvaluationContext();
        // 为了让表达式可以访问该对象, 先把对象放到上下文中
        context.setVariable("login", loginDto);
        context.setVariable("user", dbUser);
        context.setVariable("header", loginConfigDto.getHeaders());
        context.setVariable("params", loginConfigDto.getParams());

        ExpressionParser parser = new SpelExpressionParser();
        parserMap(context, parser, loginConfigDto.getHeaders());
        parserMap(context, parser, loginConfigDto.getParams());
    }

    default String doErrorMark(Map resultMap, String exp){
        // 表达式的上下文
        EvaluationContext context = new StandardEvaluationContext();
        // 为了让表达式可以访问该对象, 先把对象放到上下文中
        context.setVariable("result", resultMap);
        ExpressionParser parser = new SpelExpressionParser();
        return parser.parseExpression(exp).getValue(context, String.class);
    }

    /**
     * 解析请求中的占位符参数
     * @param context
     * @param parser
     * @param map
     */
    default void parserMap(EvaluationContext context, ExpressionParser parser, Map<String, Object> map){
        if(null == map || map.isEmpty()){
            return;
        }

        Object val = "";
        Map<String, Object> valMap = null;
        String value = "";
        for (Map.Entry<String, Object> entry : map.entrySet()){
            val = entry.getValue();
            if(null == val){
                continue;
            }

            if(val instanceof String){
                value = val.toString();
                if(value.indexOf("#") > -1){
                    val = parser.parseExpression(value).getValue(context);
                    map.put(entry.getKey(), val);
                }
                continue;
            }

            if(val instanceof Map){
                valMap = (Map<String, Object>) val;
                for (Map.Entry<String, Object> valEntry : valMap.entrySet()){
                    val = valEntry.getValue();
                    if(null == val){
                        continue;
                    }

                    value = val.toString();
                    if(value.indexOf("#") > -1){
                        valMap.put(valEntry.getKey(), parser.parseExpression(value).getValue(context, String.class));
                    }
                }
                map.put(entry.getKey(), valMap);
            }
        }
    }
}
