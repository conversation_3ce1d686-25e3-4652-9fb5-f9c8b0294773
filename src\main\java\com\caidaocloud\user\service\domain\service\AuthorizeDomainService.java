package com.caidaocloud.user.service.domain.service;

import com.caidaocloud.user.service.domain.entity.UserSsoInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class AuthorizeDomainService {
    @Resource
    private UserSsoInfo ssoUser;

    public UserSsoInfo getSsoInfoById(Long id){
        return ssoUser.getSsoInfoById(id);
    }

    public UserSsoInfo getSsoInfoByCode(String appCode){
        return ssoUser.getSsoInfoByCode(appCode);
    }
}
