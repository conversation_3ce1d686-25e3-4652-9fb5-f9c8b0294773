package com.caidaocloud.user.service.application.service.operate.impl;

import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.user.service.application.exception.ImportException;
import com.caidaocloud.user.service.application.service.operate.AbstarctImportOperationLink;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.rmi.ServerException;

/**
 * 检查是否填写必填字段
 *
 * <AUTHOR>
 * @date 2022/5/20
 **/
public class ImportCheckMustFiled extends AbstarctImportOperationLink {

    public ImportCheckMustFiled(String processId) {
        super(processId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    public void operate(UserImportDto importUser, ImportExcelProcessDto processDto, UserImportErrorDto error) {
        if(StringUtils.isEmpty(importUser.getName())||StringUtils.isEmpty(importUser.getMobile())){
            String msg = LangUtil.getMsg(MsgCodeConstant.IMPORT_MUST_FEILD_NULL);
            error.setErrorMsg(msg);
            throw new ImportException(msg);
        }
        if (next() != null) {
            next().operate(importUser, processDto, error);
        }
    }

}
