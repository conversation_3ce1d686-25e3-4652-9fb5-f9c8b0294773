package com.caidaocloud.user.service.feign;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.application.dto.LoginConfigDto;
import com.caidaocloud.user.service.domain.entity.User;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

@Slf4j
@RunWith(SpringRunner.class)
@ActiveProfiles("local")
@SpringBootTest(classes = Application.class)
public class UserFeignLoginServiceTest {
    //@Autowired
    //private UserFeignLoginService userFeignLoginService;
    public LoginConfigDto getLoginConfigDto(){
        String loginConfig = "{\"afterExec\":{\"afterExecAction\":\"saveUser\",\"afterExecData\":{\"corpid\":\"#dataMap['data']['corpid']\",\"logintimes\":\"#dataMap['data']['logintimes']\",\"globalid\":\"#dataMap['data']['globalid']\",\"mobnum\":\"#dataMap['data']['mobnum']\",\"userid\":\"#dataMap['data']['userid']\",\"gesture\":\"#dataMap['data']['gesture']\",\"photoUrl\":\"#dataMap['data']['photoUrl']\",\"roleids\":\"#dataMap['data']['roleids']\",\"crttime\":\"#dataMap['data']['crttime']\",\"crtuser\":\"#dataMap['data']['crtuser']\",\"issuperadmin\":\"#dataMap['data']['issuperadmin']\",\"deadline\":\"#dataMap['data']['deadline']\",\"staffid\":\"#dataMap['data']['staffid']\",\"email\":\"#dataMap['data']['email']\",\"channelId\":\"#dataMap['data']['channelId']\",\"empname\":\"#dataMap['data']['empname']\",\"thirdPart\":\"#dataMap['data']['thirdPart']\",\"empid\":\"#dataMap['data']['empid']\",\"salt\":\"#dataMap['data']['salt']\",\"mobType\":\"#dataMap['data']['mobType']\",\"locktime\":\"#dataMap['data']['locktime']\",\"thirdId\":\"#dataMap['data']['thirdId']\",\"belongOrgId\":\"#dataMap['data']['belongOrgId']\",\"postId\":\"#dataMap['data']['postId']\",\"clockType\":\"#dataMap['data']['clockType']\",\"orgid\":\"#dataMap['data']['orgid']\",\"linktel\":\"#dataMap['data']['linktel']\",\"passwd\":\"#dataMap['data']['passwd']\",\"tenantId\":\"#dataMap['data']['tenantId']\",\"passremind\":\"#dataMap['data']['passremind']\",\"updtime\":\"#dataMap['data']['updtime']\",\"upduser\":\"#dataMap['data']['upduser']\",\"account\":\"#dataMap['data']['account']\",\"status\":\"#dataMap['data']['status']\"}},\"headers\":{\"secret\":\"bbb\",\"key\":\"aaaaa\",\"Content-Type\":\"application/json\"},\"loginType\":\"feign\",\"loginUrl\":\"/api/adapterTencent/user/v1/auth\",\"params\":{\"thirdPart\":\"#login.thirdPart\",\"extMap\":{\"globalid\":\"#user.extMap['globalid']\",\"corpkey\":\"#user.extMap['corpkey']\",\"appkey\":\"#user.extMap['appkey']\",\"userName\":\"#user.extMap['userName']\",\"userId\":\"#user.userid\"},\"password\":\"#login.password\",\"terminal\":\"0\",\"account\":\"#login.account\"},\"reqMethod\":\"postjson\",\"serverName\":\"adapter-tencent-service\",\"successMark\":\"\\\"success\\\":true\"}";

        LoginConfigDto loginConfigDto = JSON.parseObject(loginConfig, LoginConfigDto.class);
        log.info("LoginConfigDto = {}", JSON.toJSONString(loginConfigDto));
        return loginConfigDto;
    }

    public LoginDto getLoginDto(){
        String loginConfig = "{\"account\":\"abcds\",\"password\":\"1111\",\"loginPlatform\":2,\"loginType\":4,\"deviceType\":2,\"thirdPart\":\"tencent-test3\",\"thirdId\":\"\",\"extMap\":{\"globalid\":\"21\",\"corpkey\":\"tencent-test3\",\"appkey\":\"appkey\",\"userName\":\"userName\"}}";

        LoginDto loginDto = JSON.parseObject(loginConfig, LoginDto.class);
        log.info("LoginDto = {}", JSON.toJSONString(loginDto));
        return loginDto;
    }

    public User getDbUser(){
        String user = "{\"tenantId\":\"tencent-test3\",\"staffid\":\"21\",\"userid\":35125,\"corpid\":11651,\"account\":\"t_wx_tencent-test3_21\",\"passwd\":\"f6c82ad030b7b500df7ec605f4cfd9a4\",\"empname\":\"一号人员\",\"mobnum\":\"\",\"email\":\"\",\"empid\":5,\"issuperadmin\":false,\"status\":1,\"crtuser\":0,\"crttime\":**********,\"belongOrgId\":56594,\"thirdPart\":\"TENCENT\",\"thirdId\":\"21:tencent-test3\",\"extMap\":{\"globalid\":\"21\",\"corpkey\":\"tencent-test3\",\"appkey\":\"appkey\",\"userName\":\"userName\"}}";

        User dbUser = JSON.parseObject(user, User.class);
        log.info("dbUser = {}", JSON.toJSONString(dbUser));
        return dbUser;
    }

    @Test
    public void test(){

        System.out.println("----------------");
        String str = "{\"afterExec\":{\"afterExecAction\":\"saveUser\",\"afterExecData\":{\"corpid\":\"#dataMap['data']['corpid']\",\"logintimes\":\"#dataMap['data']['logintimes']\",\"globalid\":\"#dataMap['data']['globalid']\",\"mobnum\":\"#dataMap['data']['mobnum']\",\"userid\":\"#dataMap['data']['userid']\",\"gesture\":\"#dataMap['data']['gesture']\",\"photoUrl\":\"#dataMap['data']['photoUrl']\",\"roleids\":\"#dataMap['data']['roleids']\",\"crttime\":\"#dataMap['data']['crttime']\",\"crtuser\":\"#dataMap['data']['crtuser']\",\"issuperadmin\":\"#dataMap['data']['issuperadmin']\",\"deadline\":\"#dataMap['data']['deadline']\",\"staffid\":\"#dataMap['data']['staffid']\",\"email\":\"#dataMap['data']['email']\",\"channelId\":\"#dataMap['data']['channelId']\",\"empname\":\"#dataMap['data']['empname']\",\"thirdPart\":\"#dataMap['data']['thirdPart']\",\"empid\":\"#dataMap['data']['empid']\",\"salt\":\"#dataMap['data']['salt']\",\"mobType\":\"#dataMap['data']['mobType']\",\"locktime\":\"#dataMap['data']['locktime']\",\"thirdId\":\"#dataMap['data']['thirdId']\",\"belongOrgId\":\"#dataMap['data']['belongOrgId']\",\"postId\":\"#dataMap['data']['postId']\",\"clockType\":\"#dataMap['data']['clockType']\",\"orgid\":\"#dataMap['data']['orgid']\",\"linktel\":\"#dataMap['data']['linktel']\",\"passwd\":\"#dataMap['data']['passwd']\",\"tenantId\":\"#dataMap['data']['tenantId']\",\"passremind\":\"#dataMap['data']['passremind']\",\"updtime\":\"#dataMap['data']['updtime']\",\"upduser\":\"#dataMap['data']['upduser']\",\"account\":\"#dataMap['data']['account']\",\"status\":\"#dataMap['data']['status']\"}},\"headers\":{\"secret\":\"bbb\",\"key\":\"aaaaa\",\"Content-Type\":\"application/json\"},\"loginType\":\"feign\",\"loginUrl\":\"/api/adapterTencent/user/v1/auth\",\"params\":{\"thirdPart\":\"#login.thirdPart\",\"extMap\":{\"globalid\":\"#user.extMap['globalid']\",\"corpkey\":\"#user.extMap['corpkey']\",\"appkey\":\"#user.extMap['appkey']\",\"userName\":\"#user.extMap['userName']\",\"userId\":\"#user.userid\"},\"password\":\"#login.password\",\"terminal\":\"0\",\"account\":\"#login.account\"},\"reqMethod\":\"postjson\",\"serverName\":\"adapter-tencent-service\",\"successMark\":\"\\\"success\\\":true\"}";
        System.out.println(str);
        System.out.println("----------------");

        LoginConfigDto loginConfigDto = getLoginConfigDto();

        LoginDto loginDto = getLoginDto();

        User dbUser = getDbUser();

        //userFeignLoginService.doLogin(loginConfigDto, loginDto, dbUser);
    }

    @Test
    public void testAfterExec(){
        String data = "{\"data\":{\"tenantId\":\"tencent-test3\",\"staffid\":null,\"userid\":42528,\"corpid\":11646,\"account\":\"t_wx_tencent-test3_null_21\",\"passwd\":\"f6c82ad030b7b500df7ec605f4cfd9a4\",\"empname\":\"userName\",\"mobnum\":null,\"linktel\":null,\"email\":null,\"empid\":null,\"issuperadmin\":false,\"status\":1,\"salt\":null,\"crtuser\":0,\"crttime\":**********,\"upduser\":null,\"updtime\":null,\"belongOrgId\":56566,\"channelId\":null,\"mobType\":null,\"passremind\":null,\"deadline\":null,\"logintimes\":null,\"locktime\":null,\"gesture\":null,\"roleids\":null,\"orgid\":null,\"postId\":null,\"photoUrl\":null,\"clockType\":null,\"thirdPart\":\"TENCENT\",\"thirdId\":\"_21:tencent-test3\",\"globalid\":\"21\"},\"code\":0,\"msg\":\"success\",\"serverTime\":*************,\"success\":true}";
        Map dataMap = JSON.parseObject(data, Map.class);

        EvaluationContext context = new StandardEvaluationContext();
        // 为了让表达式可以访问该对象, 先把对象放到上下文中
        context.setVariable("dataMap", dataMap);

        String afterExec = "{\"afterExecAction\":\"saveUser\",\"afterExecData\":{\"tenantId\":\"#dataMap['data']['tenantId']\",\"staffid\":\"#dataMap['data']['staffid']\",\"userid\":\"#dataMap['data']['userid']\",\"corpid\":\"#dataMap['data']['corpid']\",\"account\":\"#dataMap['data']['account']\",\"passwd\":\"#dataMap['data']['passwd']\",\"empname\":\"#dataMap['data']['empname']\",\"mobnum\":\"#dataMap['data']['mobnum']\",\"linktel\":\"#dataMap['data']['linktel']\",\"email\":\"#dataMap['data']['email']\",\"empid\":\"#dataMap['data']['empid']\",\"issuperadmin\":\"#dataMap['data']['issuperadmin']\",\"status\":\"#dataMap['data']['status']\",\"salt\":\"#dataMap['data']['salt']\",\"crtuser\":\"#dataMap['data']['crtuser']\",\"crttime\":\"#dataMap['data']['crttime']\",\"upduser\":\"#dataMap['data']['upduser']\",\"updtime\":\"#dataMap['data']['updtime']\",\"belongOrgId\":\"#dataMap['data']['belongOrgId']\",\"channelId\":\"#dataMap['data']['channelId']\",\"mobType\":\"#dataMap['data']['mobType']\",\"passremind\":\"#dataMap['data']['passremind']\",\"deadline\":\"#dataMap['data']['deadline']\",\"logintimes\":\"#dataMap['data']['logintimes']\",\"locktime\":\"#dataMap['data']['locktime']\",\"gesture\":\"#dataMap['data']['gesture']\",\"roleids\":\"#dataMap['data']['roleids']\",\"orgid\":\"#dataMap['data']['orgid']\",\"postId\":\"#dataMap['data']['postId']\",\"photoUrl\":\"#dataMap['data']['photoUrl']\",\"clockType\":\"#dataMap['data']['clockType']\",\"thirdPart\":\"#dataMap['data']['thirdPart']\",\"thirdId\":\"#dataMap['data']['thirdId']\",\"globalid\":\"#dataMap['data']['globalid']\"}}";
        Map afterExecMap = JSON.parseObject(afterExec, Map.class);
        ExpressionParser parser = new SpelExpressionParser();
        Map<String, Object> afterExecDataMap = (Map) afterExecMap.get("afterExecData");
        String entryVal = "";
        for (Map.Entry<String, Object> entry : afterExecDataMap.entrySet()) {
            System.out.println("entry -> entryKey=" + entry.getKey() + ",entryValue=" + entry.getValue());
            if(null == entry.getValue()){
                continue;
            }

            entryVal = entry.getValue().toString();
            if(entryVal.indexOf("#") > -1){
                entry.setValue(parser.parseExpression(entryVal).getValue(context));
            }

        }

        System.out.println(JSON.toJSONString(afterExecDataMap));


    }
}
