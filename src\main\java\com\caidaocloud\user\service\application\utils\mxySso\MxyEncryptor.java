package com.caidaocloud.user.service.application.utils.mxySso;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class MxyEncryptor {
    private static final Charset CHARSET = Charset.forName("utf-8");
    private static final Base64 base64 = new Base64();
    private byte[] aesKey;
    private String token;
    private String corpId;
    private static final Integer AES_ENCODE_KEY_LENGTH = 43;
    private static final Integer RANDOM_LENGTH = 16;

    public MxyEncryptor(String token, String encodingAesKey, String corpId, Integer bitType)
            throws MxyEncryptException {
        if ((encodingAesKey == null) || (encodingAesKey.length() != AES_ENCODE_KEY_LENGTH && encodingAesKey.length() != RANDOM_LENGTH)) {
            throw new MxyEncryptException(900004);
        }
        this.token = token;
        this.corpId = corpId;

        // 受java环境策略限制，可使用  请联系魔学院技术进行配置
        if (bitType == 1){
            encodingAesKey = encodingAesKey.substring(0, 16);
        }

        this.aesKey = Base64.decodeBase64(encodingAesKey + "=");
    }

    /**
     * 加密
     *
     * @param plaintext 加密参数
     * @param timeStamp 时间戳
     * @param nonce     随机数
     * @return
     * @throws MxyEncryptException
     */
    public Map<String, String> getEncryptedMap(String plaintext, Long timeStamp, String nonce)
            throws MxyEncryptException {
        if (plaintext == null) {
            throw new MxyEncryptException(900001);
        }
        if (timeStamp == null) {
            throw new MxyEncryptException(900002);
        }
        if (nonce == null) {
            throw new MxyEncryptException(900003);
        }
        String encrypt = encrypt(MxyCommon.getRandomStr(RANDOM_LENGTH), plaintext);
        String signature = getSignature(this.token, String.valueOf(timeStamp), nonce, encrypt);
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("msg_signature", signature);
        resultMap.put("encrypt", encrypt);
        resultMap.put("timeStamp", String.valueOf(timeStamp));
        resultMap.put("nonce", nonce);
        return resultMap;
    }

    /**
     * 解密
     *
     * @param msgSignature
     * @param timeStamp
     * @param nonce
     * @param encryptMsg
     * @return
     * @throws MxyEncryptException
     */
    public String getDecryptMsg(String msgSignature, String timeStamp, String nonce, String encryptMsg) throws MxyEncryptException {
        String signature = getSignature(this.token, timeStamp, nonce, encryptMsg);
        if (!signature.equals(msgSignature)) {
            throw new MxyEncryptException(900006);
        }
        return decrypt(encryptMsg);
    }

    /**
     * byte 转string类型
     *
     * @param bArr
     * @return
     */
    public static String bytesToHexString(byte[] bArr) {
        if (bArr == null) {
            return null;
        }
        StringBuilder sb = new StringBuilder(bArr.length);
        String sTmp;
        for (byte b : bArr) {
            sTmp = Integer.toHexString(0xFF & b);
            if (sTmp.length() < 2)
                sb.append(0);
            sb.append(sTmp);
        }
        return sb.toString();
    }

    /**
     * 加密方法
     *
     * @param random    随机数
     * @param plaintext 加密参数
     * @return
     * @throws MxyEncryptException
     */
    private String encrypt(String random, String plaintext) throws MxyEncryptException {
        try {
            byte[] randomBytes = random.getBytes(CHARSET);
            byte[] plainTextBytes = plaintext.getBytes(CHARSET);
            byte[] lengthByte = MxyCommon.int2Bytes(plainTextBytes.length);
            byte[] corpidBytes = this.corpId.getBytes(CHARSET);
            ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
            byteStream.write(randomBytes);
            byteStream.write(lengthByte);
            byteStream.write(plainTextBytes);
            byteStream.write(corpidBytes);
            byte[] padBytes = PKCS7Padding.getPaddingBytes(byteStream.size());
            byteStream.write(padBytes);
            byte[] unencrypted = byteStream.toByteArray();
            byteStream.close();
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");

            //不足位数补零操作
            if (this.aesKey.length < 16) {
                this.aesKey = postscape(this.aesKey);
            }

            SecretKeySpec keySpec = new SecretKeySpec(this.aesKey, "AES");
            IvParameterSpec iv = new IvParameterSpec(this.aesKey, 0, 16);
            System.out.println("iv偏移量：" + bytesToHexString(iv.getIV()));

            cipher.init(1, keySpec, iv);
            byte[] encrypted = cipher.doFinal(unencrypted);
            return base64.encodeToString(encrypted);
        } catch (Exception e) {
            e.printStackTrace();
            throw new MxyEncryptException(900007);
        }
    }


    /**
     * 不足16位的 byte数据进行补零
     *
     * @param data 原byte数据
     * @return 处理成16位byte[]后的数据
     */
    private byte[] postscape(byte[] data) {
        byte[] bytes = new byte[16];
        for (int i = 0; i < bytes.length; i++) {
            if (i < data.length) {
                bytes[i] = data[i];
            } else {
                bytes[i] = 0;
            }
        }
        return bytes;
    }


    /**
     * 解密方法
     *
     * @param text 参数
     * @return 返回
     * @throws MxyEncryptException
     */
    private String decrypt(String text) throws MxyEncryptException {
        byte[] originalArr;
        String plainText;
        String fromCorpid;
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");

            //不足位数补零操作
            if (this.aesKey.length < 16) {
                this.aesKey = postscape(this.aesKey);
            }


            SecretKeySpec keySpec = new SecretKeySpec(this.aesKey, "AES");
            IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(this.aesKey, 0, 16));
            cipher.init(2, keySpec, iv);

            byte[] encrypted = Base64.decodeBase64(text);

            originalArr = cipher.doFinal(encrypted);
        } catch (Exception e) {
            //byte[] originalArr;
            throw new MxyEncryptException(900008);
        }
        try {
            //byte[] originalArr;
            byte[] bytes = PKCS7Padding.removePaddingBytes(originalArr);

            byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);
            int plainTextLegth = MxyCommon.bytes2int(networkOrder);
            plainText = new String(Arrays.copyOfRange(bytes, 20, 20 + plainTextLegth), CHARSET);
            fromCorpid = new String(Arrays.copyOfRange(bytes, 20 + plainTextLegth, bytes.length), CHARSET);
        } catch (Exception e) {
            throw new MxyEncryptException(900009);
        }

        if (!fromCorpid.equals(this.corpId)) {
            throw new MxyEncryptException(900010);
        }
        return plainText;
    }

    /**
     * 加、解密时 byte不足两位则进行补零处理
     *
     * @param token
     * @param timestamp
     * @param nonce
     * @param encrypt
     * @return
     * @throws MxyEncryptException
     */
    public String getSignature(String token, String timestamp, String nonce, String encrypt) throws MxyEncryptException {
        try {
            String[] array = {token, timestamp, nonce, encrypt};
            Arrays.sort(array);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 4; i++) {
                sb.append(array[i]);
            }
            String str = sb.toString();
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            md.update(str.getBytes());
            byte[] digest = md.digest();

            StringBuilder hexstr = new StringBuilder();
            String shaHex;
            for (byte b : digest) {
                shaHex = Integer.toHexString(b & 0xFF);
                if (shaHex.length() < 2) {
                    hexstr.append(0);
                }
                hexstr.append(shaHex);
            }
            return hexstr.toString();
        } catch (Exception e) {
            throw new MxyEncryptException(900006);
        }
    }
}
