package com.caidaocloud.user.service;

import com.caidaocloud.user.service.application.event.publish.message.UserEventMsg;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

/**
 * 订阅使用
 * created by: FoAng
 * create time: 13/12/2022 2:29 下午
 */
@Slf4j
@Component
public class UserEventSubscribe {


    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "user.event.queue", durable = "true"),
                    exchange = @Exchange(value = "message.user.fanout.exchange",
                    type = "fanout"))
    )
    @RabbitHandler
    public void handleUserEvent(String msg) {
        UserEventMsg userEventMsg = FastjsonUtil.toObject(msg, UserEventMsg.class);
        log.info("接受到用户消息：{}", userEventMsg);
    }
}
