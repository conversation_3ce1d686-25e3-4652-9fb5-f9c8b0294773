package com.caidaocloud.user.service;

import com.caidaocloud.user.service.application.dto.operate.UserImportDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import org.apache.shiro.crypto.RandomNumberGenerator;
import org.apache.shiro.crypto.SecureRandomNumberGenerator;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

public class TestDemo {

    public static void main(String[] args) {
        String str = "15121095866+86";
        String[] split = str.split("\\+");
        System.out.println(split[0]);
        System.out.println(split[1]);

        RandomNumberGenerator randomNumberGenerator = new SecureRandomNumberGenerator();
        String s = randomNumberGenerator.nextBytes().toHex();
        System.out.println(s);

        ArrayList<Integer> integers = Lists.newArrayList(1, 2, 3, 4, 5);
        ConcurrentLinkedQueue<Integer> queue = new ConcurrentLinkedQueue(integers);
        while (true) {
            if (queue.isEmpty()) {
                break;
            }
            System.out.println(queue.poll());
        }
        System.out.println("end");
    }


}
