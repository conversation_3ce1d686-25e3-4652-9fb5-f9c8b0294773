package com.caidaocloud.user.service;

import com.caidaocloud.user.service.application.utils.PasswordHelper;
import org.junit.Assert;
import org.junit.Test;

import static org.hibernate.validator.internal.util.Contracts.assertTrue;

public class PasswordHelperTest {

    @Test(expected = IllegalArgumentException.class)
    public void generatePasswordShouldThrowExceptionForLengthLessThan6() {
        PasswordHelper.PasswordGenerator.generatePassword(5);
    }

    @Test
    public void generatePasswordShouldContainUppercaseLetter() {
        String password = PasswordHelper.PasswordGenerator.generatePassword(8);
        System.out.println(password);
        assertTrue(isUppercase(password),
                "Generated password should contain at least one uppercase letter");
    }

    @Test
    public void generatePasswordShouldContainLowercaseLetter() {
        String password = PasswordHelper.PasswordGenerator.generatePassword(8);
        System.out.println(password);
        assertTrue(isLowercase(password),
                "Generated password should contain at least one lowercase letter");
    }

    @Test
    public void generatePasswordShouldContainDigit() {
        String password = PasswordHelper.PasswordGenerator.generatePassword(8);
        System.out.println(password);
        assertTrue(isDigit(password),
                "Generated password should contain at least one digit");
    }

    @Test
    public void generatePasswordShouldContainSpecialCharacter() {
        String password = PasswordHelper.PasswordGenerator.generatePassword(8);
        System.out.println(password);
        assertTrue(isSpecialCharacter("我"),
                "Generated password should contain at least one special character");
    }

    @Test
    public void generatePasswordShouldHaveSpecifiedLength() {
        int length = 10;
        String password = PasswordHelper.PasswordGenerator.generatePassword(length);
        System.out.println(password);
        Assert.assertEquals("Generated password should have the specified length", length, password.length());
    }

    // Helper methods to check password characteristics
    private boolean isUppercase(String password) {
        return password.matches(".*[A-Z].*");
    }

    private boolean isLowercase(String password) {
        return password.matches(".*[a-z].*");
    }

    private boolean isDigit(String password) {
        return password.matches(".*\\d.*");
    }

    private boolean isSpecialCharacter(String password) {
        return password.matches(".*[^a-zA-Z0-9].*");
    }


}
