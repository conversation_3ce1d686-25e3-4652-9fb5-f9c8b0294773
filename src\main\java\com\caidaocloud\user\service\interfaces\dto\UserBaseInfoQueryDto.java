package com.caidaocloud.user.service.interfaces.dto;

import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.FilterElement;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("用户信息查询dto")
public class UserBaseInfoQueryDto extends BasePage {

    /**
     * 账号ID
     */
    private Long accountId;

    @ApiModelProperty("姓名，账号")
    private String keywords;

    @ApiModelProperty("工号")
    private String workNo;

    @ApiModelProperty("所属组织")
    private List<Long> organizes;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("员工ID")
    private List<Long> empIds;

    @ApiModelProperty("角色")
    private List<Long> roleNames;

    @ApiModelProperty("用户状态：1 正常 2 停用 3 锁定")
    private List<Integer> status;

    @ApiModelProperty("删除状态 0 未删除 1 已删除")
    private Integer deleted;

    @ApiModelProperty("是否候选人账号")
    private Boolean onboarding;

}
