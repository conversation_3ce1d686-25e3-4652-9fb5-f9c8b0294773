package com.caidaocloud.user.service.application.utils;

import com.caidaocloud.excption.ServerException;
import org.springframework.context.EmbeddedValueResolverAware;
import org.springframework.stereotype.Component;
import org.springframework.util.StringValueResolver;

/**
 * 获取property,yaml文件配置信息
 *
 * <AUTHOR>
 * @date 2022/5/20
 **/
@Component
public class PropertyUtil implements EmbeddedValueResolverAware {

    private static StringValueResolver stringValueResolver;

    @Override
    public void setEmbeddedValueResolver(StringValueResolver resolver) {
        PropertyUtil.stringValueResolver = resolver;
    }

    public static String getProperty(String key) {
        if (stringValueResolver == null) {
            throw new ServerException("no StringValueResolver");
        }
        return stringValueResolver.resolveStringValue(String.format("${%s}", key));
    }

}
