package com.caidaocloud.user.service.infrastructure.config.mongo;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.user.service.infrastructure.repository.po.TenantPo;
import com.caidaocloud.user.service.infrastructure.repository.po.UserPo;
import com.caidaocloud.user.service.infrastructure.repository.po.UserTenantPo;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoClientURI;
import com.mongodb.WriteConcern;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.Morphia;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class MorphiaAutoConfiguration {

    @NacosValue(value = "${spring.data.mongodb.uri}", autoRefreshed = true)
    private String mongoDBUrl;

    @Bean
    @Primary
    public MongoClientOptions mongoOptions() {
        return MongoClientOptions.builder().writeConcern(WriteConcern.JOURNALED).build();
    }

    @Bean
    @Qualifier(value = "morphiaContext")
    public MorphiaContext morphiaContext() {
        MongoClient mongoClient = new MongoClient(new MongoClientURI(mongoDBUrl));
        MorphiaContext ctx = new MorphiaContext();
        Morphia morphia = new Morphia();
        morphia.map(UserPo.class);
        morphia.map(TenantPo.class);
        morphia.map(UserTenantPo.class);
        Datastore datastore = morphia.createDatastore(mongoClient, "user_center");
        ctx.setMorphia(morphia);
        ctx.setDatastore(datastore);
        return ctx;
    }

}