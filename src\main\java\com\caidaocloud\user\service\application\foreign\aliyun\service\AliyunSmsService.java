package com.caidaocloud.user.service.application.foreign.aliyun.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.IAcsClient;
import com.caidaocloud.user.service.application.dto.MsgInfo;
import com.caidaocloud.user.service.application.enums.SmsCodeType;
import com.caidaocloud.user.service.application.foreign.aliyun.util.AliyunApiHelper;
import com.caidaocloud.user.service.application.foreign.service.IForeignSmsService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AliyunSmsService implements IForeignSmsService {
    @Value("${aliyun.api.sms.regionId:cn-hangzhou}")
    private String regionId;

    @Value("${aliyun.api.sms.accessKeyId:}")
    private String accessKeyId;

    @Value("${aliyun.api.sms.accessSecret:}")
    private String accessSecret;

    @Value("${aliyun.api.sms.mns.signName:}")
    private String signName;

    @Value("${aliyun.api.sms.mns.templateCode:}")
    private String templateCode;

    /**
     * 短信模版相关变量的变量名-登录验证码变量
     */
    @Value("${aliyun.api.sms.mns.templateParam.verificationCode:code}")
    private String verificationCode;

    @Override
    public String getSmsType() {
        return "aliyun";
    }

    @Override
    public boolean sendMessageCode(String mobile, String smsCode, SmsCodeType smsCodeType) {
        return sendMessageCode(mobile, "", smsCode, smsCodeType);
    }

    @Override
    public boolean sendMessageCode(String mobile, String code, String smsCode, SmsCodeType smsCodeType) {
        // 短信内容
        MsgInfo msgInfo = new MsgInfo();
        msgInfo.setPhoneNumbers(new ArrayList<>(Arrays.asList(code + mobile)));
        msgInfo.setTemplateCode(templateCode);
        // 短信模版相关变量 paramMap
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(verificationCode, smsCode);
        msgInfo.setTemplateParams(paramMap);
        // 发送短信
        return this.doSendMessage(msgInfo);
    }

    /**
     * 发送短信
     *
     * @param msgInfo
     * @return
     */
    private boolean doSendMessage(MsgInfo msgInfo) {
        if (StringUtil.isEmpty(accessKeyId) || StringUtil.isEmpty(accessSecret)) {
            log.error("发送短信失败，请检查短信发送配置参数");
            return false;
        }
        try {
            IAcsClient client = AliyunApiHelper.getClient(regionId, accessKeyId, accessSecret);
            final String phoneNumbers = String.join(",", msgInfo.getPhoneNumbers());
            final String params = JSON.toJSONString(msgInfo.getTemplateParams());
            // todo 模版变量，需要替换
            CommonRequest request = AliyunApiHelper.getSmsRequest();
            request.putQueryParameter("PhoneNumbers", phoneNumbers);
            request.putQueryParameter("SignName", StringUtil.isNotBlank(msgInfo.getSignName()) ? msgInfo.getSignName() : signName);
            request.putQueryParameter("TemplateCode", msgInfo.getTemplateCode());
            request.putQueryParameter("TemplateParam", params);
            CommonResponse response = client.getCommonResponse(request);
            JSONObject result = JSON.parseObject(response.getData());
            log.info("sendSmsCode={}, params={}, sendSmsResult={}, queryParameters={}",
                    phoneNumbers, params, response.getData(), FastjsonUtil.toJson(request.getSysQueryParameters()));
            return result.containsKey("Code") && result.get("Code").equals("OK");
        } catch (Exception e) {
            log.error("doSendMessage:{}", e.getMessage());
            return false;
        }
    }
}
