package com.caidaocloud.user.service.application.feign.fallback;

import com.caidaocloud.user.service.application.dto.DingResponse;
import com.caidaocloud.user.service.application.dto.DingUserInfoResponse;
import com.caidaocloud.user.service.application.feign.DingTalkFeignClient;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/2/1 下午6:20
 * @Version 1.0
 **/
@Component
public class DingTalkFeignClientFallback implements DingTalkFeignClient {



    @Override
    public Map<String, String> getSsoToken(String appKey, String appSecret) {
        return null;
    }


    @Override
    public DingResponse getUserId(String code, String accessToken) {
        return null;
    }

    @Override
    public DingUserInfoResponse getUserInfo(String userid, String accessToken) {
        return null;
    }
}

 
    
    
    
    