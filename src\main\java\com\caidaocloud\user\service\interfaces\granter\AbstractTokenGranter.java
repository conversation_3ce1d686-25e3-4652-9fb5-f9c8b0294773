package com.caidaocloud.user.service.interfaces.granter;

import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.user.service.application.feign.IDataScopeClient;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.user.service.interfaces.vo.LoginVo;
import com.caidaocloud.web.RequestHelper;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2020-01-15
 */
@Slf4j
public abstract class AbstractTokenGranter implements ITokenGranter {
    @Autowired
    private IDataScopeClient dataScopeClient;

    protected Result beforeGrant(LoginDto loginDto){
        if (StringUtils.isEmpty(loginDto.getAccount()) || StringUtils.isEmpty(loginDto.getPassword())) {
            return Result.status(null, ErrorCodes.EMPTY_ACCOUNT_OR_PASSWORD, MessageHandler.getExceptionMessage(ErrorCodes.EMPTY_ACCOUNT_OR_PASSWORD, RequestHelper.getRequest()));
        }

        return null;
    }

    protected Result afterGrant(LoginOutDto loginOutDo){
        if (loginOutDo.getErrorCode() != ErrorCodes.NO_ERROR) {
            return Result.status(null, loginOutDo.getErrorCode(), MessageHandler.getExceptionMessage(loginOutDo.getErrorCode(), RequestHelper.getRequest()));
        }

        LoginVo vo = new LoginVo();
        vo.setToken(loginOutDo.getToken());
        vo.setUserId(String.valueOf(loginOutDo.getUserId()));
        vo.setUserName(loginOutDo.getUserName());

        try{
            // dataScopeClient.datascopeLoad(vo.getToken());

            dataScopeClient.getCodeList(vo.getToken());
        } catch (Exception e){
            log.error("dataScopeClient datascopeLoad err,{}", e.getMessage(), e);
        }

        return Result.ok(vo);
    }
}
