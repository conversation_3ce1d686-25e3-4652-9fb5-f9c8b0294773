package com.caidaocloud.user.service.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.domain.repository.IHisUserPwdRecRepository;
import com.caidaocloud.user.service.domain.util.ListUtil;
import com.caidaocloud.util.SnowflakeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户密码历史表
 */
@Slf4j
@Service
@Data
public class HisUserPwdRecDo extends BaseEntity {
    /**
     * 修改记录ID
     */
    @TableId
    private Long recId;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐值
     */
    private String salt;

    @Autowired
    private IHisUserPwdRecRepository hisUserPwdRecRepository;

    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    @Transactional
    public void syncSave(List<HisUserPwdRecDo> dataList) throws Exception {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        // 主键字段检查
        long count = dataList.stream().filter(o -> o.getRecId() == null).count();
        if (count > 0) {
            throw new ServerException("主键ID不允许为空");
        }
        // 清除老数据
        List<Long> ids = dataList.stream().map(HisUserPwdRecDo::getRecId).distinct().collect(Collectors.toList());
        hisUserPwdRecRepository.delete(ids);
        // 必填字段检查
        BaseEntity.setDefValueOfRequiredField(dataList);
        // 数据保存
        List<List<HisUserPwdRecDo>> lists = ListUtil.split(dataList, 500);
        for (List<HisUserPwdRecDo> list : lists) {
            hisUserPwdRecRepository.insertBatch(list);
        }
    }

    public List<HisUserPwdRecDo> getListByAccountId(Long tenantId, Long accountId) {
        return hisUserPwdRecRepository.getListByAccountId(tenantId, accountId);
    }

    public List<HisUserPwdRecDo> getPageListByAccountId(Long tenantId, Long accountId, long current, long size) {
        return hisUserPwdRecRepository.getPageListByAccountId(tenantId, accountId, current, size);
    }
}
