package com.caidaocloud.user.service.application.foreign.aliyun.util;


import com.aliyun.teaopenapi.models.Config;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;

public class AliyunApiHelper {
    private static IAcsClient acsClient;

    private AliyunApiHelper() {
    }

    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @return
     * @throws Exception
     */
    public static com.aliyun.dypnsapi20170525.Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "dypnsapi.aliyuncs.com";
        return new com.aliyun.dypnsapi20170525.Client(config);
    }

    public static IAcsClient getClient(String regionId, String accessKey, String accessSecret) throws Exception {
        if (acsClient == null) {
            IClientProfile profile = DefaultProfile.getProfile(regionId, accessKey, accessSecret);
            acsClient = new DefaultAcsClient(profile);
        }
        return acsClient;
    }

    public static CommonRequest getSmsRequest() {
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysAction("SendSms");
        request.setSysVersion("2017-05-25");
        return request;
    }
}
