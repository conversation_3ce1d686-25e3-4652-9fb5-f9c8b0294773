package com.caidaocloud.user.service.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.AccountPayslipInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccountPayslipInfoMapper extends BaseMapper<AccountPayslipInfo> {
    int insertSelective(AccountPayslipInfo record);

    int updateByPrimaryKeySelective(AccountPayslipInfo record);

    int insertBatch(@Param("records") List<AccountPayslipInfo> records);

    int updateBatch(@Param("records") List<AccountPayslipInfo> records);

    AccountPayslipInfo selectByPrimaryKey(@Param("accountId") Long accountId);
}
