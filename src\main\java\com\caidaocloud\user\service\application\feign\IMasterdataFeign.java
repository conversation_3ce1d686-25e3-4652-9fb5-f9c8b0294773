package com.caidaocloud.user.service.application.feign;

import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.user.service.application.dto.masterdata.EmpSearchDto;
import com.caidaocloud.user.service.application.dto.masterdata.EmpWorkInfoDto;
import com.caidaocloud.user.service.application.feign.fallback.MasterdataFeignFallback;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023/5/26
 */
@FeignClient(value = "caidaocloud-masterdata-service", path = "/api/masterdata",
		fallback = MasterdataFeignFallback.class, configuration = FeignConfiguration.class,contextId = "masterdataFeignV2")
public interface IMasterdataFeign {

	/**
	 * 获取员工信息
	 * @return
	 */
	@PostMapping(value = "/v2/emp/empInfo/list")
	Result<List<EmpWorkInfoDto>> getEmpInfoByEmpIds(@RequestBody EmpSearchDto dto);


	/**
	 * 获取员工信息
	 * @return
	 */
	@PostMapping(value = "/v2/emp/listByWorknoAndEmail")
	Result<List<EmpWorkInfoDto>> listByWorknoAndEmail(@RequestBody EmpSearchDto dto);


	@GetMapping("/v2/emp/loadEmpInfoByWorkno")
	Result<EmpWorkInfoDto> loadEmpInfoByWorkno(@RequestParam(value = "workno") String workno, @RequestParam(value = "datetime") Long datetime);
}
