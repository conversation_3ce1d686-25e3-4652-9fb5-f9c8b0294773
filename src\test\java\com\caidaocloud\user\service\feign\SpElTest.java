package com.caidaocloud.user.service.feign;

import com.alibaba.fastjson.JSON;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.HashMap;
import java.util.Map;

public class SpElTest {
    public static void main(String[] args) {
        String str = "{\"msg\":\"success\",\"code\":0,\"success\":true,\"serverTime\":1623058188421}";
        Map dataMap = JSON.parseObject(str, Map.class);

        EvaluationContext context = new StandardEvaluationContext();
        // 为了让表达式可以访问该对象, 先把对象放到上下文中
        context.setVariable("dataMap", dataMap);

        ExpressionParser parser = new SpelExpressionParser();
        String nodeVal = "";
        Map<String, Object> afterExecMap = new HashMap();
        afterExecMap.put("deadline", "#dataMap['data'] == null ? null : #dataMap['data']['deadline']");
        afterExecMap.put("staffid", "#dataMap['data'] == null ? null: #dataMap['data']['staffid']");
        for (Map.Entry<String, Object> node : afterExecMap.entrySet()) {
            if(null == node.getValue()){
                continue;
            }

            nodeVal = String.valueOf(node.getValue());
            if(nodeVal.indexOf("#") > -1){
                node.setValue(parser.parseExpression(nodeVal).getValue(context));
            }
        }

        for (Object obj : afterExecMap.values()) {
            System.out.println("------" + obj);
        }
    }
}
