package com.caidaocloud.user.service.application.dto.operate;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户导入dto
 *
 * <AUTHOR>
 * @date 2022/5/18
 **/
@Data
public class UserImportDto implements Serializable {

    private Long accountId;

    private Long empId;

    @Excel(name = "*姓名")
    private String name;

    @Excel(name = "工号")
    private String workno;

    @Excel(name = "所属角色")
    private String roles;

    @Excel(name = "*手机号")
    private String mobile;

    @Excel(name = "邮箱")
    private String email;

    @Excel(name = "密码")
    private String password;

    private Long userId;

    private Long corpid;

}
