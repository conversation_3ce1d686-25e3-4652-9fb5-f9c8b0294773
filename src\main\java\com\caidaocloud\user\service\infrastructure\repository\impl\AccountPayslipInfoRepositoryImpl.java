package com.caidaocloud.user.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.em.DeleteStatusEnum;
import com.caidaocloud.user.service.domain.entity.AccountPayslipInfoDo;
import com.caidaocloud.user.service.domain.enums.AccountType;
import com.caidaocloud.user.service.domain.repository.IAccountPayslipInfoRepository;
import com.caidaocloud.user.service.infrastructure.repository.mapper.AccountPayslipInfoMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.AccountPayslipInfo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class AccountPayslipInfoRepositoryImpl implements IAccountPayslipInfoRepository {
    @Autowired
    private AccountPayslipInfoMapper accountPayslipInfoMapper;

    @Override
    public List<AccountPayslipInfoDo> selectList(String account, AccountType accountType) {
        LambdaQueryWrapper<AccountPayslipInfo> mQueryInfo = new QueryWrapper<AccountPayslipInfo>().lambda();
        mQueryInfo.ne(AccountPayslipInfo::getDeleted, 1);
        mQueryInfo.eq(AccountPayslipInfo::getAccount, account);
        List<AccountPayslipInfo> list = accountPayslipInfoMapper.selectList(mQueryInfo);
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : ObjectConverter.convertList(list, AccountPayslipInfoDo.class);
    }

    @Override
    public void insertBatch(List<AccountPayslipInfoDo> dataList) {
        accountPayslipInfoMapper.insertBatch(ObjectConverter.convertList(dataList, AccountPayslipInfo.class));
    }

    @Override
    public void insert(AccountPayslipInfoDo data) {
        accountPayslipInfoMapper.insert(ObjectConverter.convert(data, AccountPayslipInfo.class));
    }

    @Override
    public void update(AccountPayslipInfoDo data) {
        accountPayslipInfoMapper.updateById(ObjectConverter.convert(data, AccountPayslipInfo.class));
    }

    @Override
    public int insertSelective(AccountPayslipInfoDo record) {
        return accountPayslipInfoMapper.insertSelective(ObjectConverter.convert(record, AccountPayslipInfo.class));
    }

    @Override
    public int updateByPrimaryKeySelective(AccountPayslipInfoDo record) {
        return accountPayslipInfoMapper.updateByPrimaryKeySelective(ObjectConverter.convert(record, AccountPayslipInfo.class));
    }

    @Override
    public void deleteByIds(List<Long> accountIds) {
        accountPayslipInfoMapper.deleteBatchIds(accountIds);
    }

    @Override
    public void softDeleteByIds(List<Long> accountIds) {
        AccountPayslipInfo accountBaseInfo = new AccountPayslipInfo();
        accountBaseInfo.setDeleted(DeleteStatusEnum.DELETED.getIndex());
        accountBaseInfo.setUpdateTime(System.currentTimeMillis());
        LambdaQueryWrapper<AccountPayslipInfo> mQueryInfo = new QueryWrapper<AccountPayslipInfo>().lambda();
        mQueryInfo.in(AccountPayslipInfo::getAccountId, accountIds);
        accountPayslipInfoMapper.update(accountBaseInfo, mQueryInfo);
    }

    @Override
    public List<AccountPayslipInfoDo> getListByIds(List<Long> accountIds) {
        return ObjectConverter.convertList(accountPayslipInfoMapper.selectBatchIds(accountIds), AccountPayslipInfoDo.class);
    }

    @Override
    public AccountPayslipInfoDo getById(Long accountId) {
        AccountPayslipInfo info = accountPayslipInfoMapper.selectById(accountId);
        return info == null ? null : ObjectConverter.convert(info, AccountPayslipInfoDo.class);
    }

    @Override
    public List<AccountPayslipInfoDo> selectAccountByMobNumAndEmail(String mobNum, String email) {
        LambdaQueryWrapper<AccountPayslipInfo> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.ne(AccountPayslipInfo::getDeleted, 1);
        queryWrapper.select(AccountPayslipInfo::getAccountId, AccountPayslipInfo::getMobNum, AccountPayslipInfo::getEmail)
                .eq(AccountPayslipInfo::getMobNum, mobNum);
        if (!StringUtils.isEmpty(email)) {
            queryWrapper.or().eq(AccountPayslipInfo::getEmail, email);
        }
        List<AccountPayslipInfo> list = accountPayslipInfoMapper.selectList(queryWrapper);
        return Sequences.sequence(list).map(e -> FastjsonUtil.convertObject(e, AccountPayslipInfoDo.class)).toList();
    }

    @Override
    public List<AccountPayslipInfoDo> getAccountByMobNumAndEmail(String mobNum, String email) {
        LambdaQueryWrapper<AccountPayslipInfo> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.ne(AccountPayslipInfo::getDeleted, 1);
        queryWrapper.select(AccountPayslipInfo::getAccountId, AccountPayslipInfo::getMobNum, AccountPayslipInfo::getEmail);
        if (!StringUtils.isEmpty(mobNum)) {
            queryWrapper.eq(AccountPayslipInfo::getMobNum, mobNum);
        }
        if (!StringUtils.isEmpty(email)) {
            queryWrapper.eq(AccountPayslipInfo::getEmail, email);
        }
        List<AccountPayslipInfo> list = accountPayslipInfoMapper.selectList(queryWrapper);
        return Sequences.sequence(list).map(e -> FastjsonUtil.convertObject(e, AccountPayslipInfoDo.class)).toList();
    }

    @Override
    public AccountPayslipInfoDo getAccountByAccountId(Long accountId) {
        var queryWrapper = new QueryWrapper<AccountPayslipInfo>().lambda()
                .eq(AccountPayslipInfo::getAccountId, accountId);
        AccountPayslipInfo accountBaseInfo = accountPayslipInfoMapper.selectOne(queryWrapper);
        return FastjsonUtil.convertObject(accountBaseInfo, AccountPayslipInfoDo.class);
    }

    @Override
    public List<AccountPayslipInfoDo> getAccountByMobNumOrEmail(List<String> mobNums, List<String> emails) {
        LambdaQueryWrapper<AccountPayslipInfo> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.ne(AccountPayslipInfo::getDeleted, 1);
        queryWrapper.select(AccountPayslipInfo::getAccountId, AccountPayslipInfo::getMobNum, AccountPayslipInfo::getEmail);
        queryWrapper.and(wq->{
            if (!CollectionUtils.isEmpty(emails)) {
                wq.in(AccountPayslipInfo::getEmail, emails);
            }
            if (!CollectionUtils.isEmpty(mobNums)) {
                wq.or().in(AccountPayslipInfo::getMobNum, mobNums);
            }
            return wq;
        });

        List<AccountPayslipInfo> list = accountPayslipInfoMapper.selectList(queryWrapper);
        return Sequences.sequence(list).map(e -> FastjsonUtil.convertObject(e, AccountPayslipInfoDo.class)).toList();
    }

    @Override
    public int updateBatch(List<AccountPayslipInfoDo> dataList) {
        return accountPayslipInfoMapper.updateBatch(ObjectConverter.convertList(dataList, AccountPayslipInfo.class));
    }

    @Override
    public List<AccountPayslipInfoDo> getAccountByMobNums(List<String> mobNums) {
        LambdaQueryWrapper<AccountPayslipInfo> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.ne(AccountPayslipInfo::getDeleted, 1);
        queryWrapper.select(AccountPayslipInfo::getAccountId, AccountPayslipInfo::getMobNum, AccountPayslipInfo::getEmail)
                .in(AccountPayslipInfo::getMobNum, mobNums);
        List<AccountPayslipInfo> list = accountPayslipInfoMapper.selectList(queryWrapper);
        return Sequences.sequence(list).map(e -> FastjsonUtil.convertObject(e, AccountPayslipInfoDo.class)).toList();
    }
}
