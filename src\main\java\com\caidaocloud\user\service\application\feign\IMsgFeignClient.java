package com.caidaocloud.user.service.application.feign;

import com.caidaocloud.message.sdk.feign.MessageFeignClientFallBack;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

@FeignClient(
        value = "caidaocloud-message-service",
        fallback = MessageFeignClientFallBack.class,
        configuration = FeignConfiguration.class,
        contextId = "msgFeignClient"
)
public interface IMsgFeignClient {

    @PostMapping("/api/msg/emailconfig/v1/sendMail")
    Result sendEmail(@RequestBody Map<String, String> map);
}
