package com.caidaocloud.user.service.infrastructure.repository.po;

import lombok.Data;
import lombok.experimental.Accessors;
import org.mongodb.morphia.annotations.*;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/4/2021 3:55 PM
 *        4
 */
@Entity(value = "user", noClassnameStored = true)
@Indexes(@Index(fields = @Field("id")))
@Component
@Data
@Accessors(chain = true)
public class UserPo {
    @Id
    private String id;
    private String tenantId;
    private String staffid;
    private Integer userid;
    private Integer corpid;
    private String account;
    private String passwd;
    private String empname;
    private String mobnum;
    private String linktel;
    private String email;
    private Integer empid;
    private Boolean issuperadmin;
    private Integer status;
    private String salt;
    private Integer crtuser;
    private Long crttime;
    private Integer upduser;
    private Long updtime;
    private Integer belongOrgId;
    private String channelId;
    private Integer mobType;
    private String passremind;
    private String deadline;
    private String logintimes;
    private Long locktime;
    private String gesture;
    private String roleids;
    private Integer orgid;
    private Integer postId;
    private String photoUrl;
    private Integer clockType;
    private String globalid;
    /**
     * 第三方平台编码
     */
    private String thirdPart;
    /**
     * 第三方数据ID
     */
    private String thirdId;
    /**
     * 预留扩展字段
     */
    private Map extMap;
    /**
     * 最后登录时间
     */
    private Long lastLoginTime;
}
