package com.caidaocloud.user.service.application.dto.preemp;

import com.google.j2objc.annotations.AutoreleasePool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class PreEmpUserDto {

    @ApiModelProperty("手机号")
    private String mobileNo;

    @ApiModelProperty("员工名称")
    private String userName;

    @ApiModelProperty("员工Id")
    private Long empId;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("角色信息")
    private List<String> roleIds;

    private Long lastEmpId;

    private boolean reentry;

    private Long toId;
}
