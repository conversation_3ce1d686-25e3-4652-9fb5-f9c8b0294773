package com.caidaocloud.user.service.infrastructure.util;

import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.WebUtil;

/**
 * 多语言工具类
 */
public final class LangUtil {
    /**
     * 获取多语言提示
     */
    public static String getMsg(int msgCode){
        return MessageHandler.getExceptionMessage(msgCode, WebUtil.getRequest());
    }

    /**
     * 带占位符的多语言
     */
    public static String getFormatMsg(int msgCode, Object... args){
        String format = getMsg(msgCode);
        return String.format(format, args);
    }
}
