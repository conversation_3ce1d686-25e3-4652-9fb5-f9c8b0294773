package com.caidaocloud.user.service.application.event.publish.message;

import com.caidaocloud.mq.rabbitmq.RabbitBaseMessage;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * created by: FoAng
 * create time: 13/12/2022 1:48 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserEventMsg extends RabbitBaseMessage implements Serializable {

    @ApiModelProperty("时间类型，新增、修改、删除等操作")
    private String type;

    @ApiModelProperty("用户信息")
    private UserBaseInfoDo userBaseInfoDo;

}
