package com.caidaocloud.user.service;

import com.caidaocloud.record.core.annotation.EnableLogRecord;
import com.caidaocloud.util.SpringUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/3/2021 10:59 PM
 * 4
 */
@EnableCircuitBreaker
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication
@EnableLogRecord(name = "user")
@ComponentScan(basePackages = {"com.caidaocloud.user.service", "com.caidaocloud.config", "com.caidaocloud.util","com.caidaocloud.message.sdk"})
@MapperScan(value = {"com.caidaocloud.user.service.infrastructure.repository.mapper"})
public class Application extends SpringBootServletInitializer {
    @Bean
    public SpringUtil getSpringUtil() {
        return new SpringUtil();
    }

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
