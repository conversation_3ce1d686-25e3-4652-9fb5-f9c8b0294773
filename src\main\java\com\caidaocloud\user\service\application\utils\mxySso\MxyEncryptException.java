package com.caidaocloud.user.service.application.utils.mxySso;
import com.caidaocloud.excption.ServerException;

import java.util.HashMap;
import java.util.Map;

public class MxyEncryptException extends ServerException {
    private static final long serialVersionUID = -2463314363764052567L;
    private static Map<Integer, String> msgMap = new HashMap();
    public Integer code;

    static {
        msgMap.put(Integer.valueOf(0), "成功");
        msgMap.put(Integer.valueOf(900001), "加密明文文本非法");
        msgMap.put(Integer.valueOf(900002), "加密时间戳参数非法");
        msgMap.put(Integer.valueOf(900003), "加密随机字符串参数非法");
        msgMap.put(Integer.valueOf(900005), "签名不匹配");
        msgMap.put(Integer.valueOf(900006), "签名计算失败");
        msgMap.put(Integer.valueOf(900004), "不合法的aes key");
        msgMap.put(Integer.valueOf(900007), "计算加密文字错误");
        msgMap.put(Integer.valueOf(900008), "计算解密文字错误");
        msgMap.put(Integer.valueOf(900009), "计算解密文字长度不匹配");
        msgMap.put(Integer.valueOf(900010), "计算解密文字corpid或者suiteKey不匹配");
    }

    public MxyEncryptException(Integer exceptionCode) {
        super((String) msgMap.get(exceptionCode));
        this.code = exceptionCode;
    }
}
