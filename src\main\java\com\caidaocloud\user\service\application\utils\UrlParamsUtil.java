package com.caidaocloud.user.service.application.utils;

import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 */
public class UrlParamsUtil {
    private static Pattern pattern = Pattern.compile("([^?=]+)=([^&=]*)");

    public static Map<String, String> parseParams(String url) throws UnsupportedEncodingException {
        String decodedQuery = URLDecoder.decode(url, "UTF-8");
        Map<String, String> params = Maps.newHashMap();
        Matcher matcher = pattern.matcher(decodedQuery);
        while (matcher.find()) {
            params.put(matcher.group(1), matcher.group(2));
        }
        return params;
    }

    public static String buildUrl(String url, Map<String, String> params, boolean decode) throws UnsupportedEncodingException, MalformedURLException {
        if (params == null || params.isEmpty()) {
            return url;
        } else {
            URL originUrl = new URL(url);
            Map<String, String> urlParams = parseParams(url);
            params.putAll(urlParams);
            StringBuilder paramsBuilder = new StringBuilder();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (paramsBuilder.length() > 0) {
                    paramsBuilder.append("&");
                }
                if (decode) {
                    paramsBuilder.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                            .append("=")
                            .append(URLEncoder.encode(entry.getValue(), "UTF-8"));
                } else {
                    paramsBuilder.append(entry.getKey())
                            .append("=")
                            .append(entry.getValue());
                }
            }
            String redirectUrl = new URL(originUrl.getProtocol(), originUrl.getHost(), originUrl.getPort(), originUrl.getPath()).toString();
            return redirectUrl + (StringUtils.isEmpty(paramsBuilder.toString()) ? "" :
                    "?" + paramsBuilder);
        }
    }
}
