package com.caidaocloud.user.service.domain.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.dto.LoginPlatform;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.dto.SimpleSession;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.session.SessionUtil;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.feign.IAuthFeign;
import com.caidaocloud.user.service.domain.entity.*;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SignUtil;
import com.google.common.base.Joiner;
import com.googlecode.totallylazy.Option;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/6/2021 1:57 PM
 *        4
 */
@Slf4j
@Service
public class LoginDomainService {
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private User user;
    @Autowired
    private Tenant tenant;
    @Autowired
    private IAuthFeign authFeign;

    public Tenant getTenantByCorpid(Long corpid) {
        Tenant tenantDo = tenant.getTenantByCorpid(corpid);
        return tenantDo;
    }

    public User getUserByAccount(String account) {
        User userDo = user.getByAccount(account);
        return userDo;
    }

    public User getByAccountTenantId(String account, String tenantId) {
        User userDo = user.getByAccountTenantId(account, tenantId);
        return userDo;
    }

    public LoginOutDto login(User userDo, LoginDo loginDo) {
        LoginOutDto loginOutDo = new LoginOutDto();

        if (StringUtils.isNotEmpty(userDo.getPasswd())
                && !SignUtil.md5(loginDo.getPassword()).equals(userDo.getPasswd())) {
            loginOutDo.setErrorCode(ErrorCodes.INVALIDED_USER_OR_PASSWORD);
            return loginOutDo;
        }

        loginOutDo = getLoginOutDto(userDo, loginDo);
        return loginOutDo;
    }

    public LoginOutDto getLoginOutDto(User userDo, LoginDo loginDo) {
        LoginOutDto loginOutDo = new LoginOutDto();
        String tokens = "";
        try {
            tokens = TokenGenerator.getToken(String.valueOf(userDo.getUserid()), userDo.getTenantId(),
                    loginDo.getLoginPlatform());
            loginOutDo.setUserId(String.valueOf(userDo.getUserid()));
            loginOutDo.setToken(tokens);
            loginOutDo.setUserName(userDo.getEmpname());
        } catch (Exception ex) {
            log.error("Login failed, getLoginOutDto err,{}", ex.getMessage(), ex);
        }

        // 缓存用户信息
        UserInfo userInfo = ObjectConverter.convert(userDo, UserInfo.class);
        userInfo.setExtMap(loginDo.getExtMap());

        // 更新用户最后登录时间
        try {
            userDo.updateLastLoginTime();
        } catch (Exception e) {
            log.error("更新用户最后登录时间失败", e);
        }

        this.sessionRefresh(loginDo, userInfo);
        // TODO 异步记录登录日志
        return loginOutDo;
    }

    private UserInfo initUserInfoFromBaseInfo(UserBaseInfoDo userBaseInfo) {
        var tenantId = null == userBaseInfo.getTenantId() ? null : userBaseInfo.getTenantId().toString();
        var userInfo = new UserInfo();
        userInfo.setUserName(userBaseInfo.getUserName());
        userInfo.setUserid(userBaseInfo.getUserId().intValue());
        userInfo.doSetUserId(userBaseInfo.getUserId());
        userInfo.setTenantId(tenantId);
        userInfo.setBelongOrgId(null == userBaseInfo.getTenantId() ? null : userBaseInfo.getTenantId().intValue());
        if (userBaseInfo.getEmpId() != null) {
            userInfo.setEmpid(userBaseInfo.getEmpId().intValue());
            userInfo.setStaffId(userBaseInfo.getEmpId());
        }
        if (null != userBaseInfo.getCorpId()) {
            userInfo.setCorpid(userBaseInfo.getCorpId().intValue());
        }
        if (userInfo.getUserId() == -1L) {
            userInfo.setIssuperadmin(true);
            return userInfo;
        }
        try {
            var securityUserInfo = new SecurityUserInfo();
            securityUserInfo.setTenantId(tenantId);
            securityUserInfo.setUserId(0L);
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            var authResult = authFeign.getRoleNameBySubjectIds(userBaseInfo.getUserId().toString());
            if (authResult.isSuccess() && !CollectionUtils.isEmpty(authResult.getData())) {
                var option = authResult.getData().stream()
                        .filter(e -> StringUtils.isNotBlank(e.getCodes())).map(e -> e.getCodes()).findFirst();
                var isAdmin = option.isPresent() && StringUtils.isNotBlank(option.get())
                        && option.get().contains("ADMIN");
                if (isAdmin) {
                    userInfo.setIssuperadmin(true);
                }
                var roleIdSequence = Sequences.sequence(authResult.getData())
                        .filter(e -> StringUtils.isNotBlank(e.getRoleIds()))
                        .map(e -> e.getRoleIds());
                if (!roleIdSequence.isEmpty()) {
                    var roleIdsStr = Joiner.on(",").join(roleIdSequence.toList());
                    userInfo.setRoleids(roleIdsStr);
                }
                Map ext = Option.option(userInfo.getExtMap()).getOrElse(new HashMap());
                Sequence<String> roleCodes = Sequences.sequence(authResult.getData())
                        .flatMap(auth -> StringUtils.isEmpty(auth.getCodes()) ? Sequences.sequence()
                                : Sequences.sequence(auth.getCodes()
                                        .split(",")))
                        .filter(StringUtils::isNotBlank);
                ext.put("roleCodes", Joiner.on(",").join(roleCodes.toList()));
                ext.put("lastLoginTime", userBaseInfo.getLastLoginTime());
                // TODO: 2025/9/16 ext中新增上次登录时间
                userInfo.setExtMap(ext);
            }
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        return userInfo;
    }

    public synchronized void sessionRefresh(AccountLoginDto loginDo, UserBaseInfoDo userBaseInfo) {
        sessionService.doDelete();
        String key = "";
        var userInfo = initUserInfoFromBaseInfo(userBaseInfo);
        SimpleSession simpleSession = new SimpleSession();
        simpleSession.setAttribute(SessionUtil.AUTH_USER_INFO, userInfo);
        String auth = "[\"PayrollArchives\",\"payrollStructureList\",\"payrollEmpgroupList\",\"PayrollCalculation\",\"PayrollReport\",\"PayrollSettings\",\"DataMaintenance\",\"SocialReport\",\"SocialSecurityPolicyList\",\"HfPolicyList\",\"dictpayroll\",\"SobSelectSobOptions\",\"SobSelectWaGroupList\",\"LeaveListGet\",\"LeaveBalanceListRecord\",\"AnnualLeaveDetailsListGet\",\"LeaveDetailsListGet\",\"FixedQuotaDetailsGet\",\"OvertimeapplyEmpOtList\",\"OutOfficeRuleList\",\"OutOfficeRecordList\",\"OutOfficePunchInList\",\"RegisterListRecord\",\"RegisterBdkListGet\",\"ShiftList\",\"WorkroundList\",\"HolidayList\",\"WorkCalendarList\",\"ClockPlanList\",\"HolidayRules\",\"AttendanceTotalSettings\",\"AttendanceGroupList\",\"AttendanceMessageNotification\",\"AttendanceSobList\",\"EmployeeAttendanceScheme\",\"EmployeeClockPlan\",\"EmployeeCalendar\",\"AttendanceSchedulingDetails\",\"AttendanceAdjustmentRecord\",\"AttendanceWorkflowProcess\",\"AttendanceWorkflowTemplate\",\"AttendanceWorkflowControl\",\"AttendanceWorkflowDelegate\",\"AttendanceWorkflowApprovalDocument\",\"PayrollPlan\",\"SocialSettings\",\"QuotaList\",\"WorkCalendar\",\"HolidaySettings\",\"AttendanceWorkflowSetting\",\"AttendanceWorkflowMonitor\",\"PayrollManage\",\"SocialSecurityFund\",\"AttendanceStatistics\",\"VacationManagement\",\"OutOfficeManagementList\",\"CheckInRecord\",\"AttendanceSettings\",\"AttendanceFile\",\"AttendanceWorkflow\",\"PayrollModel\",\"AttendanceShiftRecord\",\"AttendanceWorkforceManagement\",\"AttendanceModel\"]";
        simpleSession.setAttribute(SessionUtil.AUTH_SECURITY_INFO, FastjsonUtil.toList(auth, String.class));
        log.info("session is " + FastjsonUtil.toJson(simpleSession));
        if (loginDo.getLoginPlatform() == LoginPlatform.THIRD_WEB.getValue()) {
            key = String.format("t-user-session-%s:%s", StringUtils.trimToEmpty(userInfo.getTenantId()),
                    userInfo.getUserId());
            cacheService.cacheObject(key, simpleSession, 3600L);
        } else if (loginDo.getLoginPlatform() == LoginPlatform.THIRD_MOBILE.getValue()) {
            key = String.format("m-t-user-session-%s:%s", StringUtils.trimToEmpty(userInfo.getTenantId()),
                    userInfo.getUserId());
            cacheService.cacheObject(key, simpleSession, -1L);
        } else if (loginDo.getLoginPlatform() == LoginPlatform.WEB.getValue()) {
            key = String.format("user-session-%s:%s", StringUtils.trimToEmpty(userInfo.getTenantId()),
                    userInfo.getUserId());
            log.info("key " + key + " with value " + FastjsonUtil.toJson(simpleSession));
            cacheService.cacheObject(key, simpleSession, 3600L);
        } else if (loginDo.getLoginPlatform() == LoginPlatform.MOBILE.getValue()) {
            key = String.format("m-user-session-%s:%s", StringUtils.trimToEmpty(userInfo.getTenantId()),
                    userInfo.getUserId());
            cacheService.cacheObject(key, simpleSession, -1L);
        } else {
            this.setSessionCacheInfo(userInfo);
            log.error("sessionRefresh err, loginDo=[{}],UserInfo=[{}]", FastjsonUtil.toJson(loginDo),
                    FastjsonUtil.toJson(userInfo));
        }
    }

    public synchronized void sessionRefresh(LoginDo loginDo, UserInfo userInfo) {
        sessionService.doDelete();
        String key = "";
        SimpleSession simpleSession = new SimpleSession();
        simpleSession.setAttribute(SessionUtil.AUTH_USER_INFO, userInfo);
        if (loginDo.getLoginPlatform() == LoginPlatform.THIRD_WEB.getValue()) {
            key = String.format("t-user-session-%s:%s", StringUtils.trimToEmpty(userInfo.getTenantId()),
                    userInfo.getUserId());
            cacheService.cacheObject(key, simpleSession, 3600L);
        } else if (loginDo.getLoginPlatform() == LoginPlatform.THIRD_MOBILE.getValue()) {
            key = String.format("m-t-user-session-%s:%s", StringUtils.trimToEmpty(userInfo.getTenantId()),
                    userInfo.getUserId());
            cacheService.cacheObject(key, simpleSession, -1L);
        } else if (loginDo.getLoginPlatform() == LoginPlatform.WEB.getValue()) {
            key = String.format("user-session-%s:%s", StringUtils.trimToEmpty(userInfo.getTenantId()),
                    userInfo.getUserId());
            cacheService.cacheObject(key, simpleSession, 3600L);
        } else if (loginDo.getLoginPlatform() == LoginPlatform.MOBILE.getValue()) {
            key = String.format("m-user-session-%s:%s", StringUtils.trimToEmpty(userInfo.getTenantId()),
                    userInfo.getUserId());
            cacheService.cacheObject(key, simpleSession, -1L);
        } else {
            this.setSessionCacheInfo(userInfo);
            log.error("sessionRefresh err, loginDo=[{}],UserInfo=[{}]", FastjsonUtil.toJson(loginDo),
                    FastjsonUtil.toJson(userInfo));
        }
    }

    public synchronized void sessionRefresh(SessionDo sessionData) {
        sessionData.initSession();
        if (log.isDebugEnabled()) {
            log.debug("sessionKey:" + sessionData.getSessionKey());
        }
        SimpleSession session = sessionService.getSession(sessionData.getSessionKey());
        if (null == session) {
            return;
        }
        Object obj = session.getAttribute(SessionUtil.AUTH_SECURITY_INFO);
        List<String> menuCodeList = FastjsonUtil.toList(FastjsonUtil.toJson(obj), String.class);
        sessionData.addCodeList(menuCodeList);
        if (sessionData.getAuthUrlSet() != null && sessionData.getAuthUrlSet().size() > 0) {
            session.setAttribute(SessionUtil.AUTH_SECURITY_INFO, FastjsonUtil.toJson(sessionData.getAuthUrlSet()));
        } else {
            session.setAttribute(SessionUtil.AUTH_SECURITY_INFO, FastjsonUtil.toJson(sessionData.getCodeList()));
        }
        if (log.isDebugEnabled()) {
            log.debug("user session : " + FastjsonUtil.toJson(session));
        }
        cacheService.cacheObject(sessionData.getSessionKey(), session, sessionData.getExpireTime());
    }

    public synchronized void setSessionCacheInfo(UserInfo userInfo) {
        sessionService.setObject(SessionUtil.AUTH_USER_INFO, userInfo);
    }

}
