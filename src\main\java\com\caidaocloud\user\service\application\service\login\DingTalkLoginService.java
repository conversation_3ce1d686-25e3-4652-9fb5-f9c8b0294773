package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.dto.masterdata.EmpInfoDto;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.repository.ISysEmpInfoRepository;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class DingTalkLoginService implements IAccountLoginService {

    @Value("${sso.dingtalk.tenantId:}")
    private String dingTalkTenant;

    @Autowired
    private UserBaseInfoService userBaseInfoService;

    @Autowired
    private ISysEmpInfoRepository empInfoRepository;

    public String getSsoServiceKey() {
        return "saml.byWorkNo";
    }

    @Override
    public GrantType getGrantType() {
        return GrantType.DING_TALK_GRANT_TYPE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        PreCheck.preCheckArgument(StringUtil.isEmpty(loginDto.getAccount()), "未获取到workno参数，请检查");
        val workNo = loginDto.getAccount();
        log.info("Dingtalk login workNo: {}", workNo);
        val userInfo = new SecurityUserInfo();
        userInfo.setTenantId(dingTalkTenant);
        SecurityUserUtil.setSecurityUserInfo(userInfo);
        try {
            val empResult = empInfoRepository.getEmpInfoByWorkno(workNo);
            List<EmpInfoDto> empInfoList = FastjsonUtil.convertList(empResult, EmpInfoDto.class);
            if (empInfoList.isEmpty()) {
                throw new ServerException("login failed");
            } else {
                val empIdList = empInfoList.stream().filter(e -> Objects.nonNull(e.getEmpid())).map(e -> e.getEmpid()).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(empIdList)) {
                    throw new ServerException("login failed, not found effective empid");
                }
                List<UserBaseInfoDo> list = userBaseInfoService.listByEmpIds(empIdList);
                //只取正常状态的用户信息
                List<UserBaseInfoDo> filterList = list.stream().filter(userBaseInfoDo -> userBaseInfoDo.getStatus() == 1).collect(Collectors.toList());
                log.info("checkAndGetUser,filterList={}", FastjsonUtil.toJson(filterList));
                if (CollectionUtils.isEmpty(filterList)) {
                    throw new ServerException("login failed, filterList is empty!");
                }
                List<Long> collect = filterList.stream().filter(
                        st -> StringUtil.isNotEmpty(st.getReEmpId()) && st.getOnboarding() != null && st.getOnboarding()
                ).map(UserBaseInfoDo::getReEmpId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    //候选人二次入职原本信息
                    List<UserBaseInfoDo> reEmpUserList = userBaseInfoService.listByEmpIds(collect);
                    filterList.removeIf(st -> StringUtil.isNotEmpty(st.getReEmpId()) && st.getOnboarding());
                    filterList.addAll(reEmpUserList);
                }
                return filterList;
            }
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}
