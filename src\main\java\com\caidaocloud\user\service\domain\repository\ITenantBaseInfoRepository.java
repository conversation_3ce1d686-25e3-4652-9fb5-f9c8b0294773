package com.caidaocloud.user.service.domain.repository;

import com.caidaocloud.user.service.domain.entity.TenantBaseInfoDo;

import java.util.List;

public interface ITenantBaseInfoRepository {
    void insertBatch(List<TenantBaseInfoDo> dataList);

    void insert(TenantBaseInfoDo data);

    void update(TenantBaseInfoDo data);

    int insertSelective(TenantBaseInfoDo record);

    int updateByPrimaryKeySelective(TenantBaseInfoDo record);

    void delete(List<Long> tenantIds);

    void softDelete(List<Long> tenantIds);

    List<TenantBaseInfoDo> getTenantList(List<Long> tenantIds);

    TenantBaseInfoDo getTenantByCode(String tenantCode);

    List<TenantBaseInfoDo> getTenantListByCorpCode(String corpCode);

    List<TenantBaseInfoDo> getAllTenantList();
}
