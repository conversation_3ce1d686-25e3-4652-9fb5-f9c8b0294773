package com.caidaocloud.user.service.application.dto.operate;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户导入dto
 *
 * <AUTHOR>
 * @date 2022/5/18
 **/
@Data
public class UserImportErrorDto implements Serializable {

    @Excel(name = "*姓名", width = 20)
    private String name;

    @Excel(name = "工号", width = 20)
    private String workno;

    @Excel(name = "所属角色", width = 20)
    private String roles;

    @Excel(name = "*手机号", width = 20)
    private String mobile;

    @Excel(name = "邮箱", width = 20)
    private String email;

    @Excel(name = "错误信息", width = 50)
    private String errorMsg;

}
