package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.record.core.util.WebUtil;
import com.caidaocloud.user.service.application.dto.UserAccountBaseInfoDto;
import com.caidaocloud.user.service.application.service.ReceiptTokenService;
import com.caidaocloud.user.service.application.service.UserAppService;
import com.caidaocloud.user.service.application.service.login.IAccountLoginService;
import com.caidaocloud.user.service.application.service.sso.AuthorizeService;
import com.caidaocloud.user.service.domain.util.TemplateUtil;
import com.caidaocloud.user.service.interfaces.dto.*;
import com.caidaocloud.user.service.interfaces.granter.ITokenGranter;
import com.caidaocloud.user.service.interfaces.granter.TokenGranterBuilder;
import com.caidaocloud.user.service.interfaces.vo.*;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.googlecode.totallylazy.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.List;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/4/2021 6:49 PM
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserController {
    @Resource
    private UserAppService userAppService;
    @Resource
    private AuthorizeService authorizeService;
    @Resource
    private ReceiptTokenService receiptTokenService;

    @ApiOperation(value = "查询用户和账号基本信息", produces = "application/json")
    @RequestMapping(value = "/v1/getUserAndAccountInfo", method = RequestMethod.GET)
    public Result<UserAccountBaseInfoVo> getUserAndAccountInfo(@RequestParam("userId") Long userId) {
        UserAccountBaseInfoDto userDto = userAppService.getUserAccountByUserId(userId);
        return ResponseWrap.wrapResult(ObjectConverter.convert(userDto, UserAccountBaseInfoVo.class));
    }

    @ApiOperation(value = "账户登录/获取用户列表", produces = "application/json")
    @RequestMapping(value = "/v1/account/login", method = RequestMethod.POST)
    @LogRecordAnnotation(
            success = "通过账号密码登录",
            menu = "{{#platform}}-登录",
            condition = "{{#condition}}",
            category = "登入",
            operator = "{{#userId}}",
            tenantId = "{{#tenantId}}"
    )
    public Result<AccountLoginVo> accountLogin(@RequestBody AccountLoginDto loginDto, HttpServletRequest request) {
        AccountLoginVo grant = IAccountLoginService.getInstance(loginDto.getGrantType()).grant(loginDto);
        LogRecordContext.putVariable("userId", grant.getUserId());
        LogRecordContext.putVariable("platform", WebUtil.getOperatorPlatform(request));
        LogRecordContext.putVariable("condition", true);
        return Result.ok(grant);
    }

    @ApiOperation(value = "根据回执二次确认登录", produces = "application/json")
    @RequestMapping(value = "/v1/receipt/login", method = RequestMethod.POST)
    public Result<AccountLoginVo> receiptLogin(@RequestBody ReceiptLoginDto loginDto) {
        return Result.ok(receiptTokenService.grant(loginDto));
    }

    @ApiOperation(value = "用户登录", produces = "application/json")
    @RequestMapping(value = "/v1/login", method = RequestMethod.POST)
    public Result<LoginVo> login(@RequestBody LoginDto loginDto) {
        ITokenGranter granter = TokenGranterBuilder.getGranter(loginDto.getLoginType());
        return granter.grant(loginDto);
    }

    @ApiOperation(value = "查询用户信息", produces = "application/json")
    @RequestMapping(value = "/v1/getUserById", method = RequestMethod.GET)
    public Result<UserVo> getUserById(@RequestParam("id") Integer id) {
        UserDto userDto = userAppService.getByUserId(id);
        UserVo vo = new UserVo();
        if (userDto != null) {
            vo = ObjectConverter.convert(userDto, UserVo.class);
        }
        return ResponseWrap.wrapResult(vo);
    }

    //
//    @ApiOperation(value = "修改用户信息", produces = "application/json")
//    @PostMapping(value = "/v1/updateClockType")
//    public Result<UserVo> updateClockType(@RequestBody UserDto userDto) {
//        log.info("updateClockType body=[{}]", JSON.toJSONString(userDto));
//        UserDto newUser = userAppService.updateClockType(userDto);
//        UserVo vo = new UserVo();
//        if (userDto != null) {
//            vo = ObjectConverter.convert(newUser, UserVo.class);
//        }
//
//        return ResponseWrap.wrapResult(vo);
//    }
//
    @ApiOperation(value = "验证用户状态")
    @GetMapping(value = "/v1/status/verify")
    public Result statusVerify() {
        UserInfo userInfo = userAppService.statusVerify();
        if (null == userInfo) {
            return Result.fail();
        }

        UserVo userVo = ObjectConverter.convert(userInfo, UserVo.class);
        return Result.ok(userVo);
    }

    @ApiOperation(value = "用户退出登录")
    @GetMapping(value = "/v1/logout")
    public Result logout() {
        userAppService.logout();
        return Result.success();
    }

    @ApiOperation(value = "获取SSO配置")
    @GetMapping(value = "/v1/sso/config")
    public Result<LoginConfigVo> getLoginConfig(@RequestParam("appCode") String appCode) {
        log.info("sso/config appCode=[{}]", appCode);
        return Result.ok(authorizeService.getLoginConfig(appCode));
    }

    @ApiOperation(value = "修改密码")
    @PostMapping(value = "/v1/account/changePassword")
    public Result changePassword(@RequestBody ChangePasswordDto dto) {
        userAppService.changePassword(dto);
        return Result.ok(true);
    }
    
    @ApiOperation(value = "模板下载")
    @GetMapping(value = "/v1/import/template")
    public void downloadTemplate(HttpServletResponse response){
        List<String> head = Lists.list("姓名", "工号", "所属角色", "手机号", "邮箱", "密码");
        List<String> required = Lists.list("姓名", "手机号");
        String desc = "填表须知： 表头橙色项为必填项，白色为非必填项\n" +
                "所属角色：需与已有角色名称保持一致，多个角色需用英文逗号隔开；新增与账号关联的角色，即账号已关联角色A，导入角色B，导入后账号关联A和B";
        try (Workbook workbook = TemplateUtil.createTemplate(head, required, desc)) {
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("用户导入.xlsx", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            throw new ServerException(e.getMessage(), e);
        }
    }
}
