package com.caidaocloud.user.service.domain.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoQueryDto;

import java.util.List;

public interface IUserBaseInfoRepository {
    void insertBatch(List<UserBaseInfoDo> dataList);

    Long insert(UserBaseInfoDo data);

    void update(UserBaseInfoDo data);

    int insertSelective(UserBaseInfoDo record);

    int updateByPrimaryKeySelective(UserBaseInfoDo record);

    int deleteByIds(List<Long> userIds);

    void softDeleteByIds(List<Long> userIds);

    void updateDingUserIdByEmpId(Long empId, String dingUserId);


    List<UserBaseInfoDo> selectListByAccountId(Long accountId);

    List<UserBaseInfoDo> selectListByAccountIds(List<Long> accountIds,boolean tenantFiltered);

    UserBaseInfoDo selectByUserId(Long userId);

    List<UserBaseInfoDo> getByUserIds(List<Long> userIds);

    UserBaseInfoDo getUserInfoByAccount(Long corpId, String account);

    IPage<UserBaseInfoDo> page(String keywords, int pageNo, int pageSize,Boolean onBoarDing);

    IPage<UserBaseInfoDo> page(UserBaseInfoQueryDto dto);

    Long selectUserId(UserBaseInfoDo userBaseInfoDo);

    List<UserBaseInfoDo> listByEmpId(Long empId);

    List<UserBaseInfoDo> listByEmpId(Long empId, Long tenantId);

    List<UserBaseInfoDo> listByEmpIds(List<Long> empIds);

    int updateBatch(List<UserBaseInfoDo> list);

    int softDeleteByEmpId(String empId);

    List<UserBaseInfoDo> listByEmpIdAndOnboarding(Long empId,Boolean onboarding);

    List<UserBaseInfoDo> selectListByWorkNo(String tenantId, String workNo);
}
