package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * saml登录统一处理逻辑
 * created by: FoAng
 * create time: 7/12/2022 3:37 下午
 */
@Slf4j
@Service
public class SamlLoginService implements IAccountLoginService{

    @Value("${saml.login.service:saml.byWorkNo}")
    private String ssoType;

    @Override
    public GrantType getGrantType() {
        return GrantType.SAML_TOKEN_GRANT_TYPE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        PreCheck.preCheckArgument(StringUtil.isEmpty(ssoType), "saml配置sso参数错误，请检查");
        log.info("saml sso login type：{}", ssoType);
        Map<String, List<String>> samlProps = loginDto.getSamlProps();
        PreCheck.preCheckArgument(samlProps == null || samlProps.isEmpty(), "saml参数错误，请检查");
        return IAccountLoginService.getInstance(GrantType.SSO_TOKEN_GRANT_TYPE, ssoType).checkAndGetUser(loginDto);
    }
}
