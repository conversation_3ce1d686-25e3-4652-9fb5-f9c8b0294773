package com.caidaocloud.user.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.user.service.domain.entity.PrivateEnvironment;
import com.caidaocloud.user.service.domain.enums.PrivateEnvironmentType;
import com.caidaocloud.user.service.domain.repository.IPrivateEnvironmentRepository;
import com.caidaocloud.user.service.infrastructure.repository.mapper.PrivateEnvironmentMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.PrivateEnvironmentPo;
import com.caidaocloud.util.FastjsonUtil;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class PrivateEnvironmentRepositoryImpl implements IPrivateEnvironmentRepository {

    @Autowired
    private PrivateEnvironmentMapper mapper;

    @Override
    public List<PrivateEnvironment> list(PrivateEnvironmentType type) {
        val wrapper = new QueryWrapper<PrivateEnvironmentPo>().lambda().eq(PrivateEnvironmentPo::getType, PrivateEnvironmentType.class);
        wrapper.ne(PrivateEnvironmentPo::getDeleted, 1);
        val result = mapper.selectList(wrapper);
        return FastjsonUtil.convertList(result, PrivateEnvironment.class);
    }
}
