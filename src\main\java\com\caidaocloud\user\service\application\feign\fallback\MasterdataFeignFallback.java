package com.caidaocloud.user.service.application.feign.fallback;

import java.util.List;

import com.caidaocloud.user.service.application.dto.masterdata.EmpSearchDto;
import com.caidaocloud.user.service.application.dto.masterdata.EmpWorkInfoDto;
import com.caidaocloud.user.service.application.feign.IMasterdataFeign;
import com.caidaocloud.web.Result;

import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @date 2023/5/26
 */
@Component
public class MasterdataFeignFallback implements IMasterdataFeign {
	/**
	 * 获取员工信息
	 * @return
	 * @param dto
	 */
	@Override
	public Result<List<EmpWorkInfoDto>> getEmpInfoByEmpIds(EmpSearchDto dto) {
		return Result.fail();
	}

	@Override
	public Result<List<EmpWorkInfoDto>> listByWorknoAndEmail(EmpSearchDto dto) {
		return Result.fail();
	}

	@Override
	public Result<EmpWorkInfoDto> loadEmpInfoByWorkno(String workno, Long datetime) {
		return null;
	}
}
