package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class EmpIdAutoLoginService implements IAccountLoginService{

    @Value("${sso.workNo.login.default.tenant:}")
    private String workNoDefaultTenant;
    @Value("${saml.login.samlPopKey:workerId}")
    private String samlPopKey;

    @Resource
    private UserBaseInfoService userBaseInfoService;

    public String getSsoServiceKey(){
        return "saml.byEmpId";
    }

    @Override
    public GrantType getGrantType() {
        return GrantType.SSO_TOKEN_GRANT_TYPE;
    }

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        String account = loginDto.getAccount();
        if(StringUtil.isEmpty(account)){
            Map<String, List<String>> samlProps = loginDto.getSamlProps();
            PreCheck.preCheckArgument(StringUtil.isEmpty(samlPopKey) || !samlProps.containsKey(samlPopKey), "未获取到samlPopKey参数，请检查");
            account = samlProps.get(samlPopKey).stream().findFirst().orElse(null);
        }

        if(StringUtil.isEmpty(account)){
            log.warn("{} is empty,login failed. loginDto={}", samlPopKey, FastjsonUtil.toJson(loginDto));
            throw new ServerException("login failed");
        }

        val userInfo = new SecurityUserInfo();
        userInfo.setTenantId(workNoDefaultTenant);
        SecurityUserUtil.setSecurityUserInfo(userInfo);

        try{
            val empId = Long.valueOf(account);
            List<UserBaseInfoDo> list = userBaseInfoService.listByEmpId(empId);
            return list;
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }
}
