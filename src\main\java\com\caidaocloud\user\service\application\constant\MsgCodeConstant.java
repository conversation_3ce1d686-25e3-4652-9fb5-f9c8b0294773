package com.caidaocloud.user.service.application.constant;

public class MsgCodeConstant {

    public final static int UNKNOWN_EXCEPTION = 60000;
    /**
     * 账号或密码错误
     */
    public final static int WRONG_ACCOUNT_OR_PASSWORD = 10000;

    /**
     * 账号已停用，请联系管理员
     */
    public final static int ACCOUNT_DISABLED = 10001;
    /**
     * 账号已锁定，请联系管理员
     */
    public final static int ACCOUNT_LOCKED = 10002;

    /**
     * 用户导入必填字段为空
     */
    public final static int IMPORT_MUST_FEILD_NULL = 60003;

    /**
     * 用户导入邮箱匹配上了 手机没匹配上
     */
    public final static int IMPORT_ERROR_EMAIL_MOBILE = 60004;

    /**
     * 用户导入未匹配到员工
     */
    public final static int IMPORT_ERROR_NOT_FOUND_EMP = 60006;

    /**
     * masterdata数据返回为空
     */
    public final static int MASTER_DATA_ERROR = 60007;

    public final static int REQUEST_AUTH_ERROR = 60008;

    /**
     * 授权失败
     */
    public final static int AUTHORIZATION_FAIL = 10009;

    // 账号已停用或已锁定，请联系管理员
    public final static int ACCOUNT_LOCKED_OR_DISABLED = 60009;

    // 手机号错误
    public final static int MOBILE_ERROR = 60010;

    // 验证码错误
    public final static int SMS_CODE_ERROR = 60011;

    // 一键登录失败
    public final static int ONE_CLICK_LOGIN_FAIL = 60012;

    // 未注册账号
    public final static int UNREGISTERED_ACCOUNT = 60013;

    // 本机号码校验认证失败
    public final static int LOCAL_NUMBER_VERIFY_LOGIN_FAIL = 60014;

    // 凭据不合法
    public final static int ILLEGAL_CREDENTIALS = 60015;

    // 用户不存在
    public final static int USER_NOT_EXIST = 60016;


    // 登录凭据为空
    public final static int LOGIN_CREDENTIALS_EMPTY = 60017;

    // 手机格式错误
    public final static int MOBILE_PHONE_FORMAT_ERROR = 60018;

    // 发送成功
    public final static int SEND_SUCCESS = 60019;

    // 发送失败
    public final static int SEND_FAIL = 60020;

    // 验证码不允许为空
    public final static int VERIFICATION_CODE_CANNOT_EMPTY = 60021;

    // 用户名或密码为空
    public final static int USERNAME_OR_PWD_EMPTY = 60022;

    // 请输入原密码
    public final static int PLEASE_ENTER_OLD_PASSWORD = 60023;

    // 请输入新密码
    public final static int PLEASE_ENTER_NEW_PASSWORD = 60024;

    // 登录失效
    public final static int LOGIN_FAILURE = 60025;

    // 原密码错误
    public final static int ORIGINAL_PASSWORD_ERROR = 60026;

    // 手机号未注册
    public final static int MOBILE_NUMBER_NOT_REGISTERED = 60027;

    // 账号不存在
    public final static int ACCOUNT_NOT_EXIST = 60028;

    // 用户账号已停用或已锁定，请联系管理员
    public final static int USER_LOCKED_OR_DISABLED = 60029;

    // 你已输错%s次，%s次后账号即将被锁定
    public final static int ERR_MSG_10002 = 60030;

    public final static int VERIFY_CODE_TOO_FREQUENTLY = 60031;

    // 验证码错误或已失效
    public final static int VERIFY_CODE_ERROR_INVALID = 60032;

    // 用户已存在
    public final static int USER_EXISTED = 60033;

    // 请输入新手势密码
    public final static int PLEASE_ENTER_NEW_GESTURE = 60034;

    // 邮箱格式错误
    public final static int EMAIL_FORMAT_ERROR = 60035;
}
