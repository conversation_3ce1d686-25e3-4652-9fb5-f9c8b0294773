package com.caidaocloud.user.service.application.service.impl;

import cn.hutool.extra.mail.MailUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.enums.SmsCodeType;
import com.caidaocloud.user.service.application.feign.IMsgFeignClient;
import com.caidaocloud.user.service.application.service.IEmailService;
import com.caidaocloud.user.service.application.service.ISmsCodeCacheService;
import com.caidaocloud.user.service.infrastructure.util.UserContext;
import com.caidaocloud.user.service.interfaces.dto.EmailCodeDto;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class EmailService implements IEmailService {
    @Autowired
    private ISmsCodeCacheService smsCodeCacheService;
    @NacosValue("${caidaocloud.sms.fixedVerifyCode:}")
    private String fixedVerifyCode;
    @Resource
    private IMsgFeignClient msgFeignClient;


    @Override
    public boolean sendMessageCode(String email) {
        // 随机生成验证码
        String code = smsCodeCacheService.generateSmsCode();
        UserInfo userInfo = UserContext.preCheckUser();
        Map<String, String> map = new HashMap<>();
        map.put("to", email);
        map.put("subject", "验证码");
        map.put("content", String.format("您的登录验证码为 %s，该验证码5分钟内有效，请勿泄露于他人。", code));
        map.put("tenantId", userInfo.getTenantId());
        Result result = msgFeignClient.sendEmail(map);
        if (result.isSuccess()) {
            smsCodeCacheService.saveSmsCode(email, code, 60 * 5, SmsCodeType.RESET_PASSWORD);
        }
        return true;
    }


    @Override
    public boolean verify(EmailCodeDto dto) {
        if (StringUtils.isNotEmpty(fixedVerifyCode)) {
            // 如果开启了固定验证码模式，则直接校验
            return fixedVerifyCode.equals(dto.getVerifyCode());
        }
        String emailCode = smsCodeCacheService.getSmsCode(dto.getEmail(), SmsCodeType.RESET_PASSWORD);
        if (StringUtils.isEmpty(emailCode)) {
            throw new ServerException("验证码已过期，请重新获取验证码");
        }
        return emailCode.equals(dto.getVerifyCode());
    }

}
