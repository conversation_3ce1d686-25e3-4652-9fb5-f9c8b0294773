package com.caidaocloud.user.service.infrastructure.repository;

import com.caidaocloud.user.service.infrastructure.repository.mongo.MongodbDao;
import com.caidaocloud.user.service.infrastructure.repository.po.TenantPo;
import com.caidaocloud.user.service.domain.repository.ITenantRepository;
import com.mongodb.BasicDBObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/8/2021 1:39 PM
 * 4
 */
@Repository
public class TenantRepositoryImpl implements ITenantRepository {
    private static final String collectionName = "basic_e_tenant";
    @Autowired
    private MongodbDao<TenantPo> mongodbDao;

    @Override
    public Class<TenantPo> getPoClass() {
        return TenantPo.class;
    }

    @Override
    public String getCollectionNamePrefix() {
        return TenantRepositoryImpl.collectionName;
    }

    @Override
    public MongodbDao<TenantPo> getMongodbDao() {
        return mongodbDao;
    }

    @Override
    public TenantPo getTenantByCorpid(Long corpid) {
        BasicDBObject query = new BasicDBObject();
        query.put("corpid", corpid);
        return getTenantPoByQuery(query);
    }

    @Override
    public TenantPo getTenantByThirdPart(String thirdPart) {
        BasicDBObject query = new BasicDBObject();
        query.put("thirdPart", thirdPart);
        return getTenantPoByQuery(query);
    }

    private TenantPo getTenantPoByQuery(BasicDBObject query){
        List<TenantPo> entities = query(query, "");
        if (CollectionUtils.isEmpty(entities)) {
            return null;
        }

        return getOneByList(entities);
    }
}
