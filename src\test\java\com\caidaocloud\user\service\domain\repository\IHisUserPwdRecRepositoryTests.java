package com.caidaocloud.user.service.domain.repository;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.domain.entity.HisUserPwdRecDo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/5/8
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class IHisUserPwdRecRepositoryTests {


	@Autowired
	private IHisUserPwdRecRepository repository;

	@Before
	public void setUp() throws Exception {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("8");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		RequestHelper.getRequest().setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(userInfo, UserInfo.class));
		SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
		try {
			Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
			field.setAccessible(true);
			field.set(service, true);
		}
		catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

@Test
public void testSyncSaveWithValidInputShouldSaveData() throws Exception {
    // given
    List<HisUserPwdRecDo> dataList = Arrays.asList(
            createHisUserPwdRecDo("password1", "salt1"),
            createHisUserPwdRecDo("password2", "salt2"),
            createHisUserPwdRecDo("password3", "salt3")
    );
    repository.insertBatch(dataList);
    for (HisUserPwdRecDo data : dataList) {
        // when
		List<HisUserPwdRecDo> result = repository.getListByAccountId(Long.valueOf(SecurityUserUtil.getSecurityUserInfo()
				.getTenantId()), data.getAccountId());

        // then
        assertEquals(1, result.size());
        assertEquals(data.getUserId(), result.get(0).getUserId());
        assertEquals(Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()), result.get(0).getTenantId());
        assertEquals(data.getPassword(), result.get(0).getPassword());
        assertEquals(data.getSalt(), result.get(0).getSalt());
    }
}

private HisUserPwdRecDo createHisUserPwdRecDo(String password, String salt) {
	HisUserPwdRecDo testData = new HisUserPwdRecDo();
	testData.setRecId(snowflakeUtil.createId());
	testData.setAccountId(snowflakeUtil.createId());
	testData.setUserId(snowflakeUtil.createId());
	testData.setTenantId(Long.valueOf(SecurityUserUtil.getSecurityUserInfo().getTenantId()));
	testData.setPassword(password);
	testData.setSalt(salt);

	// get the current security user info
	SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
	long timestamp = System.currentTimeMillis();
	testData.setCreateBy(userInfo.getUserId());
	testData.setUpdateBy(userInfo.getUserId());
	testData.setCreateTime(timestamp);
	testData.setUpdateTime(timestamp);
	testData.setDeleted(0);
	return testData;
}

}