package com.caidaocloud.user.service.application.utils.mxySso;//

import org.apache.commons.codec.digest.DigestUtils;
import java.io.InputStream;
import java.math.BigInteger;
import java.security.MessageDigest;

public final class MxySignUtil {
    private MxySignUtil() {
    }

    public static String md5(String input) {
        return DigestUtils.md5Hex(input);
    }

    public static String sha256(String input) {
        return DigestUtils.sha256Hex(input);
    }

    public static String md5(InputStream in) throws RuntimeException {
        MessageDigest md5 = null;
        byte[] buffer = new byte[1024];

        try {
            md5 = MessageDigest.getInstance("MD5");

            int len;
            while((len = in.read(buffer, 0, 1024)) != -1) {
                md5.update(buffer, 0, len);
            }
        } catch (Exception var4) {
            throw new RuntimeException("sign error");
        }

        BigInteger bigInt = new BigInteger(1, md5.digest());
        return bigInt.toString(16);
    }

    public static String md5(byte[] bytes) throws RuntimeException {
        MessageDigest md5 = null;

        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (Exception var3) {
            throw new RuntimeException("sign md5 error");
        }

        BigInteger bigInt = new BigInteger(1, md5.digest(bytes));
        return bigInt.toString(16);
    }
}
