package com.caidaocloud.user.service.interfaces.granter;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum GrantType {
    PASSWORD_TOKEN_GRANT_TYPE(0),
    MOBILE_CODE_TOKEN_GRANT_TYPE(1),
    WX_SCAN_CODE_TOKEN_GRANT_TYPE(2),
    ALIPAY_SCAN_CODE_TOKEN_GRANT_TYPE(3),
    SSO_TOKEN_GRANT_TYPE(4),
    SAML_TOKEN_GRANT_TYPE(5),
    CAIDAO1_COOKIE(6),
    ONE_CLICK_LOGIN_TYPE(7),
    LOCAL_NUMBER_VERIFICATION_TYPE(8),
    EMAIL_TOKEN_GRANT_TYPE(8),
    DING_TALK_GRANT_TYPE(10),
    OPEN_CLOUD_GRANT_TYPE(11),
    PASSWORD_PAYSLIP_GRANT_TYPE(12),
    GESTURE_PAYSLIP_GRANT_TYPE(13);

    final int type;

    GrantType(int type) {
        this.type = type;
    }

    public static GrantType valueByType(Integer typeValue) {
        return Arrays.stream(GrantType.values()).filter(it -> it.getType() == typeValue)
                .findFirst().orElseThrow(() -> new RuntimeException("unResolve grantType"));
    }


}
