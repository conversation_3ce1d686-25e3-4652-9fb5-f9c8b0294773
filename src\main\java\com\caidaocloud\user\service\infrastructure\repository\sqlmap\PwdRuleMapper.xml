<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.user.service.infrastructure.repository.mapper.PwdRuleMapper">
    <insert id="insertSelective" parameterType="com.caidaocloud.user.service.infrastructure.repository.po.PwdRule">
        insert into pwd_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pwdRuleId != null">
                pwd_rule_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="isFirstRule != null">
                is_first_rule,
            </if>
            <if test="firstRule != null">
                first_rule,
            </if>
            <if test="firstLength != null">
                first_length,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
            <if test="isPwdChange != null">
                is_pwd_change,
            </if>
            <if test="pwdLen1 != null">
                pwd_len1,
            </if>
            <if test="pwdLen2 != null">
                pwd_len2,
            </if>
            <if test="pwdComplexity != null">
                pwd_complexity,
            </if>
            <if test="notPwdSameNum != null">
                not_pwd_same_num,
            </if>
            <if test="pwdValidTime != null">
                pwd_valid_time,
            </if>
            <if test="pwdExpiresDay != null">
                pwd_expires_day,
            </if>
            <if test="lockAccountNum != null">
                lock_account_num,
            </if>
            <if test="lockAccountTime != null">
                lock_account_time,
            </if>
            <if test="autoUnlockTime != null">
                auto_unlock_time,
            </if>
            <if test="corpId != null">
                corp_id,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pwdRuleId != null">
                #{pwdRuleId,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="isFirstRule != null">
                #{isFirstRule,jdbcType=BIT},
            </if>
            <if test="firstRule != null">
                #{firstRule,jdbcType=VARCHAR},
            </if>
            <if test="firstLength != null">
                #{firstLength,jdbcType=INTEGER},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=BIT},
            </if>
            <if test="isPwdChange != null">
                #{isPwdChange,jdbcType=BIT},
            </if>
            <if test="pwdLen1 != null">
                #{pwdLen1,jdbcType=INTEGER},
            </if>
            <if test="pwdLen2 != null">
                #{pwdLen2,jdbcType=INTEGER},
            </if>
            <if test="pwdComplexity != null">
                #{pwdComplexity,jdbcType=VARCHAR},
            </if>
            <if test="notPwdSameNum != null">
                #{notPwdSameNum,jdbcType=INTEGER},
            </if>
            <if test="pwdValidTime != null">
                #{pwdValidTime,jdbcType=INTEGER},
            </if>
            <if test="pwdExpiresDay != null">
                #{pwdExpiresDay,jdbcType=INTEGER},
            </if>
            <if test="lockAccountNum != null">
                #{lockAccountNum,jdbcType=INTEGER},
            </if>
            <if test="lockAccountTime != null">
                #{lockAccountTime,jdbcType=INTEGER},
            </if>
            <if test="autoUnlockTime != null">
                #{autoUnlockTime,jdbcType=INTEGER},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=BIGINT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.caidaocloud.user.service.infrastructure.repository.po.PwdRule">
        update pwd_rule
        <set>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="isFirstRule != null">
                is_first_rule = #{isFirstRule,jdbcType=BIT},
            </if>
            <if test="firstRule != null">
                first_rule = #{firstRule,jdbcType=VARCHAR},
            </if>
            <if test="firstLength != null">
                first_length = #{firstLength,jdbcType=INTEGER},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=BIT},
            </if>
            <if test="isPwdChange != null">
                is_pwd_change = #{isPwdChange,jdbcType=BIT},
            </if>
            <if test="pwdLen1 != null">
                pwd_len1 = #{pwdLen1,jdbcType=INTEGER},
            </if>
            <if test="pwdLen2 != null">
                pwd_len2 = #{pwdLen2,jdbcType=INTEGER},
            </if>
            <if test="pwdComplexity != null">
                pwd_complexity = #{pwdComplexity,jdbcType=VARCHAR},
            </if>
            <if test="notPwdSameNum != null">
                not_pwd_same_num = #{notPwdSameNum,jdbcType=INTEGER},
            </if>
            <if test="pwdValidTime != null">
                pwd_valid_time = #{pwdValidTime,jdbcType=INTEGER},
            </if>
            <if test="pwdExpiresDay != null">
                pwd_expires_day = #{pwdExpiresDay,jdbcType=INTEGER},
            </if>
            <if test="lockAccountNum != null">
                lock_account_num = #{lockAccountNum,jdbcType=INTEGER},
            </if>
            <if test="lockAccountTime != null">
                lock_account_time = #{lockAccountTime,jdbcType=INTEGER},
            </if>
            <if test="autoUnlockTime != null">
                auto_unlock_time = #{autoUnlockTime,jdbcType=INTEGER},
            </if>
            <if test="corpId != null">
                corp_id = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
        </set>
        where pwd_rule_id = #{pwdRuleId,jdbcType=BIGINT}
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into pwd_rule (pwd_rule_id, tenant_id, is_first_rule,
        first_rule, first_length, is_valid,
        is_pwd_change, pwd_len1, pwd_len2,
        pwd_complexity, not_pwd_same_num, pwd_valid_time,
        pwd_expires_day, lock_account_num, lock_account_time,
        auto_unlock_time, corp_id, create_by,
        create_time, update_by, update_time,
        deleted)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.pwdRuleId,jdbcType=BIGINT}, #{record.tenantId,jdbcType=BIGINT},
            #{record.isFirstRule,jdbcType=BIT},
            #{record.firstRule,jdbcType=VARCHAR}, #{record.firstLength,jdbcType=INTEGER},
            #{record.isValid,jdbcType=BIT},
            #{record.isPwdChange,jdbcType=BIT}, #{record.pwdLen1,jdbcType=INTEGER}, #{record.pwdLen2,jdbcType=INTEGER},
            #{record.pwdComplexity,jdbcType=VARCHAR}, #{record.notPwdSameNum,jdbcType=INTEGER},
            #{record.pwdValidTime,jdbcType=INTEGER},
            #{record.pwdExpiresDay,jdbcType=INTEGER}, #{record.lockAccountNum,jdbcType=INTEGER},
            #{record.lockAccountTime,jdbcType=INTEGER},
            #{record.autoUnlockTime,jdbcType=INTEGER}, #{record.corpId,jdbcType=BIGINT},
            #{record.createBy,jdbcType=BIGINT},
            #{record.createTime,jdbcType=BIGINT}, #{record.updateBy,jdbcType=BIGINT},
            #{record.updateTime,jdbcType=BIGINT},
            #{record.deleted,jdbcType=INTEGER})
        </foreach>
    </insert>
</mapper>