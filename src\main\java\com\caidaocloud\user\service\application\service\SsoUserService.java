package com.caidaocloud.user.service.application.service;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.security.dto.LoginPlatform;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.user.service.domain.entity.LoginDo;
import com.caidaocloud.user.service.domain.entity.Tenant;
import com.caidaocloud.user.service.domain.entity.User;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.user.service.interfaces.dto.SimpleEmpDto;
import com.caidaocloud.user.service.interfaces.dto.UserDto;
import com.caidaocloud.user.service.application.feign.IBaseInfoClient;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SignUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-01-13
 */
@Slf4j
@Service
public class SsoUserService {
    @Autowired
    private User user;
    @Autowired
    private IBaseInfoClient orgPerClient;
    @Autowired
    private LoginDomainService loginDomainService;
    @Autowired
    private Tenant tenant;
    @Autowired
    private UserLoginService userLoginService;

    public LoginOutDto login(LoginDto loginDto) {
        String thirdId = StringUtil.isEmpty(loginDto.getThirdId()) ? "_" + loginDto.getExtMap().get("globalid")+ ":" + loginDto.getThirdPart() : loginDto.getThirdId() + ":" + loginDto.getThirdPart();
        User dbUser = user.getByTenantIdAndStaffid(loginDto.getThirdPart(), thirdId);
        if(null == dbUser){
            log.info("mongodb no this user,{}", JSON.toJSONString(loginDto));

            Tenant tenantDo = tenant.getTenantByThirdPart((String) loginDto.getExtMap().get("corpcode"));
            if (null != tenantDo && StringUtil.isNotEmpty(tenantDo.getLoginConfig())){
                loginDto.setLoginPlatform(LoginPlatform.THIRD_WEB.getValue());
                dbUser = new User();
                dbUser.setStaffid(thirdId);
                return userLoginService.autoLogin(tenantDo.getLoginConfig(), loginDto, dbUser);
            }
        }

        LoginOutDto loginOutDto = new LoginOutDto();
        if(null == dbUser){
            loginOutDto.setErrorCode(ErrorCodes.UNKNOW_ERROR);
            return loginOutDto;
        }

        int type = null == loginDto.getLoginPlatform() ? 2 : loginDto.getLoginPlatform().intValue();
        String tokens = TokenGenerator.getToken(String.valueOf(dbUser.getUserid()), dbUser.getTenantId(), type);
        loginOutDto.setToken(tokens);
        loginOutDto.setUserId(String.valueOf(dbUser.getUserid()));
        loginOutDto.setUserName(dbUser.getEmpname());
        loginOutDto.setErrorCode(0);
        // 缓存用户信息
        UserInfo userInfo = ObjectConverter.convert(dbUser, UserInfo.class);
        userInfo.setExtMap(loginDto.getExtMap());

        LoginDo loginDo = ObjectConverter.convert(loginDto, LoginDo.class);
        loginDomainService.sessionRefresh(loginDo, userInfo);
        return loginOutDto;
    }

    private User getUserByLoginDto(LoginDto loginDto){
        // thirdEmpId 采用 staffId + ":" + corpKey
        String thirdEmpId = loginDto.getThirdId() + ":" + loginDto.getThirdPart();
        Result<SimpleEmpDto> result = orgPerClient.getMasterDataEmp(thirdEmpId, "TENCENT");
        log.info("feign result=[{}]", JSON.toJSON(result));
        SimpleEmpDto simpleEmpDto = null;
        if(null == result || null == (simpleEmpDto = result.getData())){
            return null;
        }

        String empName = String.format("%s(%s)", simpleEmpDto.getEngName(), simpleEmpDto.getEmpName());
        UserDto userDto = new UserDto();
        userDto.setAccount("t_wx_" + loginDto.getThirdPart() + "_" + loginDto.getThirdId())
                .setStaffid(loginDto.getThirdId()).setTenantId(loginDto.getThirdPart()).setStatus(1)
                .setThirdId(thirdEmpId).setThirdPart("TENCENT")
                .setEmpid(simpleEmpDto.getEmpid()).setCorpid(simpleEmpDto.getCorpid())
                .setBelongOrgId(simpleEmpDto.getBelongOrgId()).setEmpname(empName)
                .setEmail(simpleEmpDto.getEmail()).setMobnum(simpleEmpDto.getMobile()).setIssuperadmin(false)
                .setCrtuser(0).setCrttime(System.currentTimeMillis() / 1000)
            .setPasswd(SignUtil.md5("wx2021"));

        Map extMap = loginDto.getExtMap();
        if(null != extMap){
            userDto.setGlobalid(loginDto.getExtMap().get("globalid").toString());
        }

        Result<UserInfo> userResult = orgPerClient.saveMasterDataUser(userDto);
        log.info("feign userResult=[{}]", JSON.toJSON(userResult));

        UserInfo userInfo = null;
        if(null == (userInfo = userResult.getData())){
            return null;
        }

        Integer userId = userInfo.getUserid();
        if(null == userId){
            return null;
        }

        userDto.setUserid(userId);
        User newUser = ObjectConverter.convert(userDto, User.class);
        List<User> saveList = new ArrayList<>();
        saveList.add(newUser);
        user.save(saveList, userDto.getTenantId());
        return newUser;
    }
}
