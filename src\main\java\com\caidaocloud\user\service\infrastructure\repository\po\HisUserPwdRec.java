package com.caidaocloud.user.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.user.service.domain.entity.AbstractBaseEntity;
import lombok.Data;

/**
 * 用户密码历史表
 */
@Data
@TableName("his_user_pwd_rec")
public class HisUserPwdRec extends AbstractBaseEntity {
    /**
     * 修改记录ID
     */
    @TableId
    private Long recId;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐值
     */
    private String salt;
}
