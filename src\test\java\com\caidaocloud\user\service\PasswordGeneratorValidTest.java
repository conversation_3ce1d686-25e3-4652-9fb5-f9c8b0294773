package com.caidaocloud.user.service;

import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import lombok.SneakyThrows;
import org.junit.*;
import static org.junit.Assert.*;

public class PasswordGeneratorValidTest {

    @Test
    public void shouldReturnFalseForPasswordWithLessThanSixCharacters() {
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("Pa1!"));
    }
    @Test
    public void shouldReturnFalseForPasswordWithCNCharacters2() {
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("中Pa1!@"));
    }

    @Test
    public void shouldReturnFalseForPasswordWithoutUppercaseLetter() {
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("password123!@#"));
    }

    @Test
    public void shouldReturnFalseForPasswordWithoutLowercaseLetter() {
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("PASSWORD123!@#"));
    }

    @Test
    public void shouldReturnFalseForPasswordWithoutDigit() {
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("Password!@#$%"));
    }

    @Test
    public void shouldReturnFalseForPasswordWithoutSpecialCharacter() {
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("Password123456"));
    }

    @Test
    public void shouldReturnTrueForValidPassword() {
        assertTrue(PasswordHelper.PasswordGenerator.validatePassword("Password123!@#"));
    }

    @Test
    public void shouldReturnFalseForEmptyPassword() {
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword(""));
    }

    @Test
    public void shouldReturnFalseForNullPassword() {
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword(null));
    }

    @Test
    public void shouldHandlePasswordJustAboveMinimumLength() {
        assertTrue(PasswordHelper.PasswordGenerator.validatePassword("Password1!@#"));
    }

    @Test
    public void shouldReturnFalseWhenPasswordContainsOnlyCharactersFromOneGroup() {
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("aaaaaaaaaaaaa"));
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("111111111111111"));
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("!!!!!!!!!!!!!!!!!!"));
        assertFalse(PasswordHelper.PasswordGenerator.validatePassword("____________________"));
    }

    @Test
    public void shouldReturnFalseForNullPassword2() {
        assertFalse("Password validation should return false for null password",
                PasswordHelper.PasswordGenerator.validateSimplePassword(null));
    }

    @Test
    public void shouldReturnFalseForEmptyPassword2() {
        assertFalse("Password validation should return false for empty password",
                PasswordHelper.PasswordGenerator.validateSimplePassword(""));
    }

    @Test
    public void shouldReturnTrueForValidPassword2() {
        assertTrue("Password validation should return true for valid password",
                PasswordHelper.PasswordGenerator.validateSimplePassword("AbCdEf12345?"));
        assertTrue("Password validation should return true for valid password",
                PasswordHelper.PasswordGenerator.validateSimplePassword("123456"));
        assertTrue("Password validation should return true for valid password",
                PasswordHelper.PasswordGenerator.validateSimplePassword("asdfgh"));
        assertTrue("Password validation should return true for valid password",
                PasswordHelper.PasswordGenerator.validateSimplePassword("!@#$%^"));
        assertTrue("Password validation should return true for valid password",
                PasswordHelper.PasswordGenerator.validateSimplePassword("ASDFGH"));
    }

    // 注意：具体的密码验证规则（如必须包含大写字母、小写字母、数字和特殊字符等）未在原始代码中明确，
    // 因此以下示例是基于常见密码复杂度要求编写的测试案例。根据实际情况调整。

    @Test
    public void shouldReturnFalseForPasswordWithLessThanSixCharacters2() {
        assertFalse("Password validation should return false for password with less than six characters",
                PasswordHelper.PasswordGenerator.validateSimplePassword("12345"));
    }

    @SneakyThrows
    @Test
    public void shouldReturnFalseForPasswordWithCNCharacters() {
        assertFalse("Password validation should return false for password with illegal characters ",
                PasswordHelper.PasswordGenerator.validateSimplePassword("姜11111"));

    }
}
