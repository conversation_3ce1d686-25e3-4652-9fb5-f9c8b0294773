# 用户登录日志功能说明

## 功能概述

本次实现了完整的用户登录日志记录功能，可以记录用户的登录时间、IP地址、设备信息等，并支持后续扩展为登录设备、地理位置等其他信息。

## 主要功能

1. **登录成功日志记录**：记录用户成功登录的详细信息
2. **登录失败日志记录**：记录用户登录失败的原因和相关信息
3. **异步处理**：使用线程池异步记录日志，不影响登录性能
4. **多存储支持**：支持MongoDB存储，同时提供MySQL DDL脚本
5. **IP地址提取**：自动从HTTP请求中提取真实IP地址（支持代理）
6. **User-Agent解析**：记录用户浏览器和设备信息

## 技术架构

### 1. 领域层 (Domain Layer)

#### 实体类
- **LoginLogDo**: 登录日志领域实体，包含业务逻辑和工厂方法
- **LoginLogInfo**: MongoDB持久化对象，使用Morphia注解

#### 仓储接口
- **ILoginLogRepository**: 登录日志仓储接口，定义数据访问方法

#### 领域服务
- **LoginLogDomainService**: 登录日志领域服务，提供异步记录功能

### 2. 基础设施层 (Infrastructure Layer)

#### 仓储实现
- **LoginLogRepositoryImpl**: MongoDB仓储实现，使用MongodbDao

### 3. 应用层 (Application Layer)

#### 应用服务
- **UserAppService**: 用户应用服务，集成登录日志记录
- **LoginDomainService**: 登录领域服务，支持IP和User-Agent参数

### 4. 接口层 (Interface Layer)

#### 控制器
- **UserController**: 用户控制器，修改登录接口支持请求信息提取

#### Token授权器
- **ITokenGranter**: Token授权器接口，新增支持HttpServletRequest的方法
- **PasswordTokenGranter**: 密码登录授权器实现

## 数据库设计

## 存储方案

### 数据库存储（当前实现）
- **表名**: `user_login_log`
- **存储引擎**: 支持MySQL和PostgreSQL
- **ORM框架**: MyBatis Plus
- **索引**: 用户ID、租户ID、登录时间、IP地址
- **数据格式**: 关系型数据库表结构

### MongoDB集合：login_log_info（备用方案）

```javascript
{
  "_id": "登录日志ID",
  "userId": "用户ID",
  "tenantId": "租户ID", 
  "account": "用户账号",
  "userName": "用户名",
  "loginTime": "登录时间",
  "loginPlatform": "登录平台：0为web端，1为移动端, 2 第三方为web端，3 为第三方移动端",
  "loginType": "登录方式：0为账号密码登录，1:为手机验证码登录,2为微信扫描登录，3为支付宝扫码登录，4为腾讯sso登录",
  "deviceType": "登录设备类型：web:0 android:1,ios:2",
  "ipAddress": "登录IP地址",
  "userAgent": "用户代理信息（浏览器信息）",
  "loginStatus": "登录状态：1成功，0失败",
  "failReason": "登录失败原因",
  "sessionId": "会话ID",
  "extInfo": "扩展信息",
  "createTime": "创建时间"
}
```

### MySQL表：user_login_log

提供了MySQL和PostgreSQL的DDL脚本：
- `src/main/resources/db/script/mysql/ddl/2025/1/V20250110_01__用户登录日志表初始化脚本-DDL.sql`
- `src/main/resources/db/script/pg/ddl/2025/1/V20250110_01__用户登录日志表初始化脚本-DDL.sql`

## 核心功能实现

### 1. 登录成功日志记录

```java
// 在登录成功后自动记录
loginLogDomainService.recordSuccessLoginAsync(
    userId, tenantId, account, userName, 
    loginPlatform, loginType, deviceType,
    ipAddress, userAgent, sessionId
);
```

### 2. 登录失败日志记录

```java
// 在登录失败时自动记录
loginLogDomainService.recordFailLoginAsync(
    account, tenantId, 
    loginPlatform, loginType, deviceType,
    ipAddress, userAgent, failReason
);
```

### 3. IP地址提取

支持从以下HTTP头中提取真实IP地址：
- X-Forwarded-For
- X-Real-IP
- Proxy-Client-IP
- WL-Proxy-Client-IP
- HTTP_CLIENT_IP
- HTTP_X_FORWARDED_FOR

### 4. 异步处理

使用现有的ThreadPoolExecutor进行异步处理，确保登录日志记录不影响登录性能。

## 接口变更

### 1. 登录接口

```java
@RequestMapping(value = "/v1/login", method = RequestMethod.POST)
public Result<LoginVo> login(@RequestBody LoginDto loginDto, HttpServletRequest request)
```

新增了`HttpServletRequest request`参数，用于提取IP地址和User-Agent信息。

### 2. Token授权器接口

```java
public interface ITokenGranter {
    Result grant(LoginDto loginDto);
    
    // 新增方法，支持HttpServletRequest
    default Result grant(LoginDto loginDto, HttpServletRequest request) {
        return grant(loginDto);
    }
}
```

## 测试

提供了完整的单元测试：
- `src/test/java/com/caidaocloud/user/service/LoginLogTest.java`

测试内容包括：
1. 成功登录日志记录测试
2. 失败登录日志记录测试
3. 登录日志实体创建测试

## 扩展性

该实现具有良好的扩展性，可以轻松添加：

1. **地理位置信息**：基于IP地址解析地理位置
2. **设备指纹**：更详细的设备识别信息
3. **风险评估**：基于登录行为的安全风险评估
4. **统计分析**：登录频次、时间分布等统计功能
5. **告警机制**：异常登录行为告警

## 配置说明

### 线程池配置

登录日志记录使用现有的ThreadPoolExecutor，可以通过以下方式调整：

```java
@Autowired
private ThreadPoolExecutor threadPoolExecutor;
```

### 数据库配置

#### 关系型数据库配置（当前实现）
确保数据库连接配置正确，登录日志将存储在`user_login_log`表中。

需要执行相应的DDL脚本创建表结构：
- MySQL: `src/main/resources/db/script/mysql/ddl/2025/1/V20250110_01__用户登录日志表初始化脚本-DDL.sql`
- PostgreSQL: `src/main/resources/db/script/pg/ddl/2025/1/V20250110_01__用户登录日志表初始化脚本-DDL.sql`

#### MongoDB配置（备用方案）
如需使用MongoDB存储，确保MongoDB连接配置正确，登录日志将存储在`login_log_info`集合中。

## 注意事项

1. **性能影响**：所有日志记录都是异步的，不会影响登录性能
2. **存储空间**：需要定期清理历史登录日志，建议保留3-6个月
3. **隐私保护**：IP地址和User-Agent信息涉及用户隐私，需要符合相关法规
4. **错误处理**：登录日志记录失败不会影响正常登录流程

## 后续优化建议

1. **批量写入**：对于高并发场景，可以考虑批量写入优化
2. **分表分库**：根据租户或时间进行分表存储
3. **缓存优化**：对频繁查询的数据进行缓存
4. **监控告警**：添加登录日志记录的监控和告警机制
