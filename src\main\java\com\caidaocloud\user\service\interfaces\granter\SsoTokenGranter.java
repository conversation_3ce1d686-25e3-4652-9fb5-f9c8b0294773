package com.caidaocloud.user.service.interfaces.granter;

import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.user.service.application.service.SsoUserService;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Sso 模式登录
 * <AUTHOR>
 * @date 2020-01-15
 */
@Component
public class SsoTokenGranter extends AbstractTokenGranter{
    @Autowired
    private SsoUserService ssoUserService;

    @Override
    protected Result beforeGrant(LoginDto loginDto){
        if (StringUtils.isEmpty(loginDto.getThirdPart()) && StringUtils.isEmpty(loginDto.getThirdId())) {
            return ResponseWrap.wrapResult(ErrorCodes.EMPTY_ACCOUNT_OR_PASSWORD, null, null);
        }
        return null;
    }

    @Override
    public Result grant(LoginDto loginDto) {
        Result result = beforeGrant(loginDto);
        if(null != result){
            return result;
        }

        LoginOutDto loginOutDo = ssoUserService.login(loginDto);
        if(null == loginOutDo){
            return ResponseWrap.wrapResult(ErrorCodes.UN_AUTHORIZED, "没有访问权限", null);
        }

        return afterGrant(loginOutDo);
    }
}
