package com.caidaocloud.user.service.interfaces.granter;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.ReceiptLoginDto;
import com.caidaocloud.user.service.interfaces.vo.AccountLoginVo;
import com.caidaocloud.web.Result;

public interface ITokenGranter {

    Result grant(LoginDto loginDto);
}
