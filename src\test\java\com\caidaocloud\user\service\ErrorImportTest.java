package com.caidaocloud.user.service;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.user.service.application.constant.KeyConstant;
import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.user.service.application.utils.MessgeToCacheUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class ErrorImportTest {

    @Autowired
    private CacheService cacheService;

    @Test
    public void importTest() {

        String errorKey = String.format("%s%s", KeyConstant.IMPORT_USER_ERROR_KEY, 1);

        UserImportErrorDto dto = new UserImportErrorDto();
        dto.setErrorMsg("test1");
        dto.setWorkno("test1");

        UserImportErrorDto dto2 = new UserImportErrorDto();
        dto2.setErrorMsg("test2");
        dto2.setWorkno("test2");

        ArrayList<UserImportErrorDto> list = Lists.newArrayList(dto, dto2);
        List<String> strings = FastjsonUtil.convertList(list, String.class);
        MessgeToCacheUtil.setMessageToCache(errorKey, strings, 1800);
        System.out.println("success");
    }

    @Test
    public void createProcess() {
        String processKey = String.format("%s_%s", KeyConstant.IMPORT_USER_PROCESS, 1);
        ImportExcelProcessDto processDto = new ImportExcelProcessDto();
        processDto.setTotal(100);
        processDto.setCompleted(10);
        cacheService.cacheValue(processKey, FastjsonUtil.toJson(processDto), 300);
    }

}
