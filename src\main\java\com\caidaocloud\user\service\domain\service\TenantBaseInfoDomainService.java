
package com.caidaocloud.user.service.domain.service;

import com.caidaocloud.user.service.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.user.service.domain.repository.ITenantBaseInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class TenantBaseInfoDomainService {
    @Autowired
    private TenantBaseInfoDo tenantBaseInfoDo;
    @Autowired
    private ITenantBaseInfoRepository tenantBaseInfoRepository;

    public void syncSave(List<TenantBaseInfoDo> dataList) {
        tenantBaseInfoDo.syncSave(dataList);
    }

    public void deleteByIds(List<Long> tenantIds) {
        tenantBaseInfoDo.deleteByIds(tenantIds);
    }

    public void softDeleteByIds(List<Long> tenantIds) {
        tenantBaseInfoDo.softDeleteByIds(tenantIds);
    }

    public List<TenantBaseInfoDo> getTenantList(List<Long> tenantIds) {
        return tenantBaseInfoDo.getTenantList(tenantIds);
    }

    public List<TenantBaseInfoDo> loadAllTenant() {
        return tenantBaseInfoRepository.getAllTenantList();
    }
}
