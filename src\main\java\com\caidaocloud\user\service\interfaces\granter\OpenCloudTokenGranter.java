package com.caidaocloud.user.service.interfaces.granter;

import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.user.service.application.service.login.IAccountLoginService;
import com.caidaocloud.user.service.application.service.user.UserHandlerManger;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.vo.AccountLoginVo;
import com.caidaocloud.user.service.interfaces.vo.LoginVo;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * open_cloud单点登录实现
 * created by: FoAng
 * create time: 28/8/2023 2:00 下午
 */
@Slf4j
@Component
public class OpenCloudTokenGranter extends AbstractTokenGranter{

    @Override
    protected Result<?> beforeGrant(LoginDto loginDto){
        if (StringUtils.isEmpty(loginDto.getThirdPart()) && StringUtils.isEmpty(loginDto.getThirdId())) {
            return ResponseWrap.wrapResult(ErrorCodes.EMPTY_ACCOUNT_OR_PASSWORD, null, null);
        }
        return null;
    }

    @Override
    public Result<?> grant(LoginDto loginDto) {
        Result<?> result = beforeGrant(loginDto);
        if(null != result) return result;
        AccountLoginDto accountDto = new AccountLoginDto();
        accountDto.setAccount(loginDto.getAccount());
        accountDto.setThirdId(loginDto.getThirdId());
        accountDto.setLoginPlatform(loginDto.getLoginPlatform());
        List<UserBaseInfoDo> baseUsers = IAccountLoginService.getInstance(GrantType.OPEN_CLOUD_GRANT_TYPE)
                .checkAndGetUser(accountDto);
        if (CollectionUtils.isNotEmpty(baseUsers)) {
            log.info("resolve {} info by account:{}, thirdId:{}", baseUsers.size() > 1 ? "multi users" : "single user",
                    accountDto.getAccount(), accountDto.getThirdId());
            UserBaseInfoDo itemUser = baseUsers.get(0);
            int type = loginDto.getLoginPlatform() == null ? 2 : loginDto.getLoginPlatform();
            String accessToken = TokenGenerator.getToken(String.valueOf(itemUser.getUserId()),
                    String.valueOf(itemUser.getTenantId()), type);
            AccountLoginVo accountVo = new AccountLoginVo();
            accountVo.setToken(accessToken);
            accountVo.setUserId(itemUser.getUserId());
            accountVo.setUserName(itemUser.getUserName());
            SpringUtil.getBean(LoginDomainService.class).sessionRefresh(accountDto, itemUser);
            try {
                UserHandlerManger.getDefaultUserHandler().handler(accountDto, accountVo, itemUser);
            } catch (Exception e) {
                log.error("权限加载失败", e);
            }
            LoginVo loginVo = new LoginVo();
            loginVo.setToken(accessToken);
            loginVo.setUserId(String.valueOf(itemUser.getUserId()));
            loginVo.setUserName(itemUser.getUserName());
            return Result.ok(loginVo);
        }
        throw new ServerException("openCloud单点登录失败");
    }
}
