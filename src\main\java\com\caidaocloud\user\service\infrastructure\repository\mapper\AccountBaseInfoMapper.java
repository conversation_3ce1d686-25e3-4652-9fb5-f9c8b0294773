package com.caidaocloud.user.service.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.AccountBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccountBaseInfoMapper extends BaseMapper<AccountBaseInfo> {
    int insertSelective(AccountBaseInfo record);

    int updateByPrimaryKeySelective(AccountBaseInfo record);

    int insertBatch(@Param("records") List<AccountBaseInfo> records);

    int updateBatch(@Param("records") List<AccountBaseInfo> records);

    AccountBaseInfo selectByPrimaryKey(@Param("accountId") Long accountId);
}
