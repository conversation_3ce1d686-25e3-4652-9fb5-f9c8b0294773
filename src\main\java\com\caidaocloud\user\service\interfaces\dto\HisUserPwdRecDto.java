package com.caidaocloud.user.service.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户历史密码DTO")
public class HisUserPwdRecDto {
    @ApiModelProperty("修改记录ID")
    private Long recId;
    @ApiModelProperty("账号ID")
    private Long accountId;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("密码")
    private String password;
    @ApiModelProperty("盐值")
    private String salt;
    @ApiModelProperty("创建人")
    private Long createBy;
    @ApiModelProperty("创建时间")
    private Long createTime;
    @ApiModelProperty("修改人")
    private Long updateBy;
    @ApiModelProperty("修改时间")
    private Long updateTime;
    @ApiModelProperty("删除状态 0 未删除 1 已删除")
    private Integer deleted;
}
