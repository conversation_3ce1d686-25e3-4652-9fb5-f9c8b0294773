package com.caidaocloud.user.service.domain.entity;

import com.caidaocloud.user.service.domain.repository.IUserTenantRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/8/2021 3:10 PM
 * 4
 */
@Slf4j
@Service
@Data
public class UserTenant {
    private String id;
    private long userId;
    private String userAccount;
    private String tenantId;
    private long createdTime;
    private long updatedTime;

    @Autowired
    private IUserTenantRepository userTenantRepository;

    public void save(List<UserTenant> list) {
        userTenantRepository.save(list);
    }

    public UserTenant getByUserIdOrAccount(long userId, String userAccount) {
        return userTenantRepository.getByUserIdOrAccount(userId, userAccount);
    }
}
