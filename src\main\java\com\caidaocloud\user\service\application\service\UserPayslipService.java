package com.caidaocloud.user.service.application.service;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.LoginPlatform;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.dto.UserAccountBaseInfoDto;
import com.caidaocloud.user.service.application.dto.UserAccountPayslipInfoDto;
import com.caidaocloud.user.service.application.utils.AESUtils;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.*;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.AccountPayslipInfoDomainService;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.ChangePasswordDto;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.user.service.interfaces.dto.UserDto;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * @Author: Daniel
 * @Desc:
 * @Date:
 * 4
 */
@Slf4j
@Service
public class UserPayslipService {
    @Autowired
    private ISessionService sessionService;

    @Autowired
    private AccountPayslipInfoDomainService accountPayslipInfoDomainService;

    @Autowired
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Autowired
    private CacheService cacheService;

    public UserAccountPayslipInfoDto getUserAccountByUserId(Long userId) {
        UserAccountPayslipInfoDto dto = new UserAccountPayslipInfoDto();
        dto.setFirstPasswordLoginPayslip(false);
        dto.setFirstPasswordLoginPayslip(false);
        Optional<UserBaseInfoDo> userOptional = userBaseInfoDomainService.getByUserId(userId);
        if (!userOptional.isPresent() || null == userOptional.get().getUserId()) {
            return dto;
        }
        UserBaseInfoDo userBaseInfoData = userOptional.get();
        BeanUtil.copyProperties(userBaseInfoData, dto);
        AccountBaseInfoDo accountBaseInfoData = accountBaseInfoDomainService.getAccountByAccountId(userBaseInfoData.getAccountId());
        if (null == accountBaseInfoData || null == accountBaseInfoData.getAccountId()) {
            return dto;
        }
        BeanUtil.copyProperties(accountBaseInfoData, dto);
        AccountPayslipInfoDo accountPayslipInfoData = accountPayslipInfoDomainService.getAccountByAccountId(userBaseInfoData.getAccountId());
        if (null == accountPayslipInfoData || null == accountPayslipInfoData.getAccountId()) {
            AccountPayslipInfoDo accountPayslipInfoDo = new AccountPayslipInfoDo();
            accountPayslipInfoDo.setAccountId(accountBaseInfoData.getAccountId());
            accountPayslipInfoDo.setAccount(accountBaseInfoData.getAccount());
            accountPayslipInfoDo.setStatus(accountBaseInfoData.getStatus());
            accountPayslipInfoDo.setCreateBy(userId);
            accountPayslipInfoDo.setUpdateBy(userId);
            accountPayslipInfoDomainService.syncInsert(accountPayslipInfoDo);
            dto.setFirstPasswordLoginPayslip(true);
            dto.setFirstGestureLoginPayslip(true);
        } else {
            if(accountPayslipInfoData != null) {
                if (StringUtils.isEmpty(accountPayslipInfoData.getPassword())) {
                    dto.setFirstPasswordLoginPayslip(true);
                }
                if (StringUtils.isEmpty(accountPayslipInfoData.getGesture())) {
                    dto.setFirstGestureLoginPayslip(true);
                }
            } else {
                dto.setFirstPasswordLoginPayslip(true);
                dto.setFirstGestureLoginPayslip(true);
            }
        }
        return dto;
    }

    @Transactional
    public void changePassword(ChangePasswordDto dto) {
        PreCheck.preCheckArgument(StringUtil.isEmpty(dto.getPassword()), LangUtil.getMsg(MsgCodeConstant.PLEASE_ENTER_NEW_PASSWORD));
        //PreCheck.preCheckArgument(StringUtil.isEmpty(dto.getOldPassword()), LangUtil.getMsg(MsgCodeConstant.PLEASE_ENTER_OLD_PASSWORD));
        // 旧密码校验
        UserInfo userInfo = sessionService.getUserInfo();
        PreCheck.preCheckArgument(null == userInfo || null == userInfo.getUserId(), LangUtil.getMsg(MsgCodeConstant.LOGIN_FAILURE));

        // 用户查询
        Optional<UserBaseInfoDo> optional = userBaseInfoDomainService.getByUserId(userInfo.getUserId());
        PreCheck.preCheckArgument(!optional.isPresent() || null == optional.get().getUserId(),
                LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

        // 账号查询
        UserBaseInfoDo userBaseInfoData = optional.get();
        AccountPayslipInfoDo accountPayslipInfoData = accountPayslipInfoDomainService.getAccountByAccountId(userBaseInfoData.getAccountId());
        PreCheck.preCheckArgument(null == accountPayslipInfoData || null == accountPayslipInfoData.getAccountId(),
                LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

        // 旧密码校验
//        boolean checkFlag = doCheckPassword(dto.isEncrypt(), dto.getOldPassword(), accountPayslipInfoData);
//        PreCheck.preCheckArgument(!checkFlag, LangUtil.getMsg(MsgCodeConstant.ORIGINAL_PASSWORD_ERROR));

        doChangePassword(dto.getPassword(), userInfo.getUserId(), accountPayslipInfoData);

        val exist = cacheService.containsKey("user_payslip_verify_code_session_" + userInfo.getUserId());
        if (!exist) {
            cacheService.cacheValue("user_payslip_verify_code_session_" + userInfo.getUserId(), "1", 10 * 60);
        }
    }

    private void doChangePassword(String password,Long userId, AccountPayslipInfoDo accountPayslipInfoData) {
        accountPayslipInfoDomainService.changePassword(password.trim(), userId, accountPayslipInfoData);
    }

    @Transactional
    public void changeGesture(ChangePasswordDto dto) {
        PreCheck.preCheckArgument(StringUtil.isEmpty(dto.getGesture()), LangUtil.getMsg(MsgCodeConstant.PLEASE_ENTER_NEW_GESTURE));
        // 旧密码校验
        UserInfo userInfo = sessionService.getUserInfo();
        PreCheck.preCheckArgument(null == userInfo || null == userInfo.getUserId(), LangUtil.getMsg(MsgCodeConstant.LOGIN_FAILURE));

        // 用户查询
        Optional<UserBaseInfoDo> optional = userBaseInfoDomainService.getByUserId(userInfo.getUserId());
        PreCheck.preCheckArgument(!optional.isPresent() || null == optional.get().getUserId(),
                LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

        // 账号查询
        UserBaseInfoDo userBaseInfoData = optional.get();
        AccountPayslipInfoDo accountPayslipInfoData = accountPayslipInfoDomainService.getAccountByAccountId(userBaseInfoData.getAccountId());
        PreCheck.preCheckArgument(null == accountPayslipInfoData || null == accountPayslipInfoData.getAccountId(),
                LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));

        doChangeGesture(dto.getGesture(), userInfo.getUserId(), accountPayslipInfoData);
    }

    private void doChangeGesture(String gesture,Long userId, AccountPayslipInfoDo accountPayslipInfoData) {
        accountPayslipInfoDomainService.changeGesture(gesture.trim(), userId, accountPayslipInfoData);
    }
}
