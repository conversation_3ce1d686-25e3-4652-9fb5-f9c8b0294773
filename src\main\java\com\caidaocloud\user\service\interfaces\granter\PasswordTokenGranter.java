package com.caidaocloud.user.service.interfaces.granter;

import com.caidaocloud.user.service.application.feign.IDataScopeClient;
import com.caidaocloud.user.service.application.service.ReceiptTokenService;
import com.caidaocloud.user.service.application.service.UserAppService;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 密码模式登录
 * <AUTHOR>
 * @date 2020-01-15
 */
@Component
@Slf4j
public class PasswordTokenGranter extends AbstractTokenGranter {
    @Autowired
    private UserAppService userAppService;

    @Autowired
    private IDataScopeClient dataScopeClient;

    @Autowired
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Autowired
    private ReceiptTokenService receiptTokenService;

    @Autowired
    private LoginDomainService loginDomainService;

    @Override
    public Result grant(LoginDto loginDto) {
        Result result = beforeGrant(loginDto);
        if(null != result){
            return result;
        }

        LoginOutDto loginOutDo = userAppService.login(loginDto);
        return afterGrant(loginOutDo);
    }

}
