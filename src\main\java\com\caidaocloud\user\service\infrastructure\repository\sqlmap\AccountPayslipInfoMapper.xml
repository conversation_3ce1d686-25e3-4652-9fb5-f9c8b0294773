<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.caidaocloud.user.service.infrastructure.repository.mapper.AccountPayslipInfoMapper">
    <insert id="insertSelective" parameterType="com.caidaocloud.user.service.infrastructure.repository.po.AccountPayslipInfo">
        insert into account_payslip_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                account_id,
            </if>
            <if test="accountLoginPrefix != null">
                account_login_prefix,
            </if>
            <if test="account != null">
                account,
            </if>
            <if test="mobNum != null">
                mob_num,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="salt != null">
                salt,
            </if>
            <if test="gesture != null">
                gesture,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="regType != null">
                reg_type,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountId != null">
                #{accountId,jdbcType=BIGINT},
            </if>
            <if test="accountLoginPrefix != null">
                #{accountLoginPrefix,jdbcType=VARCHAR},
            </if>
            <if test="account != null">
                #{account,jdbcType=VARCHAR},
            </if>
            <if test="mobNum != null">
                #{mobNum,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                #{salt,jdbcType=VARCHAR},
            </if>
            <if test="gesture != null">
                #{gesture,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="regType != null">
                #{regType,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.caidaocloud.user.service.infrastructure.repository.po.AccountPayslipInfo">
        update account_payslip_info
        <set>
            <if test="accountLoginPrefix != null">
                account_login_prefix = #{accountLoginPrefix,jdbcType=VARCHAR},
            </if>
            <if test="account != null">
                account = #{account,jdbcType=VARCHAR},
            </if>
            <if test="mobNum != null">
                mob_num = #{mobNum,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                salt = #{salt,jdbcType=VARCHAR},
            </if>
            <if test="gesture != null">
                gesture = #{gesture,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="regType != null">
                reg_type = #{regType,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=INTEGER},
            </if>
        </set>
        where account_id = #{accountId,jdbcType=BIGINT}
    </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into account_payslip_info (account_id, account_login_prefix, account,
        mob_num, email, password,
        salt, gesture, status,
        reg_type, create_by, create_time,
        update_by, update_time, deleted
        )
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.accountId,jdbcType=BIGINT}, #{record.accountLoginPrefix,jdbcType=VARCHAR},
            #{record.account,jdbcType=VARCHAR},
            #{record.mobNum,jdbcType=VARCHAR}, #{record.email,jdbcType=VARCHAR}, #{record.password,jdbcType=VARCHAR},
            #{record.salt,jdbcType=VARCHAR}, #{record.gesture,jdbcType=VARCHAR}, #{record.status,jdbcType=INTEGER},
            #{record.regType,jdbcType=VARCHAR}, #{record.createBy,jdbcType=BIGINT},
            #{record.createTime,jdbcType=BIGINT},
            #{record.updateBy,jdbcType=BIGINT}, #{record.updateTime,jdbcType=BIGINT}, #{record.deleted,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="records" item="bean" index="index" open="" close="" separator=";">
            update account_payslip_info
            <set>
                <if test="bean.accountLoginPrefix != null">
                    account_login_prefix = #{bean.accountLoginPrefix,jdbcType=VARCHAR},
                </if>
                <if test="bean.account != null">
                    account = #{bean.account,jdbcType=VARCHAR},
                </if>
                <if test="bean.mobNum != null">
                    mob_num = #{bean.mobNum,jdbcType=VARCHAR},
                </if>
                <if test="bean.email != null">
                    email = #{bean.email,jdbcType=VARCHAR},
                </if>
                <if test="bean.password != null">
                    password = #{bean.password,jdbcType=VARCHAR},
                </if>
                <if test="bean.salt != null">
                    salt = #{bean.salt,jdbcType=VARCHAR},
                </if>
                <if test="bean.gesture != null">
                    gesture = #{bean.gesture,jdbcType=VARCHAR},
                </if>
                <if test="bean.status != null">
                    status = #{bean.status,jdbcType=INTEGER},
                </if>
                <if test="bean.regType != null">
                    reg_type = #{bean.regType,jdbcType=VARCHAR},
                </if>
                <if test="bean.createBy != null">
                    create_by = #{bean.createBy,jdbcType=BIGINT},
                </if>
                <if test="bean.createTime != null">
                    create_time = #{bean.createTime,jdbcType=BIGINT},
                </if>
                <if test="bean.updateBy != null">
                    update_by = #{bean.updateBy,jdbcType=BIGINT},
                </if>
                <if test="bean.updateTime != null">
                    update_time = #{bean.updateTime,jdbcType=BIGINT},
                </if>
                <if test="bean.deleted != null">
                    deleted = #{bean.deleted,jdbcType=INTEGER},
                </if>
            </set>
            where account_id = #{bean.accountId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectByPrimaryKey">
        
    </select>
</mapper>