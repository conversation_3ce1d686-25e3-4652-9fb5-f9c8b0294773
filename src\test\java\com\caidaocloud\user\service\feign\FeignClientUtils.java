package com.caidaocloud.user.service.feign;

import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

public class FeignClientUtils {

    private static ApplicationContext applicationContext = null;
    private static final Map<String, Object> BEAN_CACHE = new ConcurrentHashMap<>();

    public static void setAppContext(ApplicationContext applicationContext){
        FeignClientUtils.applicationContext = applicationContext;
    }

    public static <T> T build(String serverName, Class<T> targetClass) {
        return buildClient(serverName, targetClass);
    }

    @SuppressWarnings("unchecked")
    private static <T> T buildClient(String serverName, Class<T> targetClass) {
        T t = (T) BEAN_CACHE.get(serverName);
        if (Objects.isNull(t)) {
            FeignClientBuilder.Builder<T> builder = new FeignClientBuilder(applicationContext).forType(targetClass, serverName);
            t = builder.build();
            BEAN_CACHE.put(serverName, t);
        }
        return t;
    }
}
