package com.caidaocloud.user.service.application.feign;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.security.config.FeignConfiguration;
import com.caidaocloud.user.service.application.dto.auth.AuthSubjectAndRoleDto;
import com.caidaocloud.user.service.application.dto.auth.AuthSubjectRoleDto;
import com.caidaocloud.user.service.application.dto.operate.AuthImportDto;
import com.caidaocloud.user.service.application.dto.operate.AuthImportMsgDto;
import com.caidaocloud.user.service.application.feign.fallback.AuthFeignFallback;
import com.caidaocloud.web.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/12
 **/
@FeignClient(value = "caidaocloud-auth-service", path = "/api/auth",
        fallback = AuthFeignFallback.class, configuration = FeignConfiguration.class)
public interface IAuthFeign {


    /**
     * 删除员工
     *
     * @return
     */
    @DeleteMapping(value = "/v1/subject/role/all")
    Result deleteSubjects(@RequestParam("subjectIds") String subjectIds);

    /**
     * 同步授予管理员权限
     *
     * @param subjectIds
     * @return
     */
    @PostMapping("/v1/subject/authorize/admin")
    Result authAdminSubjects(@RequestParam("subjects") List<Long> subjectIds);

    /**
     * 获取用户所有权限url
     *
     * @param subjectId
     * @return
     */
    @GetMapping("/v1/subject/resource/url/all")
    Result<List<String>> getResourceUrlListBySubjectId(@RequestParam("subjectId") Long subjectId,
                                                       @RequestHeader("Access-Token") String accessToken);

    /**
     * 员工授权
     *
     * @param authToSubjectList
     * @return
     */
    @PostMapping("/v1/subject/authorize")
    Result<List<AuthImportMsgDto>> authorizationToUser(@RequestBody List<AuthImportDto> authToSubjectList);

    /**
     * 员工授权
     *
     * @param authToSubjectList
     * @return
     */
    @PostMapping("/v1/subject/authorize/refresh")
    Result<List<AuthImportMsgDto>> refreshAuthorizationToUser(@RequestBody List<AuthImportDto> authToSubjectList);


    /**
     * 获取用户角色名称
     *
     * @param subjectIds
     * @return
     */
    @GetMapping("/v1/subject/role/name")
    Result<List<AuthSubjectAndRoleDto>> getRoleNameBySubjectIds(@RequestParam(value = "subjectIds", required = true) String subjectIds);

    /**
     * 管理员初始化
     *
     * @param tenantId
     * @param userId
     * @return
     */
    @PostMapping("/v1/tenant/init/admin")
    Result<String> initAdmin(@RequestParam("tenantId") String tenantId, @RequestParam("userId") String userId);

    /**
     * 管理员初始化
     *
     * @param tenantId
     * @param userId
     * @return
     */
    @PostMapping("/v1/tenant/init/config")
    Result<String> initConfig(@RequestParam("tenantId") String tenantId, @RequestParam("userId") String userId);


    @PostMapping("/v1/subject/emp/role/change")
    Result empChangeRole(@RequestBody AuthSubjectRoleDto subjectRoleDto);


    /**
     * 获取所有启动角色
     * @return
     */
    @GetMapping("/v1/role/enabled/all")
    Result<List<KeyValue>> getAllEnabledRole();
}