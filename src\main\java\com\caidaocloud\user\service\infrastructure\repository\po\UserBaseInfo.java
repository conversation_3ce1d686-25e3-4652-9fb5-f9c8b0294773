package com.caidaocloud.user.service.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.user.service.domain.entity.AbstractBaseEntity;
import lombok.Data;

@Data
@TableName("user_base_info")
public class UserBaseInfo extends AbstractBaseEntity {
    /**
     * 用户ID
     */
    @TableId(type = IdType.INPUT)
    private Long userId;
    /**
     * 账号ID
     */
    private Long accountId;
    /**
     * 用户统一注册账号
     */
    private String account;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 头像
     */
    private String headPortrait;
    /**
     * 用户状态：1 正常 2 停用 3 锁定
     */
    private Integer status;
    /**
     * 是否为默认用户
     */
    private Boolean ifDefault = false;
    /**
     * 员工ID
     */
    private Long empId;
    /**
     * 集团公司ID
     */
    private Long corpId;
    /**
     * 扩展信息
     */
    private String extInfo;
    /**
     * 是否候选人账号
     */
    private Boolean onboarding;
    /**
     * 钉钉userId
     */
    private String dingUserId;
    /**
     * 最后登录时间
     */
    private Long lastLoginTime;
}
