package com.caidaocloud.user.service.application.dto;

import com.caidaocloud.user.service.domain.util.PassGenerateHelp;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PwdCreateDto {
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("身份证号")
    private String cardNo;
    @ApiModelProperty("手机号")
    private String mobile;
    @ApiModelProperty("初始密码默认长度")
    private int defaultFirstLength = 6;
    @ApiModelProperty("初始密码默认规则")
    private int defaultFirstRule = PassGenerateHelp.TYPE_NUMBER | PassGenerateHelp.TYPE_UPPER | PassGenerateHelp.TYPE_LOWER;
}
