package com.caidaocloud.user.service.domain.repository;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.service.SessionServiceImpl;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.Application;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.SyncOnBoarDingDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.RequestHelper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 *
 * <AUTHOR> Zhou
 * @date 2023/5/6
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringJUnit4ClassRunner.class)
@Slf4j
public class IAccountBaseInfoRepositoryTests {

	@SneakyThrows
	@Test
	public void testSendSms() {
		String json = "[{\n"
				+ "  \"mobNum\": \"***********\",\n"
				+ "  \"tenantId\": 11,\n"
				+ "  \"userName\": \"测试\",\n"
				+ "  \"empId\": ****************\n"
				+ "}]";
		List<SyncOnBoarDingDto> list = FastjsonUtil.toArrayList(json, SyncOnBoarDingDto.class);
		SpringUtil.getBean(UserBaseInfoService.class).syncOnBoarDingSave(list);
	}

	@Before
	public void setUp() throws Exception {
		SecurityUserInfo userInfo = new SecurityUserInfo();
		userInfo.setTenantId("11");
		userInfo.setUserId(0L);
		userInfo.setEmpId(0L);
		SecurityUserUtil.setSecurityUserInfo(userInfo);

		RequestHelper.getRequest()
				.setAttribute("_caidaocloud_userinfo_request_threadlocal_", ObjectConverter.convert(userInfo, UserInfo.class));

// Get the instance of SessionServiceImpl which is already created by Spring bean
		SessionServiceImpl service = (SessionServiceImpl) SpringUtil.getBean(ISessionService.class);
		try {
			// Get the private field "threadLocalCache" from the SessionServiceImpl class
			Field field = SessionServiceImpl.class.getDeclaredField("threadLocalCache");
			// Set the accessibility of the field to true, as it is private
			field.setAccessible(true);
			// Set the value of the "threadLocalCache" field to true
			field.set(service, true);
		}
		catch (NoSuchFieldException | IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	@Autowired
	private IAccountBaseInfoRepository repository;

	private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

	private AccountBaseInfoDo account;

	private List<Long> accountIds = new ArrayList<Long>();

	@Test
	public void testInsert() {
		account = createAccountBaseInfoDo("<EMAIL>", "***********");
		accountIds.add(account.getAccountId());;
		repository.insertBatch(Collections.singletonList(account));

		verify(account, repository.getById(account.getAccountId()));
	}

	@Test
	public void testInsertS() {
		account = createAccountBaseInfoDo("<EMAIL>", "***********");
		accountIds.add(account.getAccountId());;
		repository.insertSelective(account);

		verify(account, repository.getById(account.getAccountId()));
	}


	private void verify(AccountBaseInfoDo except, AccountBaseInfoDo data) {
		// verify
		assertEquals(except.getAccountId(), data.getAccountId());
		assertEquals(except.getAccountLoginPrefix(), data.getAccountLoginPrefix());
		assertEquals(except.getAccount(), data.getAccount());
		assertEquals(except.getMobNum(), data.getMobNum());
		assertEquals(except.getEmail(), data.getEmail());
		assertEquals(except.getPassword(), data.getPassword());
		assertEquals(except.getSalt(), data.getSalt());
		assertEquals(except.getGesture(), data.getGesture());
		assertEquals(except.getStatus(), data.getStatus());
		assertEquals(except.getDeleted(), data.getDeleted());
		assertEquals(except.getRegType(), data.getRegType());
		assertEquals(except.getCreateBy(), data.getCreateBy());
		assertEquals(except.getUpdateBy(), data.getUpdateBy());
		assertEquals(except.getCreateTime(), data.getCreateTime());
		assertEquals(except.getUpdateTime(), data.getUpdateTime());
	}

	@Test
	public void testUpdate() {
		account = createAccountBaseInfoDo("<EMAIL>", "***********");
		accountIds.add(account.getAccountId());;
		repository.insertBatch(Collections.singletonList(account));

		// change the email after inserting
		account.setEmail("<EMAIL>");
		repository.updateBatch(Collections.singletonList(account));

		verify(account, repository.getById(account.getAccountId()));
	}

	@Test
	public void testUpdateS() {
		account = createAccountBaseInfoDo("<EMAIL>", "***********");
		accountIds.add(account.getAccountId());;
		repository.insertSelective((account));

		// change the email after inserting
		account.setEmail("<EMAIL>");
		repository.updateByPrimaryKeySelective((account));

		verify(account, repository.getById(account.getAccountId()));
	}

	@After
	public void cleanup() {
		repository.deleteByIds(accountIds);
	}

	public AccountBaseInfoDo createAccountBaseInfoDo(String email, String mobNum) {
		AccountBaseInfoDo account = new AccountBaseInfoDo();
		account.setAccountId(snowflakeUtil.createId());
		account.setAccountLoginPrefix("caidao-");
		account.setAccount("example");
		account.setMobNum(mobNum);
		account.setEmail(email);
		account.setPassword("password");
		account.setSalt("salt");
		account.setGesture("gesture");
		account.setStatus(1);
		account.setDeleted(0);
		account.setRegType("normal");
		// get the current security user info 
		SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
// set the createBy and updateBy parameters to the user's ID from the UserInfo object
		account.setCreateBy(userInfo.getUserId());
		account.setUpdateBy(userInfo.getUserId());
// Set the create time and update time to the current timestamp
		long timestamp = System.currentTimeMillis();
		account.setCreateTime(timestamp);
		account.setUpdateTime(timestamp);
		return account;
	}
}