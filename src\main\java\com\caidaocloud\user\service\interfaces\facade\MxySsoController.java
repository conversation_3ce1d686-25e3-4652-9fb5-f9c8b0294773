package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.user.service.application.service.sso.MxySsoService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/user/mxy/v1")
@Api(value = "魔学院平台单点登陆", description = "魔学院平台单点登陆", tags = "魔学院平台单点登陆")
public class MxySsoController {

    @Resource
    private MxySsoService mxySsoService;

    @ApiOperation(value = "魔学院单点登陆")
    @GetMapping(value = "/myxSsoLogin")
    public Result<String> myxSsoLogin(HttpServletRequest request, HttpServletResponse response,
                                      @RequestParam(value = "platform", defaultValue = "pc") String platform) {
        return Result.ok(mxySsoService.getMxySsoLink(platform));
    }
}
