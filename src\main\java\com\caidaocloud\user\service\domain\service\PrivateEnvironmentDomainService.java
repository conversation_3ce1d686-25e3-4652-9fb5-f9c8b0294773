package com.caidaocloud.user.service.domain.service;

import com.caidaocloud.user.service.domain.entity.PrivateEnvironment;
import com.caidaocloud.user.service.domain.enums.PrivateEnvironmentType;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PrivateEnvironmentDomainService {

    public List<PrivateEnvironment> list(PrivateEnvironmentType type){
        return PrivateEnvironment.list(type);
    }
}
