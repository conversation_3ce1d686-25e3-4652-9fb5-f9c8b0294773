package com.caidaocloud.user.service.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidaocloud.em.DeleteStatusEnum;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.enums.AccountType;
import com.caidaocloud.user.service.domain.repository.IAccountBaseInfoRepository;
import com.caidaocloud.user.service.infrastructure.repository.mapper.AccountBaseInfoMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.AccountBaseInfo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Sequences;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Repository
public class AccountBaseInfoRepositoryImpl implements IAccountBaseInfoRepository {
    @Autowired
    private AccountBaseInfoMapper accountBaseInfoMapper;

    @Override
    public List<AccountBaseInfoDo> selectList(String account, AccountType accountType) {
        LambdaQueryWrapper<AccountBaseInfo> mQueryInfo = new QueryWrapper<AccountBaseInfo>().lambda();
        mQueryInfo.ne(AccountBaseInfo::getDeleted, 1);
        if (accountType == AccountType.MOBILE) {
            mQueryInfo.eq(AccountBaseInfo::getMobNum, account);
        } else if (accountType == AccountType.EMAIL) {
            mQueryInfo.eq(AccountBaseInfo::getEmail, account);
        } else {
            mQueryInfo.eq(AccountBaseInfo::getAccount, account);
        }
        List<AccountBaseInfo> list = accountBaseInfoMapper.selectList(mQueryInfo);
        return CollectionUtils.isEmpty(list) ? Lists.newArrayList() : ObjectConverter.convertList(list, AccountBaseInfoDo.class);
    }

    @Override
    public void insertBatch(List<AccountBaseInfoDo> dataList) {
        accountBaseInfoMapper.insertBatch(ObjectConverter.convertList(dataList, AccountBaseInfo.class));
    }

    @Override
    public void insert(AccountBaseInfoDo data) {
        accountBaseInfoMapper.insert(ObjectConverter.convert(data, AccountBaseInfo.class));
    }

    @Override
    public void update(AccountBaseInfoDo data) {
        accountBaseInfoMapper.updateById(ObjectConverter.convert(data, AccountBaseInfo.class));
    }

    @Override
    public int insertSelective(AccountBaseInfoDo record) {
        return accountBaseInfoMapper.insertSelective(ObjectConverter.convert(record, AccountBaseInfo.class));
    }

    @Override
    public int updateByPrimaryKeySelective(AccountBaseInfoDo record) {
        return accountBaseInfoMapper.updateByPrimaryKeySelective(ObjectConverter.convert(record, AccountBaseInfo.class));
    }

    @Override
    public void deleteByIds(List<Long> accountIds) {
        accountBaseInfoMapper.deleteBatchIds(accountIds);
    }

    @Override
    public void softDeleteByIds(List<Long> accountIds) {
        AccountBaseInfo accountBaseInfo = new AccountBaseInfo();
        accountBaseInfo.setDeleted(DeleteStatusEnum.DELETED.getIndex());
        accountBaseInfo.setUpdateTime(System.currentTimeMillis());
        LambdaQueryWrapper<AccountBaseInfo> mQueryInfo = new QueryWrapper<AccountBaseInfo>().lambda();
        mQueryInfo.in(AccountBaseInfo::getAccountId, accountIds);
        accountBaseInfoMapper.update(accountBaseInfo, mQueryInfo);
    }

    @Override
    public List<AccountBaseInfoDo> getListByIds(List<Long> accountIds) {
        return ObjectConverter.convertList(accountBaseInfoMapper.selectBatchIds(accountIds), AccountBaseInfoDo.class);
    }

    @Override
    public AccountBaseInfoDo getById(Long accountId) {
        AccountBaseInfo info = accountBaseInfoMapper.selectById(accountId);
        return info == null ? null : ObjectConverter.convert(info, AccountBaseInfoDo.class);
    }

    @Override
    public List<AccountBaseInfoDo> selectAccountByMobNumAndEmail(String mobNum, String email) {
        LambdaQueryWrapper<AccountBaseInfo> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.ne(AccountBaseInfo::getDeleted, 1);
        queryWrapper.select(AccountBaseInfo::getAccountId, AccountBaseInfo::getMobNum, AccountBaseInfo::getEmail)
                .eq(AccountBaseInfo::getMobNum, mobNum);
        if (!StringUtils.isEmpty(email)) {
            queryWrapper.or().eq(AccountBaseInfo::getEmail, email);
        }
        List<AccountBaseInfo> list = accountBaseInfoMapper.selectList(queryWrapper);
        return Sequences.sequence(list).map(e -> FastjsonUtil.convertObject(e, AccountBaseInfoDo.class)).toList();
    }

    @Override
    public List<AccountBaseInfoDo> getAccountByMobNumAndEmail(String mobNum, String email) {
        LambdaQueryWrapper<AccountBaseInfo> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.ne(AccountBaseInfo::getDeleted, 1);
        queryWrapper.select(AccountBaseInfo::getAccountId, AccountBaseInfo::getMobNum, AccountBaseInfo::getEmail);
        if (!StringUtils.isEmpty(mobNum)) {
            queryWrapper.eq(AccountBaseInfo::getMobNum, mobNum);
        }
        if (!StringUtils.isEmpty(email)) {
            queryWrapper.eq(AccountBaseInfo::getEmail, email);
        }
        List<AccountBaseInfo> list = accountBaseInfoMapper.selectList(queryWrapper);
        return Sequences.sequence(list).map(e -> FastjsonUtil.convertObject(e, AccountBaseInfoDo.class)).toList();
    }

    @Override
    public AccountBaseInfoDo getAccountByAccountId(Long accountId) {
        var queryWrapper = new QueryWrapper<AccountBaseInfo>().lambda()
                .eq(AccountBaseInfo::getAccountId, accountId);
        AccountBaseInfo accountBaseInfo = accountBaseInfoMapper.selectOne(queryWrapper);
        return FastjsonUtil.convertObject(accountBaseInfo, AccountBaseInfoDo.class);
    }

    @Override
    public List<AccountBaseInfoDo> getAccountByMobNumOrEmail(List<String> mobNums, List<String> emails) {
        LambdaQueryWrapper<AccountBaseInfo> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.ne(AccountBaseInfo::getDeleted, 1);
        queryWrapper.select(AccountBaseInfo::getAccountId, AccountBaseInfo::getMobNum, AccountBaseInfo::getEmail);
        queryWrapper.and(wq->{
            if (!CollectionUtils.isEmpty(emails)) {
                wq.in(AccountBaseInfo::getEmail, emails);
            }
            if (!CollectionUtils.isEmpty(mobNums)) {
                wq.or().in(AccountBaseInfo::getMobNum, mobNums);
            }
            return wq;
        });

        List<AccountBaseInfo> list = accountBaseInfoMapper.selectList(queryWrapper);
        return Sequences.sequence(list).map(e -> FastjsonUtil.convertObject(e, AccountBaseInfoDo.class)).toList();
    }

    @Override
    public int updateBatch(List<AccountBaseInfoDo> dataList) {
        return accountBaseInfoMapper.updateBatch(ObjectConverter.convertList(dataList, AccountBaseInfo.class));
    }

    @Override
    public List<AccountBaseInfoDo> getAccountByMobNums(List<String> mobNums) {
        LambdaQueryWrapper<AccountBaseInfo> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.ne(AccountBaseInfo::getDeleted, 1);
        queryWrapper.select(AccountBaseInfo::getAccountId, AccountBaseInfo::getMobNum, AccountBaseInfo::getEmail)
                .in(AccountBaseInfo::getMobNum, mobNums);
        List<AccountBaseInfo> list = accountBaseInfoMapper.selectList(queryWrapper);
        return Sequences.sequence(list).map(e -> FastjsonUtil.convertObject(e, AccountBaseInfoDo.class)).toList();
    }
}
