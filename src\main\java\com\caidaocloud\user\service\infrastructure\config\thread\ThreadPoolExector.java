package com.caidaocloud.user.service.infrastructure.config.thread;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 自定义线程池
 *
 * <AUTHOR>
 * @date 2022/5/19
 **/
@Slf4j
@Component
public class ThreadPoolExector {

    @Value("${caidaocloud.thread.corePoolSize:3}")
    private int corePoolSize;

    @Value("${caidaocloud.thread.maxPoolSize:5}")
    private int maxPoolSize;

    private ThreadFactory threadFactory;

    private ThreadPoolExecutor threadPool;

    public ThreadPoolExecutor getThreadPool() {
        if (threadFactory == null || threadPool == null) {
            init();
        }
        return threadPool;
    }

    @PostConstruct
    public void init() {
        if (maxPoolSize < corePoolSize) {
            corePoolSize = maxPoolSize / 2;
        }

        if (threadFactory == null) {
            threadFactory = new ThreadFactoryBuilder().setNameFormat("ThreadPoolExector-%d").build();
        }

        if (threadPool == null) {
            threadPool = new ThreadPoolExecutor(corePoolSize, maxPoolSize, 60, TimeUnit.SECONDS,
                    new LinkedBlockingQueue(1000), threadFactory, new ThreadPoolExecutor.CallerRunsPolicy());
        }

        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (threadPool != null && !threadPool.isShutdown()) {
                threadPool.shutdown();
                if (log.isDebugEnabled()) {
                    log.debug("[ThreadPoolExector] thread pool is shutdown now");
                }
            }
        }));

        log.info("init Thread Pool success");
    }

}
