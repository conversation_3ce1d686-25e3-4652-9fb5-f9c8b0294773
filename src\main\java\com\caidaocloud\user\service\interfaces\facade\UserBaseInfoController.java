package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.dto.PageResult;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportDto;
import com.caidaocloud.user.service.application.dto.operate.UserImportErrorDto;
import com.caidaocloud.user.service.application.dto.preemp.PreEmpUserDto;
import com.caidaocloud.user.service.application.enums.DataOpEnum;
import com.caidaocloud.user.service.application.service.UserAppService;
import com.caidaocloud.user.service.application.service.UserBaseInfoService;
import com.caidaocloud.user.service.application.service.operate.ImportService;
import com.caidaocloud.user.service.application.service.operate.impl.*;
import com.caidaocloud.user.service.application.service.sso.SamlService;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.*;
import com.caidaocloud.user.service.interfaces.vo.UserBaseInfoVo;
import com.caidaocloud.user.service.interfaces.vo.UserDetailInfoVo;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/user/base/v2")
@Api(value = "/api/user/base/v2", tags = "用户中心2.0-用户管理")
public class UserBaseInfoController {

    @Resource
    private UserBaseInfoService userBaseInfoService;

    @Resource
    private UserAppService userAppService;

    @Resource
    private SamlService samlService;

    @Value("${logout.type:common}")
    private String logoutType;

    @Value("${logout.redirect.page:}")
    private String redirectLogoutPage;

    @Value("${saml.sso.errorPage:/}")
    private String errorPage;

    @PostMapping("/sync")
    @ApiOperation("同步保存用户信息")
    public Result<Boolean> syncUser(@RequestBody UserAccountInfoDto dto) {
        try {
            PreCheck.preCheckArgument(dto == null, "Data is empty");
            PreCheck.preCheckArgument(dto.getUserId() == null, "用户ID不允许为空");
            if (dto.getOp() == DataOpEnum.DELETE) {
                userBaseInfoService.deleteByIds(new ArrayList<>(Arrays.asList(dto.getUserId())));
            } else if (dto.getOp() == DataOpEnum.INSERT || dto.getOp() == DataOpEnum.UPDATE
                    || dto.getOp() == DataOpEnum.CHANGE_PASSWORD) {
                userBaseInfoService.syncSave(new ArrayList<>(Arrays.asList(dto)));
            } else {
                return Result.fail("Op Field value can only be [INSERT UPDATE DELETE CHANGE_PASSWORD]");
            }
        } catch (Exception e) {
            log.error("UserBaseInfoController.syncUser error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }

    @PostMapping("/batchSync")
    @ApiOperation("批量同步保存用户信息")
    public Result<Boolean> batchSyncUser(@RequestBody List<UserAccountInfoDto> dtos) {
        log.info("batchSyncUser dtos.size={}", dtos.size());
        try {
            PreCheck.preCheckArgument(CollectionUtils.isEmpty(dtos), "Data is empty");
            long nullCount = dtos.stream().filter(o -> o.getUserId() == null && o.isRequiredUserId()).count();
            PreCheck.preCheckArgument(nullCount > 0, "用户ID不允许为空");
            long count = dtos.stream().filter(it -> {
                DataOpEnum opEnum = it.getOp();
                return opEnum == DataOpEnum.INSERT || opEnum == DataOpEnum.UPDATE
                        || opEnum == DataOpEnum.CHANGE_PASSWORD || opEnum == DataOpEnum.DELETE;
            }).count();
            PreCheck.preCheckArgument(count == 0, "Op Field value can only be [INSERT UPDATE DELETE CHANGE_PASSWORD]");
            List<Long> delIds = dtos.stream().filter(o -> o.getOp() == DataOpEnum.DELETE)
                    .map(UserAccountInfoDto::getUserId).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delIds)) {
                userBaseInfoService.deleteByIds(delIds);
            } else {
                dtos.removeIf(o -> o.getOp() == DataOpEnum.DELETE);
                if (CollectionUtils.isEmpty(dtos)) {
                    return Result.ok(true);
                }
                List<UserAccountInfoDto> delList = dtos.stream().filter(o -> o.getStatus() != null && o.getStatus() == -1).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(delList)) {
                    List<Long> delIdList = delList.stream().map(UserAccountInfoDto::getUserId).distinct().collect(Collectors.toList());
                    userBaseInfoService.deleteByIds(delIdList);
                    dtos.removeAll(delList);
                }

                if (CollectionUtils.isEmpty(dtos)) {
                    return Result.ok(true);
                }

                userBaseInfoService.syncSave(dtos);
            }
        } catch (Exception e) {
            log.error("UserBaseInfoController.batchSyncUser error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }

    @PostMapping("/batchSyncOnBoarDing")
    @ApiOperation("批量接入候选人接口")
    public Result<List<String>> batchSyncOnBoarDing(@RequestBody List<SyncOnBoarDingDto> dtoList) {
        try {
            PreCheck.preCheckArgument(CollectionUtils.isEmpty(dtoList), "Data is empty");
            List<String> failedMobiles = userBaseInfoService.syncOnBoarDingSave(dtoList);
            return Result.ok(failedMobiles);
        } catch (Exception e) {
            log.error("UserBaseInfoController.batchSyncOnBoarDing error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
    }

    @PostMapping("/batchSyncOfficial")
    @ApiOperation("候选人员工（用户）批量转正常员工用户接口")
    public Result<Boolean> batchSyncOfficial(@RequestBody List<SyncOnBoarDingDto> dtoList) {
        try {
            PreCheck.preCheckArgument(CollectionUtils.isEmpty(dtoList), "Data is empty");
            userBaseInfoService.syncOfficialUpd(dtoList);
        } catch (Exception e) {
            log.error("UserBaseInfoController.batchSyncOfficial error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }

    @GetMapping("/getUserList")
    @ApiOperation("根据账号查询用户信息")
    public Result<List<UserBaseInfoVo>> getUserList(@RequestParam("account") String account) {
        List<UserBaseInfoDo> list = userBaseInfoService.getUserListByAccount(account);
        if (CollectionUtils.isEmpty(list)) {
            return Result.ok(Lists.newArrayList());
        }
        return Result.ok(ObjectConverter.convertList(list, UserBaseInfoVo.class));
    }

    @GetMapping("/getUserById")
    @ApiOperation("根据用户ID查询用户信息")
    public Result getUserById(@RequestParam("userId") Long userId) {
        UserBaseInfoDo userBaseInfo = userBaseInfoService.getByUserId(userId);
        return Result.ok(ObjectConverter.convert(userBaseInfo, UserBaseInfoVo.class));
    }


    @PostMapping("/getUserByIds")
    @ApiOperation("根据用户userIds查询用户信息")
    public Result<List<UserBaseInfoVo>> getUserByIds(@RequestBody List<Long> userIds) {
        var userBaseInfos = userBaseInfoService.getByUserIds(userIds);
        return Result.ok(ObjectConverter.convertList(userBaseInfos, UserBaseInfoVo.class));
    }

    @GetMapping("/page")
    @ApiOperation("获取用户分页")
    public Result<PageResult<UserDetailInfoVo>> page(@RequestParam(value = "keywords", required = false) String keywords,
                                                     @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                     @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        return Result.ok(userBaseInfoService.page(keywords, pageNo, pageSize));
    }

    @GetMapping("/onBoarDingPage")
    @ApiOperation("获取候选人分页")
    public Result<PageResult<UserDetailInfoVo>> onBoarDingPage(@RequestParam(value = "keywords", required = false) String keywords,
                                                               @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                               @RequestParam(value = "pageSize", defaultValue = "10") int pageSize
    ) {
        return Result.ok(userBaseInfoService.onBoarDingPage(keywords, pageNo, pageSize));
    }

    @PostMapping("/queryUserBaseInfoList")
    @ApiOperation("获取用户列表")
    public Result<PageResult<UserDetailInfoVo>> queryUserBaseInfoList(@RequestBody UserBaseInfoQueryDto queryDto) {
        return Result.ok(userBaseInfoService.page(queryDto));
    }

    @PostMapping("/queryListByMobilesAndEmails")
    @ApiOperation("根据手机号邮箱获取用户列表")
    public Result<List<UserDetailInfoVo>> queryListByMobilesAndEmails(@RequestBody UserBaseInfoQueryParam queryParam) {
        List<UserBaseInfoDo> userBaseInfoDoList = userBaseInfoService.queryListByMobilesAndEmails(queryParam);

        return Result.ok(ObjectConverter.convertList(userBaseInfoDoList, UserDetailInfoVo.class));
    }


    @GetMapping("/detail")
    @ApiOperation("用户信息详情")
    public Result<UserDetailInfoVo> userBaseDetail(@RequestParam("accountId") Long accountId) {
        return Result.ok(userBaseInfoService.detail(accountId));
    }

    @PostMapping("/save")
    @ApiOperation("保存用户信息")
    @LogRecordAnnotation(menu = "管理中心-用户中心-用户列表-员工账号管理", category = "新增", success = "新增了{{#preEmpUser.userName}}")
    public Result userBaseSave(@RequestBody PreEmpUserDto preEmpUser) {
        return userBaseInfoService.save(preEmpUser);
    }

    @PostMapping("/update")
    @ApiOperation("修改用户信息")
    @LogRecordAnnotation(menu = "管理中心-用户中心-用户列表-员工账号管理", category = "编辑", success = "编辑了{name{#dto.accountId}}")
    public Result userBaseUpdate(@RequestBody UserBaseInfoDto dto) {
        return userBaseInfoService.userBaseUpdate(dto);
    }

    @PostMapping("/update/status")
    @ApiOperation("修改用户信息-状态")
    @LogRecordAnnotation(menu = "管理中心-用户中心-用户列表-员工账号管理", category = "{{#dto.status == 1 ? '启用' : '停用'}}", success = "{{#dto.status == 1 ? '启用' : '停用'}}了{name{#dto.accountId}}")
    public Result userBaseUpdateStatus(@RequestBody UserBaseInfoDto dto) {
        return userBaseInfoService.userBaseUpdate(dto);
    }

    @PostMapping("/update/private")
    @ApiOperation("修改用户信息（手机号/姓名）")
    public Result userUpdatePrivateInfo(@RequestParam(name = "empId", required = true) String empId,
                                        @RequestParam(name = "name", required = false) String name,
                                        @RequestParam(name = "phone", required = false) String phone) {
        return userBaseInfoService.userUpdatePrivateInfo(empId,name,phone);
    }

    @DeleteMapping("/candidate")
    @ApiOperation("删除候选人账号")
    public Result deleteCandidate(@RequestParam(value = "userId") Long userId) {
        if (userId == null) {
            return Result.fail("userId is null");
        }
        userBaseInfoService.deleteByIds(Lists.newArrayList(userId));
        return Result.ok();
    }

    @PostMapping("/batch/delete/candidate")
    @ApiOperation("批量删除候选人账号")
    @LogRecordAnnotation(menu = "管理中心-用户中心-用户列表-员工账号管理", category = "批量删除", success = "批量删除了数据")
    public Result batchDeleteCandidate(@RequestBody List<Long> userIds) {
        userBaseInfoService.deleteByIds(userIds);
        return Result.ok();
    }

    @ApiOperation("用户信息删除")
    @GetMapping("/delete")
    public Result<Boolean> delete(@RequestParam("empId") String empId) {
        userBaseInfoService.softDeleteByEmpId(empId);
        return Result.ok(true);
    }

    @ApiOperation("用户信息查询")
    @GetMapping("/info")
    public Result<UserBaseInfoVo> getUserByEmpId(@RequestParam("empId") Long empId) {
        UserBaseInfoDo userBaseInfo = userBaseInfoService.getByEmpId(empId);
        return Result.ok(ObjectConverter.convert(userBaseInfo, UserBaseInfoVo.class));
    }

    @DeleteMapping
    @ApiOperation("根据用户ID删除用户信息")
    @LogRecordAnnotation(menu = "管理中心-用户中心-用户列表-员工账号管理", success = "删除了{{#name}}", category = "删除")
    public Result deleteUsers(@RequestParam("userIds") String userIds) {
        List<Long> collect = Arrays.asList(userIds.split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
        List<UserBaseInfoDo> infoDos = userBaseInfoService.getByUserIds(collect);
        StringBuilder builder = new StringBuilder();
        for (UserBaseInfoDo infoDo : infoDos) {
            builder.append(infoDo.getUserName()).append("、");
        }
        if (builder.length() > 0) {
            builder.deleteCharAt(builder.length() - 1);
        }
        LogRecordContext.putVariable("name", builder.toString());
        userBaseInfoService.deleteByUserIds(userIds);
        return Result.ok("success");
    }

    @PostMapping("/deleteByEmpId")
    @ApiOperation("根据empId删除员工信息")
    public Result deleteUserByEmpId(@RequestParam String empId, @RequestParam String tenantId) {
        userBaseInfoService.deleteByEmpId(empId, tenantId);
        return Result.ok("success");
    }

    @ApiOperation(value = "用户退出登录2.0")
    @DeleteMapping(value = "/account/logout")
    public Result<String> accountLogout(HttpServletRequest request, HttpServletResponse response) throws Exception {
        userAppService.logout();
        if ("Redirect".equals(logoutType)) {
            return Result.ok(redirectLogoutPage);
            //response.sendRedirect(redirectLogoutPage);
        }
        return Result.success();
    }

    @ApiOperation(value = "saml sp metadata")
    @GetMapping(value = "/saml/sp/metadata")
    public void getSpMetadata(HttpServletRequest request, HttpServletResponse response) throws Exception {
        samlService.getSpMetadata(request, response);
    }

    @ApiOperation(value = "saml sso login")
    @ApiImplicitParam(name = "type", dataType = "string", example = "web")
    @GetMapping({"/saml/sso/{type}", "/saml/sso"})
    public void samlLogin(HttpServletRequest request, HttpServletResponse response, @PathVariable(value = "type", required = false) String type) throws Exception {
        samlService.samlLogin(request, response, type);
    }

    @ApiOperation("单点登出")
    @GetMapping({"/saml/slo/{type}", "/saml/slo"})
    public void samlSLO(@PathVariable(name = "type", required = false) String type, HttpServletRequest request, HttpServletResponse response) {
        samlService.samlLogout(request, response);
    }

    @ApiOperation(value = "saml slo login callback")
    @GetMapping(value = "/saml/sso/logout")
    public void samlLogoutCallback(HttpServletRequest request, HttpServletResponse response) throws Exception {
        samlService.logoutCallback(request, response);
    }

    @ApiOperation(value = "saml sso login callback")
    @PostMapping(value = "/saml/sso/callback")
    public void samlCallback(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            samlService.callback(request, response);
        } catch (Exception e) {
            log.error("saml sso login failed: {}, redirect to page: {}", e.getMessage(), errorPage);
            response.sendRedirect(errorPage);
        }
    }

    @ApiOperation(value = "saml sso test")
    @GetMapping(value = "/saml/sso/test")
    public void samlLoginTest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("/saml/sso/test url is " + request.getRequestURL());
    }

    @ApiOperation("员工停用")
    @GetMapping("/stop")
    public Result stopUser(@RequestParam("empId") String empId) {
        userBaseInfoService.stop(empId);
        return Result.ok("员工停用成功");
    }

    @ApiOperation("员工账号解锁")
    @GetMapping("/unlock")
    @LogRecordAnnotation(menu = "管理中心-用户中心-用户列表-员工账号管理", category = "解锁", success = "解锁了员工账号")
    public Result unlockUser(@RequestParam("empId") String empId) {
        userBaseInfoService.unlock(empId);
        return Result.ok();
    }

    @ApiOperation(value = "新增用户")
    @PostMapping(value = "/add")
    public Result<Boolean> addUser(@RequestBody UserImportDto user) throws Exception {
        val processId = UUID.randomUUID().toString();
        ImportExcelProcessDto processDto = new ImportExcelProcessDto();
        processDto.setProcessUUid(processId);
        processDto.setTotal(1);
        val importOperationLink = new ImportCheckMustFiled(processId)
                .appendNext(new ImportCheckAccountIsExist(processId)
                        .appendNext(new ImportCheckEmpInfo(processId)
                                .appendNext(new ImportCreateAccount(processId)
                                        .appendNext(new ImportAuthorization(processId)))));
        val error = FastjsonUtil.convertObject(user, UserImportErrorDto.class);
        try {
            SpringUtil.getBean(ImportService.class)
                    .importOne(importOperationLink, user, processDto, error);
            return Result.ok(true);
        } catch (Exception e) {
            log.error("import account occur error", e);
            throw e;
        }
    }

}
