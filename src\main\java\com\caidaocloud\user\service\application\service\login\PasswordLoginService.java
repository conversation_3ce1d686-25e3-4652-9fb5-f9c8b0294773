package com.caidaocloud.user.service.application.service.login;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.excption.ErrorMessage;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.constant.MsgCodeConstant;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.utils.AESUtils;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.PwdRuleDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.infrastructure.util.LangUtil;
import com.caidaocloud.user.service.interfaces.dto.AccountLoginDto;
import com.caidaocloud.user.service.interfaces.granter.GrantType;
import com.googlecode.totallylazy.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 账号密码登录
 */
@Slf4j
@Component
public class PasswordLoginService implements IAccountLoginService {

    /*AES秘钥*/
    @Value("${aes.secret_key:QofA168erYjs6Cwr}")
    private String AES_SECRET_KEY;

    @Autowired
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Autowired
    private PwdRuleDomainService pwdRuleDomainService;

    @Autowired
    private PasswordHelper passwordHelper;
    
    @Resource
    private CacheService cacheService;

    @Override
    public GrantType getGrantType() {
        return GrantType.PASSWORD_TOKEN_GRANT_TYPE;
    }

    @Value("${platform.default.entry:ghFoasFr8t7e9e!!}")
    private String platformPassword;

    @Value("${sso.enable:false}")
    private Boolean ssoEnableFlag;

    @Value("${tenantOfWorkNoAsAccount:}")
    private String tenantOfWorkNoAsAccount;

    @Override
    public List<UserBaseInfoDo> checkAndGetUser(AccountLoginDto loginDto) {
        beforeGrant(loginDto);

        if ("caidao-platform-admin".equals(loginDto.getAccount())) {
            if (loginDto.getPassword().equals(platformPassword)) {
                val user = new UserBaseInfoDo();
                user.setAccount("caidao-platform-admin");
                user.setAccountId(-1l);
                user.setUserId(-1l);
                user.setTenantId(null);
                user.setStatus(1);
                return Lists.list(user);
            }
            else {
                throw new ServerException(LangUtil.getMsg(MsgCodeConstant.WRONG_ACCOUNT_OR_PASSWORD));
            }
        }else if (ssoEnableFlag) {
            throw ServerException.globalException(ErrorMessage.fromCode("caidao.exception.error_60025"));
        }

        List<UserBaseInfoDo> userList = null;
        List<AccountBaseInfoDo> accountList = null;

        if(StringUtils.isNotEmpty(tenantOfWorkNoAsAccount)){
            userList = userBaseInfoDomainService.selectListByWorkNo(tenantOfWorkNoAsAccount, loginDto.getAccount());
            if(!userList.isEmpty()){
                accountList = accountBaseInfoDomainService.getListByIds(userList.stream().map(it->it.getAccountId()).collect(Collectors.toList()));
            }
        }
        if(CollectionUtils.isEmpty(userList)){
            // 账号查询并检查
            accountList = accountBaseInfoDomainService.getAndCheckAccountList(loginDto.getAccount(), GrantType.PASSWORD_TOKEN_GRANT_TYPE);

            // 用户查询并检查
            List<Long> accountIds = accountList.stream().map(AccountBaseInfoDo::getAccountId).collect(Collectors.toList());
            userList = userBaseInfoDomainService.getAndCheckUserList(accountIds);
        }
        // 只匹配到一个用户
        boolean singleUser = userList.size() == 1;
        if (singleUser) {
            // 账号已停用或已锁定判断
            long normalCount = userList.stream().filter(o -> AccountStatusEnum.NORMAL.getIndex().equals(o.getStatus())).count();
            // 账号已停用或已锁定，请联系管理员
            PreCheck.preCheckArgument(normalCount == 0, LangUtil.getMsg(MsgCodeConstant.ACCOUNT_LOCKED_OR_DISABLED));
        }

        // 密码检查，匹配到多个账号时，只要密码和任意账号匹配则认为登录成功
        boolean checkPwdSuccess = false;
        for (AccountBaseInfoDo account : accountList) {
            if (doCheckPassword(loginDto.getPassword(), account)) {
                checkPwdSuccess = true;
                break;
            }
        }

        if (!checkPwdSuccess) {
            // 密码错误
            // 当账号只绑定一个租户时需校验用户状态，需检查账号是否锁定（使用登录时密码错误超过次数限制将被锁定规则）
            if (singleUser) {
                pwdRuleDomainService.checkUserIsLockAfterLoginFail(userList.get(0));
            }
            // 账号或密码错误
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.WRONG_ACCOUNT_OR_PASSWORD));
        }
        return userList;
    }

    /**
     * 密码校验
     * @param password
     * @param accountBaseInfo
     * @return
     */
    private boolean doCheckPassword(String password, AccountBaseInfoDo accountBaseInfo) {
        if (StringUtils.isEmpty(accountBaseInfo.getSalt())) {
            // 兼容Sha密码加密规则
            if (passwordHelper.matchesBySHA(password, accountBaseInfo.getPassword())) {
                pwdRuleDomainService.cleanErrorCountCache(accountBaseInfo.getAccountId());
                return true;
            }
        } else {
            // 2.0 密码校验
            if (passwordHelper.matches(password, accountBaseInfo.getSalt(), accountBaseInfo.getPassword())) {
                pwdRuleDomainService.cleanErrorCountCache(accountBaseInfo.getAccountId());
                return true;
            }
            // 兼容1.0 密码生成规则
            String account = accountBaseInfo.getAccount();
            if (StringUtils.isNotBlank(accountBaseInfo.getAccountLoginPrefix())) {
                int startLength = accountBaseInfo.getAccountLoginPrefix().length();
                account = accountBaseInfo.getAccount().substring(startLength);
            }
            if (passwordHelper.matches(password, account + accountBaseInfo.getSalt(), accountBaseInfo.getPassword())) {
                pwdRuleDomainService.cleanErrorCountCache(accountBaseInfo.getAccountId());
                return true;
            }
        }
        return false;
    }

    private void beforeGrant(AccountLoginDto loginDto) {
        if (StringUtils.isEmpty(loginDto.getAccount()) || StringUtils.isEmpty(loginDto.getPassword())) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.USERNAME_OR_PWD_EMPTY));
        }

        // 账号、密码 AES 解密
        if (loginDto.isEncrypt()) {
            String account = loginDto.getAccount();
            String password = loginDto.getPassword();
            AESUtils mAESAesUtils = new AESUtils(AES_SECRET_KEY);
            try {
                account = mAESAesUtils.decryptData(account);
                password = mAESAesUtils.decryptData(password);

                loginDto.setAccount(account);
                loginDto.setPassword(password);
            } catch (Exception e) {
                log.error("PasswordLoginService.beforeGrant err,{}", e.getMessage(), e);
            }
        }
    }

    @Override
    public boolean checkPasswordIfInvalid(Long tenantId, Long accountId) {
        return pwdRuleDomainService.checkPasswordIfInvalid(tenantId, accountId);
    }

    @Override
    public boolean checkIfChangePassword(Long tenantId, Long accountId) {
        String key = String.format("change_pwd_account:%s", accountId);
        boolean exist = cacheService.containsKey(key);
        if(exist)
            return true;
        return pwdRuleDomainService.checkIfChangePassword(tenantId, accountId);
    }

    public Long verify(String pass) {
        AccountBaseInfoDo account = loadAccountInfo();
        if (!doCheckPassword(pass, account)) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ORIGINAL_PASSWORD_ERROR));
        }
        return account.getAccountId();
    }

    public UserBaseInfoDo loadBaseUserInfo() {
        Long userId = SecurityUserUtil.getSecurityUserInfo().getUserId();
        Optional<UserBaseInfoDo> userInfo = userBaseInfoDomainService.getByUserId(userId);
        if (!userInfo.isPresent()) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));
        }
        return userInfo.get();
    }

    public AccountBaseInfoDo loadAccountInfo() {
        Long userId = SecurityUserUtil.getSecurityUserInfo().getUserId();
        Optional<UserBaseInfoDo> userInfo = userBaseInfoDomainService.getByUserId(userId);
        if (!userInfo.isPresent()) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.USER_NOT_EXIST));
        }
        Long accountId = userInfo.get().getAccountId();
        AccountBaseInfoDo account = accountBaseInfoDomainService.getAccountByAccountId(accountId);
        if (account == null) {
            throw new ServerException(LangUtil.getMsg(MsgCodeConstant.ACCOUNT_NOT_EXIST));
        }
        return account;
    }
}
