package com.caidaocloud.user.service.application.foreign.service;


import com.caidaocloud.user.service.application.enums.SmsCodeType;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;

public interface IForeignSmsService {
    ConcurrentHashMap<String, IForeignSmsService> smsManager = new ConcurrentHashMap<>();

    @PostConstruct
    default void register() {
        String type = getSmsType();
        smsManager.put(type, this);
    }

    String getSmsType();

    /**
     * 发送短信验证码
     *
     * @param mobile
     * @param smsCode
     * @param smsCodeType
     * @return
     */
    boolean sendMessageCode(String mobile, String smsCode, SmsCodeType smsCodeType);

    boolean sendMessageCode(String mobile, String code, String smsCode, SmsCodeType smsCodeType);
}
