package com.caidaocloud.user.service.application.service.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.user.service.application.enums.SmsCodeType;
import com.caidaocloud.user.service.application.foreign.service.SmsSendFactory;
import com.caidaocloud.user.service.application.service.ISmsCodeCacheService;
import com.caidaocloud.user.service.application.service.ISmsService;
import com.caidaocloud.user.service.interfaces.dto.MobileCodeDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SmsService implements ISmsService {
    @Autowired
    private ISmsCodeCacheService smsCodeCacheService;
    @NacosValue("${caidaocloud.sms.fixedVerifyCode:}")
    private String fixedVerifyCode;

    @Override
    public boolean sendMessageCode(String mobile, SmsCodeType smsCodeType) {
        return sendMessageCode(mobile, "+86", smsCodeType);
    }

    @Override
    public boolean sendMessageCode(String mobile, String code, SmsCodeType smsCodeType) {
        try {
            // 随机生成验证码
            String smsCode = smsCodeCacheService.generateSmsCode();
            boolean result = SmsSendFactory.getSmsSendService().sendMessageCode(mobile, code, smsCode, smsCodeType);
            if (result) {
                smsCodeCacheService.saveSmsCode(mobile, smsCode, 60 * 5, smsCodeType);
            }
        } catch (Exception e) {
            log.error("mobile={}, code={}, sendMessageCode:{},", mobile, code, e.getMessage(), e);
        }
        return true;
    }

    @Override
    public boolean verify(MobileCodeDto dto) {
        if(StringUtils.isNotEmpty(fixedVerifyCode)){
            // 如果开启了固定验证码模式，则直接校验
            return fixedVerifyCode.equals(dto.getVerifyCode());
        }

        String smsCode = smsCodeCacheService.getSmsCode(dto.getMobile(), dto.getSmsCodeType());
        if (StringUtils.isEmpty(smsCode)) {
            throw new ServerException("验证码已过期，请重新获取验证码");
        }
        return smsCode.equals(dto.getVerifyCode());
    }
}
