package com.caidaocloud.user.service.interfaces.dto;

import com.caidaocloud.user.service.interfaces.granter.GrantType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/6/11
 */
@Data
public class IdentityVerifyDto {

	@ApiModelProperty("账号，可为手机号")
	private String account;

	@ApiModelProperty("令牌，可为密码或者验证码")
	private String pass;

	@ApiModelProperty("令牌验证类型")
	private GrantType type;
}
