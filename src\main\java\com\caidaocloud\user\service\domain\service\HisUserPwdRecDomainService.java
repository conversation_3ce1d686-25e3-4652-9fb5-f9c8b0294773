package com.caidaocloud.user.service.domain.service;

import com.caidaocloud.user.service.domain.entity.HisUserPwdRecDo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class HisUserPwdRecDomainService {
    @Autowired
    private HisUserPwdRecDo hisUserPwdRecDo;

    public void syncSave(List<HisUserPwdRecDo> dataList) throws Exception {
        hisUserPwdRecDo.syncSave(dataList);
    }

    public List<HisUserPwdRecDo> getPageListByAccountId(Long tenantId, Long accountId, long current, long size) {
        return hisUserPwdRecDo.getPageListByAccountId(tenantId, accountId, current, size);
    }
}
