package com.caidaocloud.user.service.infrastructure.repository.po;

import lombok.Data;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.Indexes;
import org.springframework.stereotype.Component;

/**
 * @Author: Max
 * @Desc: 登录日志
 * @Date: 1/6/2021 2:00 PM
 * 4
 */
@Entity("LoginLogInfo")
@Indexes(@Index(fields = @Field("id")))
@Component
@Data
public class LoginLogInfo {
}
