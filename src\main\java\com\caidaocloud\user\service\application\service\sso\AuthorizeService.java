package com.caidaocloud.user.service.application.service.sso;

import com.caidaocloud.excption.ServerException;
import com.caidaocloud.factory.bean.TarBeanFactory;
import com.caidaocloud.user.service.domain.entity.UserSsoInfo;
import com.caidaocloud.user.service.domain.service.AuthorizeDomainService;
import com.caidaocloud.user.service.interfaces.vo.LoginConfigVo;
import com.caidaocloud.user.service.interfaces.vo.LoginVo;
import com.caidaocloud.util.ObjectConverter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class AuthorizeService {
    @Resource
    private AuthorizeDomainService authorizeDomainService;

    public LoginVo authorize(Long appkey, String appCode){
        UserSsoInfo ssoInfo = authorizeDomainService.getSsoInfoById(appkey);
        if(null == ssoInfo || !appCode.equals(ssoInfo.getTenantCode())){
            new ServerException("auth err,errcode 10000");
        }

        return TarBeanFactory.tarBean(SsoAuthService.class, String.valueOf(ssoInfo.getSsoType()))
                .authorize(ssoInfo);
    }

    public LoginConfigVo getLoginConfig(String appCode){
        UserSsoInfo ssoInfo = authorizeDomainService.getSsoInfoByCode(appCode);
        if(null == ssoInfo){
            return new LoginConfigVo();
        }

        return ObjectConverter.convert(ssoInfo, LoginConfigVo.class);
    }
}
