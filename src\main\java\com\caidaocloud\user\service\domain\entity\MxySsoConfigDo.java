package com.caidaocloud.user.service.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.caidaocloud.user.service.domain.repository.IMxySsoConfigRepository;
import com.caidaocloud.user.service.infrastructure.repository.po.MxySsoConfigPo;
import com.caidaocloud.user.service.infrastructure.repository.po.TenantPo;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
@Data
public class MxySsoConfigDo extends BaseEntity {
    private String tenantId;
    private String tenantName;
    private String corpId;
    private String corpSecret;
    private String token;
    private String mxyKey;

    @Resource
    private IMxySsoConfigRepository mxySsoConfigRepository;

    public MxySsoConfigDo getMxySsoConfigById(String tenantId) {
        MxySsoConfigPo mxySsoConfigPo = mxySsoConfigRepository.getById(tenantId);
        return ObjectConverter.convert(mxySsoConfigPo, MxySsoConfigDo.class);
    }
}
