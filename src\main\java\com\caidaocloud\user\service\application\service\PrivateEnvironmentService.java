package com.caidaocloud.user.service.application.service;

import com.caidaocloud.user.service.domain.entity.PrivateEnvironment;
import com.caidaocloud.user.service.domain.enums.PrivateEnvironmentType;
import com.caidaocloud.user.service.domain.service.PrivateEnvironmentDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PrivateEnvironmentService {

    @Autowired
    PrivateEnvironmentDomainService privateEnvironmentDomainService;

    public List<PrivateEnvironment> list(PrivateEnvironmentType type){
        return privateEnvironmentDomainService.list(type);
    }
}
