
package com.caidaocloud.user.service.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.UserBaseInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserBaseInfoMapper extends BaseMapper<UserBaseInfo> {
    int insertSelective(UserBaseInfo record);

    int updateByPrimaryKeySelective(UserBaseInfo record);

    int insertBatch(@Param("records") List<UserBaseInfo> records);

    int updateBatch(@Param("records") List<UserBaseInfo> records);
}
