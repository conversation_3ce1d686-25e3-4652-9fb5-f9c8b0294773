package com.caidaocloud.user.service.application.feign.fallback;

import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.user.service.application.dto.auth.AuthSubjectAndRoleDto;
import com.caidaocloud.user.service.application.dto.auth.AuthSubjectRoleDto;
import com.caidaocloud.user.service.application.dto.operate.AuthImportDto;
import com.caidaocloud.user.service.application.dto.operate.AuthImportMsgDto;
import com.caidaocloud.user.service.application.feign.IAuthFeign;
import com.caidaocloud.web.Result;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/5/12
 **/
public class AuthFeignFallback implements IAuthFeign {


    @Override
    public Result deleteSubjects(String subjectIds) {
        return Result.fail();
    }

    @Override
    public Result authAdminSubjects(List<Long> subjectIds) {
        return Result.fail();
    }

    @Override
    public Result<List<String>> getResourceUrlListBySubjectId(Long subjectId, String accessToken) {
        return Result.fail();
    }

    @Override
    public Result<List<AuthImportMsgDto>> authorizationToUser(List<AuthImportDto> authToSubjectList) {
        return Result.fail();
    }

    @Override
    public Result<List<AuthImportMsgDto>> refreshAuthorizationToUser(List<AuthImportDto> authToSubjectList) {
        return Result.fail();
    }

    @Override
    public Result<List<AuthSubjectAndRoleDto>> getRoleNameBySubjectIds(String subjectIds) {
        return Result.fail();
    }

    /**
     * 管理员初始化
     *
     * @param tenantId
     * @param userId
     * @return
     */
    @Override
    public Result<String> initAdmin(String tenantId, String userId) {
        return Result.fail();
    }

    @Override
    public Result<String> initConfig(String tenantId, String userId) {
        return Result.fail();
    }

    @Override
    public Result empChangeRole(AuthSubjectRoleDto subjectRoleDto) {
        return Result.fail();
    }

    @Override
    public Result<List<KeyValue>> getAllEnabledRole() {
        return Result.fail();
    }

}
