package com.caidaocloud.user.service;

import com.caidaocloud.user.service.infrastructure.repository.po.TenantPo;
import com.caidaocloud.user.service.domain.repository.ITenantRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/8/2021 1:58 PM
 * 4
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class TenantTest {
    @Autowired
    private ITenantRepository tenantRepository;

    @Test
    public void save_test() {
        List<TenantPo> list = new ArrayList<>();
        TenantPo tenant = new TenantPo();
        tenant.setName("腾讯HR助手");
        tenant.setDescription("腾讯HR助手");
        tenant.setCreatedTime(System.currentTimeMillis());
        list.add(tenant);
        tenantRepository.save(list, "");
    }

    @Test
    public void saveTest() {
        List<TenantPo> list = new ArrayList<>();
        TenantPo tenant = new TenantPo();
        tenant.setName("科锐国际");
        tenant.setDescription("科锐国际");
        tenant.setCreatedTime(System.currentTimeMillis());
        tenant.setLoginConfig("{\"headers\":{\"secret\":\"bbb\",\"key\":\"aaaaa\",\"Content-Type\":\"application/json\"},\"loginType\":\"http\",\"loginUrl\":\"http://dev.caidaocloud.com/api/career/user/v1/login\",\"params\":{\"extMap\":{\"userId\":\"#user.userid\"},\"password\":\"#login.password\",\"terminal\":\"0\",\"account\":\"#login.account\"},\"successMark\":\"\\\"success\\\": true\"}");
        tenant.setCorpid(11646L);
        list.add(tenant);
        tenantRepository.save(list, "");
    }
}
