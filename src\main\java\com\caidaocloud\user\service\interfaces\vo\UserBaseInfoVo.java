package com.caidaocloud.user.service.interfaces.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("用户信息")
public class UserBaseInfoVo {
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("账号ID")
    private Long accountId;
    @ApiModelProperty("用户统一注册账号")
    private String account;
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("用户姓名")
    private String userName;
    @ApiModelProperty("性别")
    private Integer sex;
    @ApiModelProperty("头像")
    private String headPortrait;
    @ApiModelProperty("用户状态：1 正常 2 停用 3 锁定")
    private Integer status;
    @ApiModelProperty("是否为默认用户")
    private Boolean ifDefault;
    @ApiModelProperty("员工ID")
    private Long empId;
    @ApiModelProperty("集团公司ID")
    private Long corpId;
    @ApiModelProperty("扩展信息")
    private String extInfo;
    @ApiModelProperty("是否候选人账号")
    private Boolean onboarding;
    @ApiModelProperty("钉钉userId")
    private String dingUserId;
}
