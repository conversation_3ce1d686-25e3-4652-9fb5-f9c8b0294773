package com.caidaocloud.user.service.domain.repository;

import com.caidaocloud.user.service.domain.entity.HisUserPwdRecDo;

import java.util.List;

public interface IHisUserPwdRecRepository {

    void insertBatch(List<HisUserPwdRecDo> dataList);

    void insert(HisUserPwdRecDo data);

    void update(HisUserPwdRecDo data);

    int insertSelective(HisUserPwdRecDo record);

    int updateByPrimaryKeySelective(HisUserPwdRecDo record);

    void delete(List<Long> ids);

    List<HisUserPwdRecDo> getListByAccountId(Long tenantId, Long accountId);

    List<HisUserPwdRecDo> getPageListByAccountId(Long tenantId, Long accountId, long current, long size);
}
