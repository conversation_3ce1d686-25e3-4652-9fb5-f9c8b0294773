package com.caidaocloud.user.service.interfaces.facade;


import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.user.service.application.enums.DataOpEnum;
import com.caidaocloud.user.service.application.service.PwdRuleService;
import com.caidaocloud.user.service.domain.entity.PwdRuleDo;
import com.caidaocloud.user.service.interfaces.dto.SyncPwdRuleDto;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/user/pwdrule/v2")
@Api(value = "/api/user/pwdrule/v2", tags = "用户中心2.0-密码规则")
public class PwdRuleController {
    @Autowired
    private PwdRuleService pwdRuleService;

    @PostMapping("/sync")
    @ApiOperation("同步保存密码规则")
    public Result<Boolean> syncPwdrule(@RequestBody SyncPwdRuleDto dto) {
        try {
            PreCheck.preCheckArgument(dto == null, "Data is empty");
            PreCheck.preCheckArgument(dto.getPwdRuleId() == null, "密码规则ID为空");
            PreCheck.preCheckArgument(!(dto.getOp() == DataOpEnum.INSERT || dto.getOp() == DataOpEnum.UPDATE), "Op Field value can only be [INSERT UPDATE]");
            pwdRuleService.syncSave(new ArrayList<>(Arrays.asList(ObjectConverter.convert(dto, PwdRuleDo.class))));
        } catch (Exception e) {
            log.error("PwdRuleController.syncPwdrule error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }

    @PostMapping("/batchSync")
    @ApiOperation("批量同步保存密码规则")
    public Result<Boolean> batchSyncPwdrule(@RequestBody List<SyncPwdRuleDto> dtos) {
        try {
            PreCheck.preCheckArgument(CollectionUtils.isEmpty(dtos), "Data is empty");
            long nullCount = dtos.stream().filter(o -> o.getPwdRuleId() == null).count();
            PreCheck.preCheckArgument(nullCount > 0, "密码规则ID为空");
            long count = dtos.stream().filter(dto -> !(dto.getOp() == DataOpEnum.INSERT || dto.getOp() == DataOpEnum.UPDATE)).count();
            PreCheck.preCheckArgument(count > 0, "Op Field value can only be [INSERT UPDATE]");
            pwdRuleService.syncSave(ObjectConverter.convertList(dtos, PwdRuleDo.class));
        } catch (Exception e) {
            log.error("PwdRuleController.batchSyncPwdrule error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }
}
