package com.caidaocloud.user.service.interfaces.granter;

import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OneClickLoginTokenGranter extends AbstractTokenGranter {

    @Override
    protected Result beforeGrant(LoginDto loginDto) {
        return super.beforeGrant(loginDto);
    }

    @Override
    public Result grant(LoginDto loginDto) {
        return null;
    }

    @Override
    protected Result afterGrant(LoginOutDto loginOutDo) {
        return super.afterGrant(loginOutDo);
    }
}
