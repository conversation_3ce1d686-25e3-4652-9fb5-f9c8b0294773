package com.caidaocloud.user.service;

import com.caidaocloud.user.service.application.dto.operate.ImportExcelProcessDto;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.SignUtil;
import com.caidaocloud.vo.ImportExcelProcessVo;

public class DtoTest {

    public static void main(String[] args) {
        System.out.println(SignUtil.md5("123456"));



        ImportExcelProcessDto dto = new ImportExcelProcessDto();
        dto.setSuccessCount(2);
        dto.setTotal(2);
        dto.setCompleted(2);
        dto.setProcessUUid("1653565791191");
        dto.setNotDone(0);
        String json = FastjsonUtil.toJson(dto);
        System.out.println(json);
        ImportExcelProcessVo importExcelProcessVo = FastjsonUtil.toObject(json, ImportExcelProcessVo.class);
        System.out.println(importExcelProcessVo);
    }

}
