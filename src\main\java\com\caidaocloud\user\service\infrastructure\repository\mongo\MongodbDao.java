package com.caidaocloud.user.service.infrastructure.repository.mongo;

import com.mongodb.DBObject;

import java.util.List;

public interface MongodbDao<T> {

    /**
     * 查询类
     *
     * @param query          查询参数
     * @param collectionName 查询表名
     * @param t              要返回的实体名
     * @return List<T>
     */
    List<T> query(DBObject query, String collectionName, Class<T> t);

    List<T> queryAggregate(List<? extends DBObject> pipeline, String collectionName, Class<T> t);

    <E extends Object> List<E> query(DBObject query, DBObject projection, String collectionName, Class<E> t);

    void delete(DBObject query, String collectionName);

    void save(List<T> obj, String collectionName);

    void save(T obj, String collectionName);

    /**
     * 更新
     *
     * @param query          查询条件（用来查询需要更新的数据）
     * @param update         需要更新的值
     * @param upsert         如果查询出来没有匹配结果是否增加数据（默认false）
     * @param multi          如果查询出来多条，是否全部update(默认false)
     * @param collectionName 表名
     */
    void update(DBObject query, DBObject update, boolean upsert, boolean multi, String collectionName);

    List<T> queryWithPaging(DBObject query, DBObject projection, String tableName, Class<T> t, int pageNum, int pageSize);

    void createIndex(DBObject keys, String collectionName);

    void dropCollection(String collectionName);

    void createCollection(String collectionName);

    int queryCount(DBObject query, String collectionName);

    boolean collectionExisting(String collectionName);
}
