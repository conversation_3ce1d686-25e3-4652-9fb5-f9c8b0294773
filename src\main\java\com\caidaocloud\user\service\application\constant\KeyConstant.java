package com.caidaocloud.user.service.application.constant;

/**
 * <AUTHOR>
 * @date 2022/5/19
 **/
public class KeyConstant {

    /**
     * 用户导入错误key前缀
     */
    public final static String IMPORT_USER_ERROR_KEY = "_IMPORT_USER_ERRO";

    /**
     * 用户导入进度前缀
     */
    public final static String IMPORT_USER_PROCESS = "_IMPORT_USER_PROCESS";

    /**
     * 导入进度key
     *
     * @param processInstId
     * @return
     */
    public static String getImportProcessKey(String processInstId) {
        return String.format("%s%s", processInstId, KeyConstant.IMPORT_USER_PROCESS);
    }

    /**
     * 导入失败key
     * @param processInstId
     * @return
     */
    public static String getImportErrorKey(String processInstId) {
        return String.format("%s%s", processInstId, KeyConstant.IMPORT_USER_ERROR_KEY);
    }

}
