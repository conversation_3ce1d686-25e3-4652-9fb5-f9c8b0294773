-- 方案一：
-- --租户表
-- 租户ID、公司名称、公司代码
-- --用户表
-- 用户ID、手机号、邮箱、密码、盐值、姓名
-- --用户租户关系表
-- 用户ID、租户ID、租户个性化字段（例如：工号）
--
--
-- 方案二：
-- --租户表
-- 租户ID、公司名称、公司代码
-- --账号表
-- 账号ID、手机号、邮箱、密码、盐值
-- --用户表
-- 用户ID、账号ID，租户ID、姓名

--目前采用的是方案二
create table if not exists tenant_base_info
(
    tenant_id   bigint      not null,
    tenant_name varchar(50) not null,
    tenant_code varchar(50),
    logo        varchar(200),
    corp_id     bigint,
    corp_code   varchar(50),
    create_by   bigint      not null,
    create_time bigint      not null,
    update_by   bigint,
    update_time bigint,
    constraint pk_tenant_base_info_tenant_id primary key (tenant_id)
    );
comment on table tenant_base_info is '租户基本信息表';
comment on column tenant_base_info.tenant_id is '租户ID';
comment on column tenant_base_info.tenant_name is '租户名称';
comment on column tenant_base_info.tenant_code is '租户代码';
comment on column tenant_base_info.logo is '租户logo';
comment on column tenant_base_info.corp_id is '集团公司ID';
comment on column tenant_base_info.corp_code is '集团公司唯一编码';

create table if not exists account_base_info
(
    account_id  bigint  not null,
    account_login_prefix varchar(50),
    account     varchar(50),
    mob_num     varchar(50),
    email       varchar(50),
    password    varchar(100),
    salt        varchar(50),
    gesture     varchar(50),
    status      integer not null default 0,
    reg_type    varchar(50),
    create_by   bigint  not null,
    create_time bigint  not null,
    update_by   bigint,
    update_time bigint,
    constraint pk_account_base_info_account_id primary key (account_id)
    );
comment on table account_base_info is '账号基本信息表';
comment on column account_base_info.account_login_prefix is '使用用户统一注册账号登录时的前缀，例如：caidao-，登录时输入：前缀-账号进行登录';
comment on column account_base_info.account is '用户统一注册账号，例如统一工号';
comment on column account_base_info.mob_num is '注册手机号';
comment on column account_base_info.email is '注册邮箱';
comment on column account_base_info.password is '密码';
comment on column account_base_info.salt is '盐值';
comment on column account_base_info.gesture is '手势密码';
comment on column account_base_info.status is '账号状态：0 有效 1 锁定';
comment on column account_base_info.reg_type is '注册方式、注册类型';

create table if not exists user_base_info
(
    user_id       bigint      not null,
    account_id    bigint      not null,
    account       varchar(50),
    tenant_id     bigint      not null,
    user_name     varchar(50) not null,
    sex           integer,
    head_portrait varchar(200),
    status        integer     not null default 0,
    if_default    boolean     not null default false,
    emp_id        bigint,
    corp_id       bigint,
    ext_info      varchar(500),
    create_by     bigint      not null,
    create_time   bigint      not null,
    update_by     bigint,
    update_time   bigint,
    constraint pk_user_base_info_user_id primary key (user_id)
    );
comment on table user_base_info is '用户基本信息表';
comment on column user_base_info.account_id is '账号信息ID';
comment on column user_base_info.account is '用户统一注册账号，例如统一工号';
comment on column user_base_info.tenant_id is '所属租户ID';
comment on column user_base_info.user_name is '用户姓名';
comment on column user_base_info.sex is '性别';
comment on column user_base_info.head_portrait is '头像';
comment on column user_base_info.status is '用户状态：0 有效 1 锁定';
comment on column user_base_info.if_default is '是否为默认登录用户';
comment on column user_base_info.emp_id is '此用户关联的员工ID';
comment on column user_base_info.corp_id is '集团公司ID';
comment on column user_base_info.ext_info is '用户扩展信息';

create table if not exists pwd_rule
(
    pwd_rule_id       bigint not null,
    tenant_id         bigint not null,
    is_first_rule     boolean default false,
    first_rule        varchar(20),
    first_length      integer,
    is_valid          boolean default false,
    is_pwd_change     boolean default false,
    pwd_len1          integer,
    pwd_len2          integer,
    pwd_complexity    varchar(20),
    not_pwd_same_num  integer,
    pwd_valid_time    integer,
    pwd_expires_day   integer default 0,
    lock_account_num  integer default 5,
    lock_account_time integer,
    auto_unlock_time  integer,
    corp_id           bigint,
    create_by         bigint not null,
    create_time       bigint not null,
    update_by         bigint,
    update_time       bigint,
    constraint pk_pwd_rule_rule_id primary key (pwd_rule_id)
    );
comment on table pwd_rule is '密码规则表';
comment on column pwd_rule.pwd_rule_id is '密码规则ID';
comment on column pwd_rule.tenant_id is '租户ID';
comment on column pwd_rule.is_first_rule is '是否启用初始、重置密码生成规则';
comment on column pwd_rule.first_rule is '初始密码生成规则的复杂度:1、数字 2、大写字母 3、小写字母 4 特殊字符 5、手机号码后六位 6、身份证后六位';
comment on column pwd_rule.first_length is '初始密码生成规则的密码长度';
comment on column pwd_rule.is_valid is '是否启用修改密码规则';
comment on column pwd_rule.is_pwd_change is '使用初始密码登陆后是否强制修改密码';
comment on column pwd_rule.pwd_len1 is '密码长度（最小长度）';
comment on column pwd_rule.pwd_len2 is '密码长度（最大长度）';
comment on column pwd_rule.pwd_complexity is '密码复杂度：1.包含数字、2.大写字母、3.小写字母 4.特殊字符';
comment on column pwd_rule.not_pwd_same_num is '不可与前多少次历史密码相同：自定义次数';
comment on column pwd_rule.pwd_valid_time is '密码期限：自定义密码有效时长，单位为天';
comment on column pwd_rule.pwd_expires_day is '密码到期前提醒天数';
comment on column pwd_rule.lock_account_num is '登录时密码错误超过次数限制将被锁定：默认5次';
comment on column pwd_rule.lock_account_time is '超过锁定次数后的锁定时间：自定义密码锁定时间，单位为分钟';
comment on column pwd_rule.auto_unlock_time is '自动解锁时间，锁定用户时更新锁定时间到用户表上，在根据 锁定时间判断是否可解锁';
comment on column pwd_rule.corp_id is '集团公司ID';