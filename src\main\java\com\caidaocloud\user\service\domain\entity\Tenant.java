package com.caidaocloud.user.service.domain.entity;

import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.user.service.infrastructure.repository.po.TenantPo;
import com.caidaocloud.user.service.domain.repository.ITenantRepository;
import com.caidaocloud.util.ObjectConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: Max
 * @Desc: 租户实体类，采用充血模型
 * @Date: 1/8/2021 1:35 PM
 * 4
 */
@Slf4j
@Service
@Data
public class Tenant {
    private String id;
    private Long corpid;
    private String name;
    private String thirdPart;
    private String loginConfig;
    private String description;
    private long createdTime;
    private long updatedTime;

    @Autowired
    private ITenantRepository tenantRepository;

    @Autowired
    private ISessionService sessionService;

    public void save(List<Tenant> list) {
        List<TenantPo> poList = ObjectConverter.convertList(list, TenantPo.class);
        tenantRepository.save(poList, sessionService.getTenantId());
    }

    public Tenant getTenantByCorpid(Long corpid){
        TenantPo tenantPo = tenantRepository.getTenantByCorpid(corpid);
        return ObjectConverter.convert(tenantPo, Tenant.class);
    }

    public Tenant getTenantByThirdPart(String thirdPart){
        TenantPo tenantPo = tenantRepository.getTenantByThirdPart(thirdPart);
        return ObjectConverter.convert(tenantPo, Tenant.class);
    }
}
