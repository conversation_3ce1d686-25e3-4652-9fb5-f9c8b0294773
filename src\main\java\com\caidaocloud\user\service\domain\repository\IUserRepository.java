package com.caidaocloud.user.service.domain.repository;

import com.caidaocloud.user.service.domain.entity.User;

import java.util.List;

/**
 * @Author: Max
 * @Desc:
 * @Date: 1/4/2021 3:58 PM
 *        4
 */
public interface IUserRepository {
    void save(List<User> list, String tenantId);

    User getByAccount(String account, String tenantId);

    User getByUserId(long userId, String tenantId);

    User getByTenantIdAndStaffid(String tenantId, String staffid);

    User updateClockType(User newUser);

    void updateLastLoginTime(User user);
}
