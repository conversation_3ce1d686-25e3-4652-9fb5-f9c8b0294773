package com.caidaocloud.user.service;

import com.caidaocloud.user.service.domain.entity.LoginLogDo;
import com.caidaocloud.user.service.domain.service.LoginLogDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 登录日志测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class LoginLogTest {

    @Autowired
    private LoginLogDomainService loginLogDomainService;

    @Test
    public void testRecordSuccessLogin() {
        try {
            // 测试记录成功登录
            loginLogDomainService.recordSuccessLoginAsync(
                    1L, // userId
                    "test-tenant", // tenantId
                    "testuser", // account
                    "测试用户", // userName
                    0, // loginPlatform (web)
                    0, // loginType (password)
                    0, // deviceType (web)
                    "*************", // ipAddress
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            log.info("成功登录日志记录测试完成");

            // 等待异步执行完成
            Thread.sleep(2000);

        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    public void testRecordFailLogin() {
        try {
            // 测试记录失败登录
            loginLogDomainService.recordFailLoginAsync(
                    "testuser", // account
                    "test-tenant", // tenantId
                    0, // loginPlatform (web)
                    0, // loginType (password)
                    0, // deviceType (web)
                    "*************", // ipAddress
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", // userAgent
                    "密码错误" // failReason
            );

            log.info("失败登录日志记录测试完成");

            // 等待异步执行完成
            Thread.sleep(2000);

        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    public void testCreateLoginLogDo() {
        try {
            // 测试创建成功登录日志实体
            LoginLogDo successLog = LoginLogDo.createSuccessLog(
                    1L, // userId
                    "test-tenant", // tenantId
                    "testuser", // account
                    "测试用户", // userName
                    0, // loginPlatform
                    0, // loginType
                    0, // deviceType
                    "*************", // ipAddress
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            log.info("成功登录日志实体创建完成：{}", successLog);

            // 测试创建失败登录日志实体
            LoginLogDo failLog = LoginLogDo.createFailLog(
                    "testuser", // account
                    "test-tenant", // tenantId
                    0, // loginPlatform
                    0, // loginType
                    0, // deviceType
                    "*************", // ipAddress
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", // userAgent
                    "密码错误" // failReason
            );

            log.info("失败登录日志实体创建完成：{}", failLog);

        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    public void testDatabaseStorage() {
        try {
            // 测试数据库存储 - 创建成功登录日志并保存
            LoginLogDo loginLogDo = LoginLogDo.createSuccessLog(
                    1001L, // userId
                    "tenant001", // tenantId
                    "testuser", // account
                    "测试用户", // userName
                    0, // loginPlatform
                    0, // loginType
                    0, // deviceType
                    "*************", // ipAddress
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" // userAgent
            );

            // 保存到数据库
            loginLogDo.save();

            log.info("数据库存储测试完成");

        } catch (Exception e) {
            log.error("数据库存储测试失败", e);
        }
    }
}
