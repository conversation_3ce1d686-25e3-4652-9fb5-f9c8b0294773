package com.caidaocloud.user.service.interfaces.dto;

import com.caidaocloud.user.service.interfaces.granter.GrantType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AccountLoginDto {

    @ApiModelProperty(value = "用户账号/手机号(验证码登录)", required = true)
    private String account;

    @ApiModelProperty(value = "密码/验证码(验证码登录)", required = true)
    private String password;

    @ApiModelProperty(value = "登录平台，0为web端，1为移动端, 2 第三方为web端，3 为第三方移动端")
    private Integer loginPlatform = 0;

    @ApiModelProperty(value = "登录方式，0为账号密码登录，1:为手机验证码登录,2为微信扫描登录，3为支付宝扫码登录，4为腾讯sso登录")
    private GrantType grantType = GrantType.PASSWORD_TOKEN_GRANT_TYPE;

    @ApiModelProperty(value = "登录设备类型web:0 android:1,ios:2")
    private Integer deviceType = 0;

    /**
     * 第三方平台编码
     */
    @ApiModelProperty(value = "第三方平台编码")
    private String thirdPart;

    /**
     * 第三方数据ID
     */
    @ApiModelProperty(value = "第三方数据ID")
    private String thirdId;

    /**
     * 扩展备用字段map
     */
    @ApiModelProperty(value = "扩展备用字段")
    private Map extMap;

    @ApiModelProperty(value = "一键登录的授权token")
    private String loginAccessToken;

    @ApiModelProperty(value = "本机号码校验认证授权码")
    private String loginAccessCode;

    @ApiModelProperty(value = "账号、密码  是否AES 解密，默认 false")
    private boolean encrypt;

    @ApiModelProperty("saml参数")
    private Map<String, List<String>> samlProps;

    private boolean enforceSecondLogin = false;
}
