alter table tenant_base_info
    add column if not exists deleted integer default 0 not null;
comment on column tenant_base_info.deleted is '删除状态 0 未删除 1 已删除';

alter table account_base_info
    add column if not exists deleted integer default 0 not null;
comment on column account_base_info.deleted is '删除状态 0 未删除 1 已删除';

alter table user_base_info
    add column if not exists deleted integer default 0 not null;
comment on column user_base_info.deleted is '删除状态 0 未删除 1 已删除';

alter table pwd_rule
    add column if not exists deleted integer default 0 not null;
comment on column pwd_rule.deleted is '删除状态 0 未删除 1 已删除';

alter table private_environment
    add column if not exists deleted integer default 0 not null;
comment on column private_environment.deleted is '删除状态 0 未删除 1 已删除';

alter table his_user_pwd_rec
    add column if not exists deleted integer default 0 not null;
comment on column his_user_pwd_rec.deleted is '删除状态 0 未删除 1 已删除';

create index if not exists idx_account_base_info_account on account_base_info (account);
create index if not exists idx_account_base_info_mob_num on account_base_info (mob_num);
create index if not exists idx_account_base_info_email on account_base_info (email);

create index if not exists idx_user_base_info_account_id on user_base_info (account_id);
create index if not exists idx_user_base_info_tenant_id on user_base_info (tenant_id);