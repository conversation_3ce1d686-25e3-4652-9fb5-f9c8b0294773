package com.caidaocloud.user.service.application.service;

import com.alibaba.fastjson.JSON;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.factory.bean.TarBeanFactory;
import com.caidaocloud.user.service.application.dto.LoginConfigDto;
import com.caidaocloud.user.service.domain.entity.LoginDo;
import com.caidaocloud.user.service.domain.entity.User;
import com.caidaocloud.user.service.domain.service.LoginDomainService;
import com.caidaocloud.user.service.interfaces.dto.LoginDto;
import com.caidaocloud.user.service.interfaces.dto.LoginOutDto;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 通过配置实现 http 登录
 * <AUTHOR>
 * @date 2021-05-31
 */
@Slf4j
@Service
public class UserLoginService {

    @Autowired
    private LoginDomainService loginDomainService;

    public LoginOutDto autoLogin(String loginConfig, LoginDto loginDto, User dbUser){
        LoginConfigDto loginConfigDto = JSON.parseObject(loginConfig, LoginConfigDto.class);

        LoginOutDto loginOutDo = doLogin(loginConfigDto, loginDto, dbUser);
        if (loginOutDo.getErrorCode() != ErrorCodes.NO_ERROR) {
            return loginOutDo;
        }

        LoginDo loginDo = ObjectConverter.convert(loginDto, LoginDo.class);
        loginOutDo = loginDomainService.getLoginOutDto(dbUser, loginDo);

        return loginOutDo;
    }

    private LoginOutDto doLogin(LoginConfigDto loginConfigDto, LoginDto loginDto, User dbUser){
        log.info("doLogin msg = {}", JSON.toJSONString(loginConfigDto));

        LoginOutDto loginOutDo = new LoginOutDto();
        loginOutDo.setErrorCode(ErrorCodes.UNKNOW_ERROR);

        ILoginService loginService = null;
        // 如果登录方式为空，或为不支持的登录方式，则登录失败
        boolean loginMode = null == loginConfigDto || StringUtil.isEmpty(loginConfigDto.getLoginType()) || null == (loginService = getLoginService(loginConfigDto.getLoginType().toLowerCase()));
        if(loginMode){
            log.error("Unsupported login type");
            return loginOutDo;
        }

        if(StringUtil.isEmpty(loginConfigDto.getLoginUrl())){
            log.error("Login Url Cannot be empty");
            return loginOutDo;
        }

        return loginService.doLogin(loginConfigDto, loginDto, dbUser);
    }

    private ILoginService getLoginService(String loginType){
        ILoginService service = TarBeanFactory.tarBean(ILoginService.class, loginType);
        return service;
    }

}
