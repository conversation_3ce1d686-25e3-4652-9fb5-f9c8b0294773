package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.user.service.application.enums.DataOpEnum;
import com.caidaocloud.user.service.application.service.TenantBaseInfoService;
import com.caidaocloud.user.service.domain.entity.NacosValueEntity;
import com.caidaocloud.user.service.domain.entity.TenantBaseInfoDo;
import com.caidaocloud.user.service.interfaces.dto.SyncTenantBaseInfoDto;
import com.caidaocloud.user.service.interfaces.dto.TenantBaseInfoDto;
import com.caidaocloud.user.service.interfaces.vo.TenantBaseInfoVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.Result;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.nio.channels.ScatteringByteChannel;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/user/tenant/v2")
@Api(value = "/api/user/tenant/v2", tags = "用户中心2.0-租户管理")
public class TenantBaseInfoController {
    @Autowired
    private TenantBaseInfoService tenantBaseInfoService;
    @Resource
    private NacosValueEntity nacosValueEntity;

    @PostMapping("/sync")
    @ApiOperation("同步保存租户信息")
    public Result<Boolean> syncTenant(@RequestBody SyncTenantBaseInfoDto dto) {
        try {
            PreCheck.preCheckArgument(dto == null, "Data is empty");
            PreCheck.preCheckArgument(dto.getTenantId() == null, "租户ID为空");
            if (dto.getOp() == DataOpEnum.DELETE) {
                tenantBaseInfoService.softDeleteByIds(new ArrayList<>(Arrays.asList(dto.getTenantId())));
            } else if (dto.getOp() == DataOpEnum.INSERT || dto.getOp() == DataOpEnum.UPDATE) {
                List<TenantBaseInfoDo> list = new ArrayList<>(Arrays.asList(ObjectConverter.convert(dto, TenantBaseInfoDo.class)));

                // 过滤出：无效状态的数据，进行逻辑删除
//                List<TenantBaseInfoDo> delList = list.stream().
//                        filter(o -> o.getStatus() != null && o.getStatus() == 0).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(delList)) {
//                    List<Long> delIds = delList.stream().map(TenantBaseInfoDo::getTenantId).distinct().collect(Collectors.toList());
//                    tenantBaseInfoService.softDeleteByIds(delIds);
//                    list.removeAll(delList);
//                }

//                if (CollectionUtils.isEmpty(list)) {
//                    return Result.ok(true);
//                }
                tenantBaseInfoService.syncSave(list);
            } else {
                return Result.fail("Op Field value can only be [INSERT UPDATE DELETE]");
            }
        } catch (Exception e) {
            log.error("TenantBaseInfoController.syncTenant error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }

    @PostMapping("/batchSync")
    @ApiOperation("批量同步保存租户信息")
    public Result<Boolean> batchSyncTenant(@RequestBody List<SyncTenantBaseInfoDto> dtos) {
        log.info("batchSyncTenant dtos.size={}", dtos.size());
        try {
            PreCheck.preCheckArgument(CollectionUtils.isEmpty(dtos), "Data is empty");
            long nullCount = dtos.stream().filter(o -> o.getTenantId() == null).count();
            PreCheck.preCheckArgument(nullCount > 0, "租户ID为空");
            long count = dtos.stream().filter(dto -> !(dto.getOp() == DataOpEnum.INSERT || dto.getOp() == DataOpEnum.UPDATE || dto.getOp() == DataOpEnum.DELETE)).count();
            PreCheck.preCheckArgument(count > 0, "Op Field value can only be [INSERT UPDATE DELETE]");

            List<Long> delIds = dtos.stream().filter(o -> o.getOp() == DataOpEnum.DELETE).map(TenantBaseInfoDto::getTenantId).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(delIds)) {
                tenantBaseInfoService.softDeleteByIds(delIds);
            } else {
                dtos.removeIf(o -> o.getOp() == DataOpEnum.DELETE);
                if (CollectionUtils.isEmpty(dtos)) {
                    return Result.ok(true);
                }
                List<TenantBaseInfoDo> list = ObjectConverter.convertList(dtos, TenantBaseInfoDo.class);

                // 过滤出：无效状态的数据，进行逻辑删除
//                List<TenantBaseInfoDo> delList = list.stream().
//                        filter(o -> o.getStatus() != null && o.getStatus() == 0).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(delList)) {
//                    List<Long> delIdList = delList.stream().map(TenantBaseInfoDo::getTenantId).distinct().collect(Collectors.toList());
//                    tenantBaseInfoService.softDeleteByIds(delIdList);
//                    list.removeAll(delList);
//                }
//
//                if (CollectionUtils.isEmpty(list)) {
//                    return Result.ok(true);
//                }
                
                tenantBaseInfoService.syncSave(list);
            }
        } catch (Exception e) {
            log.error("TenantBaseInfoController.batchSyncTenant error msg:{}", e.getMessage(), e);
            return Result.fail(e.getMessage());
        }
        return Result.ok(true);
    }

    @GetMapping("/getTenantList")
    @ApiOperation("根据租户ID查询租户信息")
    public Result<List<TenantBaseInfoVo>> getTenantList(@RequestParam("tenantIds") List<Long> tenantIds) {
        List<TenantBaseInfoDo> list = tenantBaseInfoService.getTenantList(tenantIds);
        if (CollectionUtils.isEmpty(list)) {
            return Result.ok(Lists.newArrayList());
        }
        return Result.ok(ObjectConverter.convertList(list, TenantBaseInfoVo.class));
    }
/*
    @PostMapping("init")
    @ApiOperation("初始化租户以及admin账号")
    public Result<UserInfoDto> init(@RequestBody TenantInitDto tenantInitDto) {
        UserInfoDto userInfo = tenantBaseInfoService.initTenant(tenantInitDto.getTenantId(), tenantInitDto.getTenantName(), tenantInitDto.getCode());
        return Result.ok(userInfo);
    }*/

    @GetMapping("/getLogo")
    @ApiOperation("根据租户ID查询租户信息")
    public Result<String> getLogo(@RequestParam("domain") String domain,
                                  @RequestParam(value = "platform", defaultValue = "pcLogo") String platform) {
        String caiDaoDomain = "dev.shcaidao.com";
        String url = "";
        Map<String, Map<String, String>> tenantLogoMap = nacosValueEntity.getTenant();
        if (tenantLogoMap != null) {
            Map<String, String> map = tenantLogoMap.get(platform);
            url = map.get(domain);
            if (StringUtils.isEmpty(url)) {
                url = map.get(caiDaoDomain);
            }
        }
        return Result.ok(url);
    }

    @PostMapping("init")
    public Result init(){
        tenantBaseInfoService.initAll();
        return Result.ok();
    }
}
