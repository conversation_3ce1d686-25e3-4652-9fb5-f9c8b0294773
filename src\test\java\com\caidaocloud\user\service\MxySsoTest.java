package com.caidaocloud.user.service;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.feign.IHrFeign;
import com.caidaocloud.user.service.application.service.sso.MxySsoService;
import com.caidaocloud.user.service.infrastructure.util.UserContext;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class MxySsoTest {

    @Resource
    private MxySsoService mxySsoService;

    @Before
    public void init() {
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        securityUserInfo.setUserId(0L);
        securityUserInfo.setTenantId("33");
        securityUserInfo.setEmpId(1737530044291073L);
        SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
        UserInfo user = new UserInfo();
        user.setTenantId("33");
        user.setStaffId(0L);
        user.setUserid(0);
        user.setStaffId(1737530044291073L);
        UserContext.setCurrentUser(user);
    }

    @Test
    public void getSSOLink() {
        System.out.println(mxySsoService.getMxySsoLink("pc"));
    }
}
