package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.user.service.application.service.AccountBaseInfoService;
import com.caidaocloud.user.service.application.service.AccountChangePassService;
import com.caidaocloud.user.service.interfaces.dto.ChangePasswordDto;
import com.caidaocloud.user.service.interfaces.dto.FindPasswordDto;
import com.caidaocloud.user.service.interfaces.dto.IdentityVerifyDto;
import com.caidaocloud.user.service.interfaces.dto.ResetPasswordDto;
import com.caidaocloud.user.service.interfaces.vo.AccountBaseInfoVo;
import com.caidaocloud.user.service.interfaces.vo.IdentityVerifyResultVo;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/user/account/v2")
@Api(value = "/api/user/account/v2", tags = "用户中心2.0-账号管理")
public class AccountBaseInfoController {
    @Autowired
    private AccountBaseInfoService accountBaseInfoService;
    @Resource
    private AccountChangePassService accountChangePassService;

    @GetMapping("/getAccountInfo")
    @ApiOperation("查询账号")
    public Result<AccountBaseInfoVo> getAccountInfo(@RequestParam("account") String account) {
        return Result.ok(ObjectConverter.convert(accountBaseInfoService.getAccountInfo(account), AccountBaseInfoVo.class));
    }

    @GetMapping("/getAccountInfoList")
    @ApiOperation("查询账号列表")
    public Result<List<AccountBaseInfoVo>> getAccountInfoList(@RequestParam("account") String account) {
        return Result.ok(ObjectConverter.convertList(accountBaseInfoService.getAccountInfoList(account), AccountBaseInfoVo.class));
    }

    @PostMapping("pass/verify")
    @ApiOperation("验证身份")
    public Result<IdentityVerifyResultVo> verify(@RequestBody IdentityVerifyDto verify) {
        IdentityVerifyResultVo resultVo = accountChangePassService.verify(verify);
        return Result.ok(resultVo);
    }

    @PostMapping("pass/change")
    @ApiOperation("修改密码")
    public Result changePass(@RequestBody ChangePasswordDto changePasswordDto) {
        accountChangePassService.changePass(changePasswordDto);
        return Result.ok();
    }
    
    @PostMapping("pass/first_login_change")
    @ApiOperation("修改密码")
    public Result changePassForFirstLogin(@RequestBody ChangePasswordDto changePasswordDto) {
        String msg = accountChangePassService.changePassForFirstLogin(changePasswordDto);
        if(StringUtil.isNotEmpty(msg))
            return Result.success(msg);
        return Result.ok();
    }
    
    @PostMapping("pass/reset")
    @ApiOperation("重设密码（发起入职）")
    public Result resetPass(@RequestParam("userId") Long userId,
                            @RequestParam("accountId")Long accountId) {
        String newPwd = accountChangePassService.resetPass(userId, accountId);
        return Result.ok(newPwd);
    }

    @ApiOperation("重置密码")
    @PostMapping("/reset/password")
    public Result<?> resetPwdCommon(@RequestBody ResetPasswordDto dto) {
        accountChangePassService.resetPasswordCommon(dto);
        return Result.ok(true);
    }

    @PostMapping("/findPwd/verify")
    @ApiOperation("验证短信验证码")
    public Result<IdentityVerifyResultVo> verifySmsCode(@RequestBody IdentityVerifyDto dto) {
        IdentityVerifyResultVo resultVo = accountChangePassService.verify(dto);
        return Result.ok(resultVo);
    }

    @PostMapping("/findPwd/reset")
    @ApiOperation("找回密码重新设置")
    public Result<?> findPwdReset(@RequestBody FindPasswordDto dto) {
        accountChangePassService.findPasswordReset(dto);
        return Result.ok(true);
    }

}
