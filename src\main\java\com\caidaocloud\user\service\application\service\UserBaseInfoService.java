
package com.caidaocloud.user.service.application.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.FilterElement;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.message.sdk.service.MsgNoticeService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.user.service.application.dto.SysEmpInfoDto;
import com.caidaocloud.user.service.application.dto.auth.AuthSubjectAndRoleDto;
import com.caidaocloud.user.service.application.dto.auth.AuthSubjectRoleDto;
import com.caidaocloud.user.service.application.dto.operate.AuthImportDto;
import com.caidaocloud.user.service.application.dto.operate.AuthImportMsgDto;
import com.caidaocloud.user.service.application.dto.preemp.PreEmpUserDto;
import com.caidaocloud.user.service.application.enums.AccountStatusEnum;
import com.caidaocloud.user.service.application.event.publish.UserEventPublish;
import com.caidaocloud.user.service.application.event.publish.enums.UserEventType;
import com.caidaocloud.user.service.application.event.publish.message.UserEventMsg;
import com.caidaocloud.user.service.application.feign.IAuthFeign;
import com.caidaocloud.user.service.application.feign.IBaseInfoClient;
import com.caidaocloud.user.service.application.utils.PasswordHelper;
import com.caidaocloud.user.service.application.utils.PropertyUtil;
import com.caidaocloud.user.service.domain.entity.AccountBaseInfoDo;
import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.repository.IAccountBaseInfoRepository;
import com.caidaocloud.user.service.domain.repository.ISysEmpInfoRepository;
import com.caidaocloud.user.service.domain.repository.IUserBaseInfoRepository;
import com.caidaocloud.user.service.domain.service.AccountBaseInfoDomainService;
import com.caidaocloud.user.service.domain.service.PwdRuleDomainService;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import com.caidaocloud.user.service.domain.util.ListUtil;
import com.caidaocloud.user.service.infrastructure.util.UserContext;
import com.caidaocloud.user.service.interfaces.dto.SyncOnBoarDingDto;
import com.caidaocloud.user.service.interfaces.dto.UserAccountInfoDto;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoDto;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoQueryDto;
import com.caidaocloud.user.service.interfaces.dto.UserBaseInfoQueryParam;
import com.caidaocloud.user.service.interfaces.vo.UserDetailInfoVo;
import com.caidaocloud.util.BeanUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.ObjectUtil;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.Result;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.totallylazy.Sequences;
import com.googlecode.totallylazy.Strings;

import lombok.val;
import lombok.var;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.nullness.qual.Nullable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class UserBaseInfoService {

    @Resource
    private PreEmpUserService preEmpUserService;

    @Resource
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Resource
    private IUserBaseInfoRepository userBaseInfoRepository;

    @Resource
    private AccountBaseInfoDomainService accountBaseInfoDomainService;

    @Resource
    private IBaseInfoClient baseInfoClient;

    @Resource
    private ISysEmpInfoRepository sysEmpInfoRepository;

    @Resource
    private IAuthFeign authFeign;

    @Resource
    private PwdRuleDomainService pwdRuleDomainService;

    @Resource
    private PasswordHelper passwordHelper;

    @Resource
    private ISessionService sessionService;

    @Resource
    private IAccountBaseInfoRepository accountBaseInfoRepository;

    @Resource
    private UserAppService userAppService;
    @Resource
    private MsgNoticeService msgNoticeService;
    @Resource
    private CacheService cacheService;


    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);

    @Transactional
    public void syncSave(List<UserAccountInfoDto> dtos) throws Exception {
        if (CollectionUtils.isEmpty(dtos)) {
            log.info("UserBaseInfoService.syncSave UserAccountInfoDtoList is empty,time={} ", System.currentTimeMillis());
            return;
        }
        List<Long> userIds = dtos.stream().map(UserAccountInfoDto::getUserId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            log.info("UserBaseInfoService.syncSave userIds is empty,time={} ", System.currentTimeMillis());
            return;
        }
        Map<Long, AccountBaseInfoDo> oldAccountMap = null;
        Map<Long, Long> oldUserAccountRelMap = null;
        // 根据用户ID查询用户信息
        List<UserBaseInfoDo> oldUserList = userBaseInfoDomainService.getByUserIds(userIds);
        if (!CollectionUtils.isEmpty(oldUserList)) {
            oldUserAccountRelMap = oldUserList.stream().collect(Collectors.toMap(UserBaseInfoDo::getUserId, UserBaseInfoDo::getAccountId));
            // 账号查询
            List<Long> accountIds = oldUserList.stream().map(UserBaseInfoDo::getAccountId).distinct().collect(Collectors.toList());
            List<AccountBaseInfoDo> accountList = accountBaseInfoDomainService.getListByIds(accountIds);
            oldAccountMap = accountList.stream().collect(Collectors.toMap(AccountBaseInfoDo::getAccountId, Function.identity()));
        }
        // 查询租户下所有的角色信息
        List<String> allRoleNames = null;
        Result<List<KeyValue>> allEnabledRole = authFeign.getAllEnabledRole();
        if (allEnabledRole.isSuccess() && !CollectionUtils.isEmpty(allEnabledRole.getData())) {
            List<KeyValue> allEnabledRoleData = allEnabledRole.getData();
            allRoleNames = allEnabledRoleData.stream().map(KeyValue::getText).distinct().collect(Collectors.toList());
        }
        // 用户角色授权
        List<AuthImportDto> authToSubjectList = Lists.newArrayList();
        List<Long> authAdminSubjects = Lists.newArrayList();
        // 用户数据遍历
        List<UserBaseInfoDo> userList = new ArrayList<>();
        for (UserAccountInfoDto dto : dtos) {
            // 账号信息保存
            boolean syncAuth = false;
            AccountBaseInfoDo account = ObjectConverter.convert(dto, AccountBaseInfoDo.class);
            if (oldUserAccountRelMap != null && oldUserAccountRelMap.containsKey(dto.getUserId()) &&
                    oldAccountMap.containsKey(oldUserAccountRelMap.get(dto.getUserId()))) {
                AccountBaseInfoDo oldAccount = oldAccountMap.get(oldUserAccountRelMap.get(dto.getUserId()));
                account.setAccountId(oldAccount.getAccountId());
            } else {
                syncAuth = dto.isSyncAuth() && dto.isAdmin();
            }
            // 根据dto account查询accountId
            if (account.getAccountId() == null) {
                // 查询user以及accountId
                List<UserBaseInfoDo> userBaseInfoDos = userBaseInfoDomainService.getUserListByAccount(dto.getAccount());
                userIds.addAll(userBaseInfoDos.stream().map(UserBaseInfoDo::getUserId).collect(Collectors.toList()));
                account.setAccountId(CollectionUtils.isEmpty(userBaseInfoDos) ? null :
                        userBaseInfoDos.get(0).getAccountId());
            }
            // 转换要保存account信息
            Long accountId = accountBaseInfoDomainService.syncSave(account);
            // 用户信息保存
            UserBaseInfoDo user = ObjectConverter.convert(dto, UserBaseInfoDo.class);
            user.setAccountId(accountId);
            pwdRuleDomainService.cleanErrorCountCache(user.getAccountId());
            userList.add(user);

            // 授权管理员角色
            if (syncAuth) {
                authAdminSubjects.add(user.getUserId());
            }
            // 用户角色授权
            if (!syncAuth && !CollectionUtils.isEmpty(allRoleNames)
                    && (!CollectionUtils.isEmpty(dto.getRoleNames()) || StringUtils.isNotBlank(dto.getDefaultRoleName()))) {
                // check role
                List<String> effectiveRoleNames = dto.getRoleNames().stream()
                        .filter(allRoleNames::contains).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(effectiveRoleNames)) {
                    var authImportDto = new AuthImportDto();
                    authImportDto.setSubjectId(user.getUserId());
                    authImportDto.setRoleName(StringUtils.join(effectiveRoleNames, ","));
                    authToSubjectList.add(authImportDto);
                } else if (StringUtils.isNotBlank(dto.getDefaultRoleName())) {
                    // 设置了默认角色
                    Optional<String> deaultRoleNameOptional = allRoleNames.stream()
                            .filter(o -> o.equals(dto.getDefaultRoleName())).collect(Collectors.toList()).stream().findFirst();
                    if (deaultRoleNameOptional.isPresent()) {
                        var authImportDto = new AuthImportDto();
                        authImportDto.setSubjectId(user.getUserId());
                        authImportDto.setRoleName(deaultRoleNameOptional.get());
                        authToSubjectList.add(authImportDto);
                    }
                }
            }
        }
        // 删除历史用户账号
        userBaseInfoDomainService.deleteByIds(userIds);
        // 保存用户
        userBaseInfoDomainService.syncSave(userList);

        // 保存更新管理员权限
        if (!CollectionUtils.isEmpty(authAdminSubjects)) {
            List<Long> authSubjects = userList.stream().filter(it -> authAdminSubjects.contains(it.getUserId())).map(UserBaseInfoDo::getUserId)
                    .collect(Collectors.toList());
            if (!authSubjects.isEmpty()) {
                authFeign.authAdminSubjects(authSubjects);
            }
        }

        // 其他角色授权
        if (!CollectionUtils.isEmpty(authToSubjectList)) {
            Result<List<AuthImportMsgDto>> result = authFeign.authorizationToUser(authToSubjectList);
            log.warn("user auth resut={}", result.isSuccess());
        }
    }

    @Transactional
    public List<String> syncOnBoarDingSave(List<SyncOnBoarDingDto> dtoList) throws Exception {
        List<String> failedMobiles = new ArrayList<>();
        UserInfo userInfo = getUserInfo();
        Long currentTime = System.currentTimeMillis();
        String defaultImportPassword = PropertyUtil.getProperty("caidaocloud.import.password:Cd1524");
        log.info("defaultPassword: " + defaultImportPassword);

        // 候选人数据遍历
        List<List<SyncOnBoarDingDto>> lists = ListUtil.split(dtoList, 500);

        // 批量处理 每次500条数据
        for (List<SyncOnBoarDingDto> dtos : lists) {
            List<AccountBaseInfoDo> insBaseInfoList = new ArrayList<>();
            List<UserBaseInfoDo> insUserInfoList = new ArrayList<>();
            List<String> mobNums = dtos.stream().map(SyncOnBoarDingDto::getMobNum).distinct().collect(Collectors.toList());

            // 通过候选人根据手机号进行校验该租户下是否存在该候选人（非取消入职状态的候选人）和在职员工
            List<AccountBaseInfoDo> updList = accountBaseInfoDomainService.getAccountByMobNums(mobNums);
            if (!CollectionUtils.isEmpty(updList)) {
                List<UserBaseInfoDo> userInfoList = userBaseInfoDomainService.getTenantUserListByAccountIds(Sequences.sequence(updList)
                        .map(AccountBaseInfoDo::getAccountId).toList());
                Map<Long, UserBaseInfoDo> userMap = userInfoList.stream()
                        .collect(Collectors.toMap(UserBaseInfoDo::getAccountId, obj -> obj, (a, b) -> a));
                Map<String, AccountBaseInfoDo> accountMap = updList.stream()
                        .collect(Collectors.toMap(AccountBaseInfoDo::getMobNum, obj -> obj, (a, b) -> a));
                // 手机号存在则更新
                List<UserBaseInfoDo> userList = new ArrayList<>();
                List<AccountBaseInfoDo> accountList = new ArrayList<>();

                Iterator<SyncOnBoarDingDto> iterator = dtos.iterator();
                while (iterator.hasNext()) {
                    SyncOnBoarDingDto onBoarDing = iterator.next();

                    // 账号是否存在
                    AccountBaseInfoDo account;
                    if ((account = accountMap.get(onBoarDing.getMobNum())) == null) {
                        continue;
                    }
                    iterator.remove();

                    // 用户是否存在
                    UserBaseInfoDo existUser = userMap.get(account.getAccountId());
                    if (existUser == null) {
                        // 用户insert
                        insUserInfoList.add(createUserBaseInfo(onBoarDing, account.getAccountId(), currentTime, userInfo.getUserId()));
                    } else {
                        // 存在非候选人账号
                        if (!existUser.getOnboarding()) {
                            failedMobiles.add(onBoarDing.getMobNum());
                            continue;
                        }
                        // 用户update
                        onBoarDing.setAccountId(account.getAccountId());
                        UserBaseInfoDo user = ObjectConverter.convert(onBoarDing, UserBaseInfoDo.class);
                        user.setUserId(existUser.getUserId());
                        user.setUpdateBy(userInfo.getUserId());
                        user.setUpdateTime(currentTime);
                        userList.add(user);
                    }
                    // 账号update
                    AccountBaseInfoDo acc = ObjectConverter.convert(onBoarDing, AccountBaseInfoDo.class);
                    acc.setUpdateBy(userInfo.getUserId());
                    acc.setUpdateTime(currentTime);
                    accountList.add(acc);
                }
            /*    for (AccountBaseInfoDo upd : updList) {
                    for (SyncOnBoarDingDto onBoarDing : dtos){
                        if(upd.getMobNum().equals(onBoarDing.getMobNum())){
                            onBoarDing.setAccountId(upd.getAccountId());
                            UserBaseInfoDo user = ObjectConverter.convert(onBoarDing, UserBaseInfoDo.class);
                            user.setUpdateBy(userInfo.getUserId());
                            user.setUpdateTime(currentTime);
                            userList.add(user);

                            AccountBaseInfoDo acc = ObjectConverter.convert(onBoarDing, AccountBaseInfoDo.class);
                            acc.setUpdateBy(userInfo.getUserId());
                            acc.setUpdateTime(currentTime);
                            accountList.add(acc);
                        }
                    }
                }*/
                userBaseInfoDomainService.syncUpdate(userList);
                accountBaseInfoDomainService.syncUpdate(accountList);
            }

        /*    // 剔除更新数据
            dtos = dtos.stream().filter(account ->
                    !updList.stream().anyMatch(upd ->upd.getMobNum().equals(account.getMobNum())))
                    .collect(Collectors.toList());*/

            // 不存在,创建候选人账号
            log.info("syncOnBoarDingSave userInfo:{}", FastjsonUtil.toJson(userInfo));
            for (SyncOnBoarDingDto dto : dtos) {
                // 不存在,创建候选人账号
                String salt = passwordHelper.createSalt();
                String password = passwordHelper.encode(defaultImportPassword, salt);
                log.info("createAccount phone = {} name = {} empId = {}  salt = {} password = {} ", dto.getMobNum(), dto.getUserName(), dto.getEmpId(), salt, password);
                AccountBaseInfoDo accountBaseInfoDo = createAccount(dto, password, salt, currentTime, userInfo.getUserId());
                long accountId = snowflakeUtil.createId();
                accountBaseInfoDo.setAccountId(accountId);
                insBaseInfoList.add(accountBaseInfoDo);

                insUserInfoList.add(createUserBaseInfo(dto, accountId, currentTime, userInfo.getUserId()));
            }
            accountBaseInfoDomainService.syncSave(insBaseInfoList);
            userBaseInfoDomainService.syncSave(insUserInfoList);
        }
        return failedMobiles;
    }

    private AccountBaseInfoDo createAccount(SyncOnBoarDingDto dto, String password, String salt, Long currentTime, Long userId) {
        AccountBaseInfoDo account = new AccountBaseInfoDo();
        account.setMobNum(dto.getMobNum());
        account.setAccount(dto.getMobNum());
        if (StringUtils.isNotBlank(dto.getAccount())) {
            account.setAccount(dto.getAccount());
        }
        account.setPassword(password);
        account.setSalt(salt);
        account.setStatus(1);
        account.setCreateBy(userId);
        account.setCreateTime(currentTime);
        return account;
    }

    public UserBaseInfoDo createUserBaseInfo(SyncOnBoarDingDto dto, Long accountId, Long currentTime, Long userId) {
        UserBaseInfoDo userInfoDto = new UserBaseInfoDo();
        userInfoDto.setAccountId(accountId);
        userInfoDto.setAccount(dto.getMobNum());
        if (StringUtils.isNotBlank(dto.getAccount())) {
            userInfoDto.setAccount(dto.getAccount());
        }
        userInfoDto.setEmpId(dto.getEmpId());
        userInfoDto.setUserName(dto.getUserName());
        userInfoDto.setTenantId(dto.getTenantId());
        userInfoDto.setStatus(1);
        userInfoDto.setIfDefault(false);
        userInfoDto.setOnboarding(true);
        userInfoDto.setCreateBy(userId);
        userInfoDto.setCreateTime(currentTime);
        userInfoDto.setCorpId(dto.getTenantId());
        return userInfoDto;
    }


    public UserInfo getUserInfo() {
        return UserContext.preCheckUser();
    }


    public Long saveOrUpdateUserBaseInfo(UserBaseInfoDto userBaseInfoDto) {
        UserBaseInfoDo userBaseInfoDo = FastjsonUtil.convertObject(userBaseInfoDto, UserBaseInfoDo.class);
        return userBaseInfoDomainService.saveOrUpdateUser(userBaseInfoDo);
    }

    /**
     * 根据账号查询用户信息
     *
     * @param account
     * @return
     */
    public List<UserBaseInfoDo> getUserListByAccount(String account) {
        return userBaseInfoDomainService.getUserListByAccount(account);
    }

    public List<UserBaseInfoDo> getNormalUserListByAccount(String account) {
        return userBaseInfoDomainService.getNormalUserListByAccount(account);
    }

    public UserBaseInfoDo getByUserId(Long userId) {
        Optional<UserBaseInfoDo> optional = userBaseInfoDomainService.getByUserId(userId);
        return optional.orElse(null);
    }

    public List<UserBaseInfoDo> getByUserIds(List<Long> userIds) {
        List<UserBaseInfoDo> list = userBaseInfoDomainService.getByUserIds(userIds);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list;
    }

    public UserDetailInfoVo detail(Long accountId) {
        UserBaseInfoQueryDto queryDto = new UserBaseInfoQueryDto();
        queryDto.setAccountId(accountId);
        queryDto.setOnboarding(false);
        PageResult<UserDetailInfoVo> page = page(queryDto);
        if (CollectionUtils.isEmpty(page.getItems())) {
            return new UserDetailInfoVo();
        }
        return page.getItems().get(0);
    }

    /**
     * 1860 添加 修改方法 修改 角色信息 启用状态；
     *
     * @param dto
     */

    @Transactional(rollbackFor = Exception.class)
    public Result userBaseUpdate(UserBaseInfoDto dto) {
        List<UserBaseInfoDo> userByAccountId = getUserByAccountId(dto.getAccountId());
        AccountBaseInfoDo accountByAccountId = accountBaseInfoDomainService.getAccountByAccountId(dto.getAccountId());
        if (userByAccountId.isEmpty() || ObjectUtil.isEmpty(accountByAccountId)) {
            return Result.fail("用户信息错误");
        }

        //caidao 1860 只支持 状态 所属角色 编辑；
        UserBaseInfoDo userBaseInfoDo = userByAccountId.get(0);
        checkRole(userBaseInfoDo.getUserId());
        userBaseInfoDo.setStatus(dto.getStatus());
        userBaseInfoDo.setUpdateBy(getUserInfo()!=null?getUserInfo().getUserId():0l);
        userBaseInfoDomainService.saveOrUpdateUser(userBaseInfoDo);
        accountByAccountId.setStatus(dto.getStatus());
        accountByAccountId.setUpdateBy(getUserInfo()!=null?getUserInfo().getUserId():0l);
        accountBaseInfoDomainService.updateById(accountByAccountId);

        //只修改 状态 不需要 传递角色信息；

        if (!CollectionUtils.isEmpty(dto.getRoleIds())) {
            //编辑时候 携带 角色信息；
            AuthSubjectRoleDto authSubjectRoleDto = new AuthSubjectRoleDto();
            authSubjectRoleDto.setSubjectId(String.valueOf(userBaseInfoDo.getUserId()));
            authSubjectRoleDto.setRoleIds(dto.getRoleIds());
            //所属角色编辑；
            Result result = authFeign.empChangeRole(authSubjectRoleDto);
            if (!result.isSuccess()) {
                throw new ServerException(result.getMsg());
            }
        }
        return Result.ok();
    }

    public void checkRole(Long userId){
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        if (userInfo.getRole().contains("ADMIN")||!userInfo.getRole().contains("CONFIG")) {
            return;
        }
        Result<List<AuthSubjectAndRoleDto>> result = authFeign.getRoleNameBySubjectIds(String.valueOf(userId));
        if (!result.isSuccess()) {
            throw new ServerException("获取用户角色失败");
        }
        for (AuthSubjectAndRoleDto dto : result.getData()) {
            if (dto.getCodes().contains("ADMIN") || dto.getCodes().contains("CONFIG")) {
                throw new ServerException("不可调整配置管理员的权限");
            }
        }
    }

    /**
     * 1860 添加 新增方法；
     */
    @Transactional(rollbackFor = Exception.class)
    public Result save(PreEmpUserDto preEmpUser) {
        String tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        if (StringUtil.isEmpty(preEmpUser.getMobileNo())) {
            return Result.fail("用户手机号缺失");
        }
        //account 冲突不允许
        //删除 只删 user  不删 account 判断 user

        //DEV-18019  账户新增校验为 当前租户下时候已有对应用户；
        List<UserBaseInfoDo> userListByAccount = userBaseInfoDomainService.getUserListByAccount(preEmpUser.getMobileNo());
        if (!CollectionUtils.isEmpty(userListByAccount)) {
            List<UserBaseInfoDo> collect = userListByAccount.stream().filter(userBaseInfoDo -> userBaseInfoDo.getTenantId() == Long.valueOf(tenantId)).collect(Collectors.toList());
            if (!collect.isEmpty()){
                return Result.fail("用户 手机号:" + preEmpUser.getMobileNo() + " 已存在");
            }
        }

        UserBaseInfoDto empUser;
        AccountBaseInfoDo accountInfo = accountBaseInfoDomainService.getAccountInfo(preEmpUser.getMobileNo());
        Map<String, String> extMap = Maps.newHashMap();
        log.info("accountInfo: {}", FastjsonUtil.toJson(accountInfo));
        if (ObjectUtil.isEmpty(accountInfo)) {
            accountInfo = preEmpUserService.createAccount(preEmpUser);
            empUser = preEmpUserService.createEmpUser(preEmpUser, accountInfo);
            //给新密码 发通知邮件
            String newPwd = PasswordHelper.PasswordGenerator.generatePassword(6);
            accountBaseInfoDomainService.changePassword(newPwd.trim(), empUser.getUserId(), accountInfo);
            extMap.put("empId", String.valueOf(preEmpUser.getEmpId()));
            extMap.put("onboarding.account.password", newPwd);
            extMap.put("onboarding.account.password.jp", newPwd);
            extMap.put("onboarding.account", empUser.getAccount());

            String key = String.format("change_pwd_account:%s", accountInfo.getAccountId());
            cacheService.cacheObject(key, 1L, -1L);
        } else {
            empUser = preEmpUserService.createEmpUser(preEmpUser, accountInfo);
            // 已有账户使用原密码
            byte[] cnBytes = "沿用入职登录密码".getBytes(StandardCharsets.UTF_8);
            String cnStr = new String(cnBytes, StandardCharsets.UTF_8);
            byte[] jpBytes = "同入社パスワード".getBytes(StandardCharsets.UTF_8);
            String jpStr = new String(cnBytes, StandardCharsets.UTF_8);
            extMap.put("empId", String.valueOf(preEmpUser.getEmpId()));
            extMap.put("onboarding.account.password", cnStr);
            extMap.put("onboarding.account.password.jp", jpStr);
            extMap.put("onboarding.account", empUser.getAccount());
        }
        //生成随机密码 并 发送消息
        if (StringUtil.isNotEmpty(accountInfo.getEmail())) {
            List<String> subjects = Lists.newArrayList(String.valueOf(preEmpUser.getEmpId()));
            msgNoticeService.sendMsgNoticeEvent(NoticeType.EMPLOYEE_ACCOUNT_CREATE, subjects, extMap, "user", 0);
        }
        AuthSubjectRoleDto authSubjectRoleDto = new AuthSubjectRoleDto();
        authSubjectRoleDto.setSubjectId(String.valueOf(empUser.getUserId()));
        authSubjectRoleDto.setRoleIds(preEmpUser.getRoleIds());
        //所属角色新增
        Result result = authFeign.empChangeRole(authSubjectRoleDto);
        if (!result.isSuccess()) {
            throw new ServerException(result.getMsg());
        }
        return Result.ok();
    }

    public PageResult<UserDetailInfoVo> page(UserBaseInfoQueryDto queryDto) {

        if (!CollectionUtils.isEmpty(queryDto.getFilters())) {
            for (FilterElement filterElement : queryDto.getFilters()) {
                if ("mobile".equals(filterElement.getProp())) {
                    queryDto.setMobile(String.valueOf(filterElement.getValue()));
                }
                if ("email".equals(filterElement.getProp())) {
                    queryDto.setEmail(String.valueOf(filterElement.getValue()));
                }
                if ("status".equals(filterElement.getProp()) && filterElement.getValue() != null) {
                    String[] strings = String.valueOf(filterElement.getValue()).split(",");
                    Integer[] statusArray = Stream.of(strings).mapToInt(Integer::parseInt).boxed().toArray(Integer[]::new);
                    queryDto.setStatus(Arrays.asList(statusArray));
                }
            }
        }

        //工号，邮箱
        if (StringUtils.isNotBlank(queryDto.getWorkNo()) || StringUtils.isNotBlank(queryDto.getEmail())) {
            List<SysEmpInfoDto> empInfoByWorkno = sysEmpInfoRepository.getEmpInfoByWorknoAndEmail(queryDto.getWorkNo(), queryDto.getEmail());
            if (!CollectionUtils.isEmpty(empInfoByWorkno)) {
                log.info("empInfoByWorkno size ={}", FastjsonUtil.toJson(empInfoByWorkno.size()));
                List<Long> empIds = empInfoByWorkno.stream().map(SysEmpInfoDto::getEmpid).collect(Collectors.toList());
                log.info("empInfoByWorkno empIds ={}", FastjsonUtil.toJson(empIds));
                queryDto.setEmpIds(empIds);
            } else {
                return new PageResult();
            }
        }
        //手机号
        if (StringUtils.isNotBlank(queryDto.getMobile())) {
            List<AccountBaseInfoDo> accountBaseInfoDos = accountBaseInfoDomainService.getAccountByMobNums(Lists.newArrayList(queryDto.getMobile()));
            if (!CollectionUtils.isEmpty(accountBaseInfoDos)) {
                queryDto.setAccountId(accountBaseInfoDos.get(0).getAccountId());
            } else {
                return new PageResult();
            }
        }
        val page = userBaseInfoDomainService.page(queryDto);

        log.info("UserBaseInfoQueryDto page data = [{}]", FastjsonUtil.toJson(page));
        List<UserBaseInfoDo> list = page.getRecords();
        val empIdList = list.stream().map(it -> it.getEmpId()).filter(it -> null != it).collect(Collectors.toList());
        List<SysEmpInfoDto> empList = Lists.newArrayList();
        if (!empIdList.isEmpty()) {
            List<SysEmpInfoDto> empInfoResult = sysEmpInfoRepository.getEmpInfoByEmpIds(StringUtils.join(empIdList, ","));
            empList.addAll(empInfoResult);
        }
        List<UserDetailInfoVo> voList = FastjsonUtil.convertList(list, UserDetailInfoVo.class);
        if (!CollectionUtils.isEmpty(voList)) {
            val accountList = accountBaseInfoDomainService.getListByIds(list.stream().map(it -> it.getAccountId()).collect(Collectors.toList()));
            String empIds = voList.stream().filter(e -> null != e.getUserId())
                    .map(e -> e.getUserId().toString()).distinct().collect(Collectors.joining(","));
            List<AuthSubjectAndRoleDto> authSubjectAndRoleDtoList = null;
            if (StringUtils.isNotBlank(empIds)) {
                Result<List<AuthSubjectAndRoleDto>> result = authFeign.getRoleNameBySubjectIds(empIds);
                if (result.isSuccess()) {
                    authSubjectAndRoleDtoList = result.getData();
                }
            }

            Map<Long, String> roleNamesMap = null;
            Map<Long, String> roleIdsMap = null;
            if (!CollectionUtils.isEmpty(authSubjectAndRoleDtoList)) {
                roleNamesMap = authSubjectAndRoleDtoList.stream().collect(Collectors.toMap(AuthSubjectAndRoleDto::getSubjectId,
                        AuthSubjectAndRoleDto::getRoleNames, (v1, v2) -> v1));
                roleIdsMap = authSubjectAndRoleDtoList.stream().collect(Collectors.toMap(AuthSubjectAndRoleDto::getSubjectId,
                        AuthSubjectAndRoleDto::getRoleIds, (v1, v2) -> v1));
            }

            Map<Long, String> finalRoleNamesMap = roleNamesMap;
            Map<Long, String> finalRoleIdsMap = roleIdsMap;
            voList.forEach(it -> {
                if (finalRoleNamesMap != null) {
                    //角色名称去重
                    String roleNames = finalRoleNamesMap.get(it.getUserId());
                    if (StringUtil.isNotBlank(roleNames)) {
                        String distinctRoleNames = Arrays.stream(roleNames.split(",")).distinct().collect(Collectors.joining(","));
                        it.setRoleNames(distinctRoleNames);
                    }
                    String roleIds = finalRoleIdsMap.get(it.getUserId());
                    List<String> roleIdList;
                    if (StringUtil.isNotEmpty(roleIds)) {
                        try {
                            roleIdList = Strings.split(",").call(roleIds)
                                    .filter(e -> StringUtils.isNotBlank(e)).toList();
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                        //去重
                        roleIdList = roleIdList.stream().distinct().collect(Collectors.toList());
                        it.setRoleIds(roleIdList);
                    }
                }
                empList.stream().filter(emp -> emp.getEmpid().equals(it.getEmpId()))
                        .findFirst().ifPresent(emp -> {
                            it.setWorkno(emp.getWorkno());
                            //                        it.setMobile(emp.getMobile());
                            it.setOrganizeTxt(emp.getOrganizeTxt());
                            it.setEmail(emp.getEmail());
                        });
                accountList.stream().filter(account -> account.getAccountId().toString().equals(it.getAccountId()))
                        .findFirst().ifPresent(account -> {
                            it.setMobile(account.getMobNum());
//                            it.setEmail(account.getEmail());
                        });
            });
        }
        return new PageResult(voList, (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal());
    }

    public List<UserBaseInfoDo> queryListByMobilesAndEmails(UserBaseInfoQueryParam queryParam) {

        List<AccountBaseInfoDo> accountBaseInfoDos = accountBaseInfoDomainService.getAccountByMobNumOrEmail(queryParam.getMobiles(), queryParam.getEmails());
        if (CollectionUtils.isEmpty(accountBaseInfoDos)) {
            return Lists.newArrayList();
        }
        return userBaseInfoDomainService.getTenantUserListByAccountIds(Sequences.sequence(accountBaseInfoDos)
                .map(AccountBaseInfoDo::getAccountId).toList());

    }


    public PageResult<UserDetailInfoVo> page(String keywords, int pageNo, int pageSize) {
        val page = userBaseInfoDomainService.page(keywords, pageNo, pageSize, false);
        log.info("page data = [{}]", FastjsonUtil.toJson(page));
        val list = page.getRecords();
        val empIdList = list.stream().map(it -> it.getEmpId()).filter(it -> null != it).collect(Collectors.toList());
        List<SysEmpInfoDto> empList = Lists.newArrayList();
        if (!empIdList.isEmpty()) {
            List<SysEmpInfoDto> empInfoResult = sysEmpInfoRepository.getEmpInfoByEmpIds(StringUtils.join(empIdList, ","));
            empList.addAll(empInfoResult);
        }
        val voList = FastjsonUtil.convertList(list, UserDetailInfoVo.class);
        if (!CollectionUtils.isEmpty(voList)) {
            val accountList = accountBaseInfoDomainService.getListByIds(list.stream().map(it -> it.getAccountId()).collect(Collectors.toList()));

            String empIds = Joiner.on(",").join(Sequences.sequence(voList).map(e -> e.getUserId()).toList());
            List<AuthSubjectAndRoleDto> authSubjectAndRoleDtoList = null;
            if (StringUtils.isNotBlank(empIds)) {
                Result<List<AuthSubjectAndRoleDto>> result = authFeign.getRoleNameBySubjectIds(empIds);
                if (result.isSuccess()) {
                    authSubjectAndRoleDtoList = result.getData();
                }
            }

            Map<Long, String> roleNamesMap = null;
            if (!CollectionUtils.isEmpty(authSubjectAndRoleDtoList)) {
                roleNamesMap = authSubjectAndRoleDtoList.stream().collect(Collectors.toMap(AuthSubjectAndRoleDto::getSubjectId,
                        AuthSubjectAndRoleDto::getRoleNames, (v1, v2) -> v1));
            }

            Map<Long, String> finalRoleNamesMap = roleNamesMap;
            voList.forEach(it -> {
                if (finalRoleNamesMap != null) {
                    it.setRoleNames(finalRoleNamesMap.get(it.getUserId()));
                }
                empList.stream().filter(emp -> emp.getEmpid().equals(it.getEmpId()))
                        .findFirst().ifPresent(emp -> {
                            it.setWorkno(emp.getWorkno());
                            //                        it.setMobile(emp.getMobile());
                            it.setOrganizeTxt(emp.getOrganizeTxt());
                            //                        it.setEmail(emp.getEmail());
                        });
                accountList.stream().filter(account -> account.getAccountId().toString().equals(it.getAccountId()))
                        .findFirst().ifPresent(account -> {
                            it.setMobile(account.getMobNum());
                            it.setEmail(account.getEmail());
                        });
            });
        }
        return new PageResult(voList, (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal());
    }

    @Autowired
    private UserEventPublish userEventPublish;

    /**
     * 删除账号，触发消息广播通知
     *
     * @param userIds
     */
    public void deleteByUserIds(String userIds) {
        Result deleteSubjects = authFeign.deleteSubjects(userIds);
        if (!deleteSubjects.isSuccess()) {
            throw new ServerException(deleteSubjects.getMsg());
        }
        int result = userBaseInfoDomainService.deleteByIds(
                Arrays.stream(userIds.split(",")).map(Long::valueOf).collect(Collectors.toList()));
        if (result > 0) {
            List<UserBaseInfoDo> baseInfoDoList = userBaseInfoDomainService.getByUserIds(Arrays.stream(userIds.split(","))
                    .map(Long::valueOf).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(baseInfoDoList)) {
                baseInfoDoList.forEach(it -> {
                    try {
                        UserEventMsg itemMsg = new UserEventMsg();
                        itemMsg.setUserBaseInfoDo(it);
                        itemMsg.setType(UserEventType.DELETE.getValue());
                        userEventPublish.publishUserEventFanout(itemMsg);
                    } catch (Exception e) {
                        log.info("send user event msg failed: {}", e.getMessage());
                    }
                });
            }
        }
    }

    @Transactional
    public void deleteByEmpId(String empId, String tenantId) {
        List<UserBaseInfoDo> userBaseInfoDoList = userBaseInfoDomainService.listByEmpId(Long.valueOf(empId), Long.valueOf(tenantId));
        if (!CollectionUtils.isEmpty(userBaseInfoDoList)) {
            userBaseInfoDomainService.deleteByIds(userBaseInfoDoList.stream().map(UserBaseInfoDo::getUserId).collect(Collectors.toList()));
        }
    }

    @Transactional
    public void deleteByIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        List<UserBaseInfoDo> userList = userBaseInfoDomainService.getByUserIds(userIds);
        if (!CollectionUtils.isEmpty(userList)) {
            List<Long> accountIds = userList.stream().map(UserBaseInfoDo::getAccountId).distinct().collect(Collectors.toList());
            accountBaseInfoDomainService.softDeleteByIds(accountIds);
        }
        userBaseInfoDomainService.softDeleteByIds(userIds);
    }

    public void updateDingUserIdByEmpId(Long empId, String dingUserId) {

        userBaseInfoDomainService.updateDingUserIdByEmpId(empId, dingUserId);
    }

    public List<UserBaseInfoDo> listByEmpId(Long empId) {
        return userBaseInfoDomainService.listByEmpId(empId);
    }

    public List<UserBaseInfoDo> listByEmpIds(List<Long> empIds) {
        return userBaseInfoDomainService.listByEmpIds(empIds);
    }

    public PageResult<UserDetailInfoVo> onBoarDingPage(String keywords, int pageNo, int pageSize) {
        val page = userBaseInfoDomainService.page(keywords, pageNo, pageSize, true);
        val list = page.getRecords();
        val voList = FastjsonUtil.convertList(list, UserDetailInfoVo.class);
        if (list.isEmpty()) {
            return new PageResult(voList, (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal());
        }

//        List<SysEmpInfoDto> empList = Lists.newArrayList();
//        val empIdList = list.stream().map(it -> it.getEmpId()).filter(it -> null != it).collect(Collectors.toList());
//        if (!empIdList.isEmpty()) {
//            val empInfoResult = baseInfoClient.getEmpInfoByEmpIds(StringUtils.join(empIdList, ","));
//            if (!empInfoResult.isSuccess()) {
//                throw new ServerException("获取员工信息失败");
//            }
//            empList.addAll(empInfoResult.getData());
//        }

        val accountList = accountBaseInfoDomainService.getListByIds(list.stream().map(it -> it.getAccountId()).collect(Collectors.toList()));
        voList.forEach(it -> {
            // 根据accountId获取邮箱和手机号
            accountList.stream().filter(account -> String.valueOf(account.getAccountId()).equals(it.getAccountId()))
                    .findFirst().ifPresent(account -> {
                        it.setMobile(account.getMobNum());
                        it.setEmail(account.getEmail());
                    });
//            empList.stream().filter(emp -> emp.getEmpid().equals(it.getEmpId()))
//                    .findFirst().ifPresent(emp -> {
//                it.setWorkno(emp.getWorkno());
//                it.setOrganizeTxt(emp.getOrganizeTxt());
//            });
        });
        return new PageResult(voList, (int) page.getCurrent(), (int) page.getSize(), (int) page.getTotal());
    }

    public void syncOfficialUpd(List<SyncOnBoarDingDto> dtoList) throws Exception {
        UserInfo userInfo = getUserInfo();
        Long currentTime = System.currentTimeMillis();

        // 候选人数据遍历
        List<List<SyncOnBoarDingDto>> lists = ListUtil.split(dtoList, 500);

        // 批量处理 每次500条数据
        for (List<SyncOnBoarDingDto> dtos : lists) {
            List<String> mobNums = dtos.stream().map(SyncOnBoarDingDto::getMobNum).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(mobNums)) {
                // 通过候选人根据手机号和邮箱进行校验该租户下是否存在该候选人
                List<AccountBaseInfoDo> updList = accountBaseInfoDomainService.getAccountByMobNums(mobNums);
                if (!CollectionUtils.isEmpty(updList)) {
                    // 候选人用户更新为正常员工用户
                    List<UserBaseInfoDo> userList = userBaseInfoDomainService.getTenantUserListByAccountIds(Sequences.sequence(updList)
                            .map(AccountBaseInfoDo::getAccountId).toList());
                    Map<Long, UserBaseInfoDo> userMap = userList.stream()
                            .collect(Collectors.toMap(UserBaseInfoDo::getAccountId, obj -> obj, (a, b) -> a));
                    Map<String, AccountBaseInfoDo> accountMap = updList.stream()
                            .collect(Collectors.toMap(AccountBaseInfoDo::getMobNum, obj -> obj, (a, b) -> a));

                    List<UserBaseInfoDo> candicateList = new ArrayList<>();
                    for (SyncOnBoarDingDto onBoarDing : dtos) {
                        AccountBaseInfoDo account = accountMap.get(onBoarDing.getMobNum());
                        UserBaseInfoDo exist = userMap.get(account.getAccountId());
                        if (exist.getOnboarding() != null && exist.getOnboarding()) {
                            BeanUtil.copyWithNoValue(onBoarDing, exist);
                            exist.setUpdateBy(userInfo.getUserId());
                            exist.setUpdateTime(currentTime);
                            exist.setOnboarding(false);
                            candicateList.add(exist);
                        }
                    }
                    // List<UserBaseInfoDo> userList = new ArrayList<>();
                    // for (AccountBaseInfoDo upd : updList) {
                    //     for (SyncOnBoarDingDto onBoarDing : dtos){
                    //         if(upd.getMobNum().equals(onBoarDing.getMobNum())){
                    //             onBoarDing.setAccountId(upd.getAccountId());
                    //             UserBaseInfoDo user = ObjectConverter.convert(onBoarDing, UserBaseInfoDo.class);
                    //             user.setOnboarding(false);
                    //             user.setUpdateBy(userInfo.getUserId());
                    //             user.setUpdateTime(currentTime);
                    //             userList.add(user);
                    //         }
                    //     }
                    // }
                    userBaseInfoDomainService.syncUpdate(candicateList);
                }
            }
        }
    }

    public void softDeleteByEmpId(String empId) {
        userBaseInfoDomainService.softDeleteByEmpId(empId);
    }

    public List<UserBaseInfoDo> getUserByAccountId(Long accountId) {
        return userBaseInfoDomainService.getUserListByAccountId(accountId);
    }

    /**
     * 根据empId查询对应的用户信息
     *
     * @param empId
     * @return
     */
    public UserBaseInfoDo getByEmpId(Long empId) {
        List<UserBaseInfoDo> userList = listByEmpId(empId);
        return userList == null || userList.isEmpty() ? null : userList.get(0);
    }

    public void stop(String empId) {
        UserBaseInfoDo emp = getByEmpId(Long.valueOf(empId));
        if (!ObjectUtil.isEmpty(emp) && null != emp.getStatus() && emp.getStatus() == 1) {
            //停用员工信息
            emp.setStatus(2);
            userBaseInfoRepository.update(emp);
        }
    }


    /**
     * 解锁员工账号
     *
     * @param empId 员工ID
     */
    public void unlock(String empId) {
        UserBaseInfoDo emp = getByEmpId(Long.valueOf(empId));
        if (!ObjectUtil.isEmpty(emp) && null != emp.getStatus() && emp.getStatus() == AccountStatusEnum.LOCK.getIndex()) {
            // 解锁员工账号
            emp.setStatus(AccountStatusEnum.NORMAL.getIndex());
            userBaseInfoRepository.update(emp);
            // 清除错误次数缓存
            if (emp.getAccountId() != null) {
                pwdRuleDomainService.cleanErrorCountCache(emp.getAccountId());
            }
        }
    }

    /**
     * 修改候选人信息 手机号/名称
     *
     * @param empId
     * @param name
     * @param phone
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result userUpdatePrivateInfo(String empId, String name, String phone) {
        List<UserBaseInfoDo> userBaseInfoDos = userBaseInfoDomainService.listByEmpIdAndOnboarding(Long.valueOf(empId), true);
        if (!userBaseInfoDos.isEmpty()) {
            for (UserBaseInfoDo userBaseInfoDo : userBaseInfoDos) {
                Long accountId = userBaseInfoDo.getAccountId();
                AccountBaseInfoDo accountBaseInfoDo = accountBaseInfoDomainService.getAccountByAccountId(accountId);
                if(accountBaseInfoDo == null) {
                    log.info("not found account, accountId={}", accountId);
                    continue;
                }
                if (StringUtil.isNotEmpty(name)) {
                    userBaseInfoDo.setUserName(name);
                }
                if (StringUtil.isNotEmpty(phone)) {
                    userBaseInfoDo.setAccount(phone);
                    accountBaseInfoDo.setAccount(phone);
                    accountBaseInfoDo.setMobNum(phone);
                }
                userBaseInfoRepository.update(userBaseInfoDo);
                accountBaseInfoRepository.update(accountBaseInfoDo);
            }
        }
        return Result.ok();
    }


}
