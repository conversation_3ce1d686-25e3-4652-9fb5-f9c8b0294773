package com.caidaocloud.user.service.application.service.impl;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.user.service.application.utils.CacheKeyDefine;
import com.caidaocloud.user.service.application.enums.SmsCodeType;
import com.caidaocloud.user.service.application.service.ISmsCodeCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SmsCodeCacheService implements ISmsCodeCacheService {
    @Autowired
    private CacheService cacheService;

    @Override
    public String generateSmsCode() {
        return String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
    }

    @Override
    public void saveSmsCode(String mobile, String code, long expireTime, SmsCodeType smsCodeType) {
        String key = getSmsCacheKey(mobile, smsCodeType);
        cacheService.cacheValue(key, code, expireTime);
    }

    private String getSmsCacheKey(String mobile, SmsCodeType smsCodeType) {
        String key;
        switch (smsCodeType) {
            case LOGIN:
                key = CacheKeyDefine.SMS_LOGIN_KEY_ + mobile;
                break;
            case REGISTER:
                key = CacheKeyDefine.SMS_REGISTER_KEY_ + mobile;
                break;
            case RETRIEVE_PASSWORD:
                key = CacheKeyDefine.SMS_RETRIEVE_PASSWORD_KEY_ + mobile;
                break;
            case RESET_PASSWORD:
                key = CacheKeyDefine.SMS_RESET_PASSWORD_KEY_ + mobile;
                break;
            default:
                key = "null";
                break;
        }
        return key;
    }

    @Override
    public String getSmsCode(String mobile, SmsCodeType smsCodeType) {
        String key = getSmsCacheKey(mobile, smsCodeType);
        if (!cacheService.containsKey(key)) {
            return null;
        }
        return cacheService.getValue(key);
    }

    @Override
    public void removeSmsCode(String mobile, SmsCodeType smsCodeType) {
        String key = getSmsCacheKey(mobile, smsCodeType);
        if (!cacheService.containsKey(key)) {
            return;
        }
        cacheService.remove(key);
    }
}
