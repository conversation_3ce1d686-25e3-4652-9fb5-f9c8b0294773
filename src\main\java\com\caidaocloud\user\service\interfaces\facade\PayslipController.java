package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.record.core.util.WebUtil;
import com.caidaocloud.user.service.application.dto.UserAccountBaseInfoDto;
import com.caidaocloud.user.service.application.dto.UserAccountPayslipInfoDto;
import com.caidaocloud.user.service.application.service.ReceiptTokenService;
import com.caidaocloud.user.service.application.service.UserPayslipService;
import com.caidaocloud.user.service.application.service.login.IAccountLoginService;
import com.caidaocloud.user.service.application.service.sso.AuthorizeService;
import com.caidaocloud.user.service.interfaces.dto.*;
import com.caidaocloud.user.service.interfaces.granter.ITokenGranter;
import com.caidaocloud.user.service.interfaces.granter.TokenGranterBuilder;
import com.caidaocloud.user.service.interfaces.vo.*;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @Author: Daniel
 * @Desc: 工资单密码
 */
@Slf4j
@RestController
@RequestMapping("/api/user/ps")
public class PayslipController {
    @Resource
    private UserPayslipService userPayslipService;
    @Resource
    private AuthorizeService authorizeService;
    @Resource
    private ReceiptTokenService receiptTokenService;

    @ApiOperation(value = "查询用户和账号基本信息", produces = "application/json")
    @RequestMapping(value = "/v1/getUserAndAccountInfo", method = RequestMethod.GET)
    public Result<UserAccountPayslipInfoVo> getUserAndAccountInfo(@RequestParam("userId") Long userId) {
        UserAccountPayslipInfoDto userDto = userPayslipService.getUserAccountByUserId(userId);
        return ResponseWrap.wrapResult(ObjectConverter.convert(userDto, UserAccountPayslipInfoVo.class));
    }

    @ApiOperation(value = "账户登录/获取用户列表", produces = "application/json")
    @RequestMapping(value = "/v1/account/login", method = RequestMethod.POST)
    @LogRecordAnnotation(
            success = "通过账号密码登录",
            menu = "{{#platform}}-登录",
            condition = "{{#condition}}",
            category = "工资单登入",
            operator = "{{#userId}}",
            tenantId = "{{#tenantId}}"
    )
    public Result<AccountLoginVo> accountLogin(@RequestBody AccountLoginDto loginDto, HttpServletRequest request) {
        AccountLoginVo grant = IAccountLoginService.getInstance(loginDto.getGrantType()).grant(loginDto);
        LogRecordContext.putVariable("userId", grant.getUserId());
        LogRecordContext.putVariable("platform", WebUtil.getOperatorPlatform(request));
        LogRecordContext.putVariable("condition", true);
        return Result.ok(grant);
    }

    @ApiOperation(value = "修改密码")
    @PostMapping(value = "/v1/account/changePassword")
    public Result changePassword(@RequestBody ChangePasswordDto dto) {
        userPayslipService.changePassword(dto);
        return Result.ok(true);
    }

    @ApiOperation(value = "修改手势密码")
    @PostMapping(value = "/v1/account/changeGesture")
    public Result changeGesture(@RequestBody ChangePasswordDto dto) {
        userPayslipService.changeGesture(dto);
        return Result.ok(true);
    }
}
