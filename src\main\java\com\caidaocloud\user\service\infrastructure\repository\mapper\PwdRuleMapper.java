
package com.caidaocloud.user.service.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.caidaocloud.user.service.infrastructure.repository.po.PwdRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PwdRuleMapper extends BaseMapper<PwdRule> {
    int insertSelective(PwdRule record);

    int updateByPrimaryKeySelective(PwdRule record);

    int insertBatch(@Param("records") List<PwdRule> records);
}
