package com.caidaocloud.user.service.interfaces.facade;

import com.caidaocloud.user.service.application.service.user.MenuService;
import com.caidaocloud.web.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/user/menu/v1")
@Api(value = "用户菜单", description = "用户菜单接口", tags = "用户菜单接口")
public class MenuController {
    @Resource
    private MenuService menuService;

    @ApiOperation(value = "获取用户菜单", produces = "application/json")
    @GetMapping(value = "/codeList")
    public Result<List<String>> getMenuCodeList() {
        return Result.ok(menuService.getMenuCodeList());
    }
}
