package com.caidaocloud.user.service.infrastructure.util;

import java.io.IOException;
import java.net.URLEncoder;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;

/**
 *
 * <AUTHOR>
 * @date 2024/11/6
 */
public class ExcelUtil {
	/**
	 * excel下载
	 * @param fileName 下载时的文件名称
	 * @param response
	 * @param workbook excel数据
	 */
	public static void downLoadExcel(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
		try {
			response.setCharacterEncoding("UTF-8");
			response.setHeader("content-Type", "application/vnd.ms-excel");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xls", "UTF-8"));
			workbook.write(response.getOutputStream());
		} catch (Exception e) {
			throw new IOException(e.getMessage(), e);
		}
	}

	public static void downLoadSXSSFExcel(String fileName, HttpServletResponse response, Workbook workbook) throws IOException {
		try {
			response.setCharacterEncoding("UTF-8");
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
			response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xls", "UTF-8"));
			workbook.write(response.getOutputStream());
		} catch (Exception e) {
			throw new IOException(e.getMessage(), e);
		}
	}
}
