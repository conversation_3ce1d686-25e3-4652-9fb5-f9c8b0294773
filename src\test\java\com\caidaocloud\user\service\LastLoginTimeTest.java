package com.caidaocloud.user.service;

import com.caidaocloud.user.service.domain.entity.UserBaseInfoDo;
import com.caidaocloud.user.service.domain.service.UserBaseInfoDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

/**
 * 最后登录时间功能测试
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-10
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class LastLoginTimeTest {

    @Autowired
    private UserBaseInfoDomainService userBaseInfoDomainService;

    @Test
    public void testUpdateLastLoginTime() {
        try {
            // 创建测试用户
            UserBaseInfoDo testUser = new UserBaseInfoDo();
            testUser.setAccount("test_lastlogin_user");
            testUser.setUserName("测试用户");
            testUser.setTenantId(1L);
            testUser.setAccountId(1001L);
            testUser.setStatus(1);
            testUser.setCreateTime(System.currentTimeMillis());
            testUser.setCreateBy(0L);
            
            // 保存用户
            Long userId = userBaseInfoDomainService.saveOrUpdateUser(testUser);
            log.info("创建测试用户，userId: {}", userId);
            
            // 获取用户信息，验证lastLoginTime为空
            Optional<UserBaseInfoDo> userOptional = userBaseInfoDomainService.getByUserId(userId);
            if (userOptional.isPresent()) {
                UserBaseInfoDo user = userOptional.get();
                log.info("用户创建后的lastLoginTime: {}", user.getLastLoginTime());
                
                // 更新最后登录时间
                userBaseInfoDomainService.updateLastLoginTime(userId);
                log.info("已更新用户最后登录时间");
                
                // 再次获取用户信息，验证lastLoginTime已更新
                Optional<UserBaseInfoDo> updatedUserOptional = userBaseInfoDomainService.getByUserId(userId);
                if (updatedUserOptional.isPresent()) {
                    UserBaseInfoDo updatedUser = updatedUserOptional.get();
                    log.info("更新后的lastLoginTime: {}", updatedUser.getLastLoginTime());
                    
                    if (updatedUser.getLastLoginTime() != null && updatedUser.getLastLoginTime() > 0) {
                        log.info("✓ 最后登录时间更新成功");
                    } else {
                        log.error("✗ 最后登录时间更新失败");
                    }
                } else {
                    log.error("✗ 无法获取更新后的用户信息");
                }
            } else {
                log.error("✗ 无法获取创建的用户信息");
            }
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    public void testLastLoginTimeInUserInfo() {
        try {
            // 测试用户信息中是否包含lastLoginTime
            Long testUserId = 1L;
            Optional<UserBaseInfoDo> userOptional = userBaseInfoDomainService.getByUserId(testUserId);
            
            if (userOptional.isPresent()) {
                UserBaseInfoDo user = userOptional.get();
                log.info("用户ID: {}, 用户名: {}, 最后登录时间: {}", 
                    user.getUserId(), user.getUserName(), user.getLastLoginTime());
                
                if (user.getLastLoginTime() != null) {
                    log.info("✓ 用户信息中包含最后登录时间");
                } else {
                    log.info("用户信息中最后登录时间为空");
                }
            } else {
                log.info("未找到用户ID为 {} 的用户", testUserId);
            }
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    @Test
    public void testMultipleLoginTimeUpdates() {
        try {
            // 创建测试用户
            UserBaseInfoDo testUser = new UserBaseInfoDo();
            testUser.setAccount("test_multiple_login_user");
            testUser.setUserName("多次登录测试用户");
            testUser.setTenantId(1L);
            testUser.setAccountId(1002L);
            testUser.setStatus(1);
            testUser.setCreateTime(System.currentTimeMillis());
            testUser.setCreateBy(0L);
            
            // 保存用户
            Long userId = userBaseInfoDomainService.saveOrUpdateUser(testUser);
            log.info("创建测试用户，userId: {}", userId);
            
            // 模拟多次登录
            for (int i = 1; i <= 3; i++) {
                Thread.sleep(1000); // 等待1秒确保时间戳不同
                
                userBaseInfoDomainService.updateLastLoginTime(userId);
                log.info("第{}次登录时间更新完成", i);
                
                Optional<UserBaseInfoDo> userOptional = userBaseInfoDomainService.getByUserId(userId);
                if (userOptional.isPresent()) {
                    UserBaseInfoDo user = userOptional.get();
                    log.info("第{}次登录后的lastLoginTime: {}", i, user.getLastLoginTime());
                }
            }
            
            log.info("✓ 多次登录时间更新测试完成");
            
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }
}
